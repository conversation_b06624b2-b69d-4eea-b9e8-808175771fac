name: Update and Deploy on EC2

on:
  push:
    branches:
      - dev

jobs:
  deploy-on-ec2:
    name: SSH and Docker Compose Deployment
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the repository
      - name: Checkout
        uses: actions/checkout@v2

      # Step 2: Execute Remote SSH Commands
      - name: Remote SSH Commands
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGE_SERVER_PUB_IP }}
          username: azureuser
          key: ${{ secrets.STAGE_SERVER_KEY }}
          port: 22
          script: |
            cd fintuition-backend/

            # Stop existing Docker containers
            make down-stage
            
            # git checkout
            git checkout dev
            
            # Pull the latest changes from the repository
            git pull origin dev
            
            # Build with staging configuration
            make build-stage

            # Start Docker containers with staging configuration
            make up-stage

            # Apply database migrations
            make migrate-deploy-stage
            
            # Clean up unused Docker images, containers, and volumes
            docker system prune --force
