import request from "supertest";
import { app } from "../../src/index.js";
import { expect } from "chai";

import { getToken ,getcompanyId} from "../helpers/companyHelpers.js";
const baseUrl = `/api/v1/company`;

describe("Delete Company API", function () {
  let authToken;
    let companyId;
  
    before(async function () {
      authToken = await getToken();
      companyId = await getcompanyId()
    });

  it("should return 404 if company does not exist", async function () {
    const res = await request(app)
      .delete(`${baseUrl}/9999999`) // Assuming this company ID doesn't exist
      .set("Authorization", `Bearer ${authToken}`);
    expect(res.status).to.equal(200);
    expect(res._body.success).to.be.false;
    expect(res._body.statusCode).to.equal(404);
    expect(res.body.message).to.contain(
      "Company not found"
    );
  });
  it("should return 400 if invalid company ID is provided", async function () {
    const res = await request(app)
      .delete(`${baseUrl}/invalidId`)
      .set("Authorization", `Bearer ${authToken}`);
    
    expect(res.status).to.equal(200);
    expect(res._body.success).to.be.false;
    expect(res._body.statusCode).to.equal(400);
  });
  it("should delete company successfully", async function () {
    const res = await request(app)
      .delete(`${baseUrl}/${companyId}`)
      .set("Authorization", `Bearer ${authToken}`);
      
    expect(res.status).to.equal(200);
    expect(res._body.success).to.be.true;
    expect(res.body.message).to.contain("deleted");
  });
  
});
