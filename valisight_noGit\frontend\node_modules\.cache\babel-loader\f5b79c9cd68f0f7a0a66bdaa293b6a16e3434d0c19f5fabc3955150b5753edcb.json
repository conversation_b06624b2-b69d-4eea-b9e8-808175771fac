{"ast": null, "code": "\"use strict\";\n\nexports.formatLong = void 0;\nvar _index = require(\"../../_lib/buildFormatLongFn.js\");\nconst dateFormats = {\n  full: \"EEEE, MMMM do, y\",\n  long: \"MMMM do, y\",\n  medium: \"MMM d, y\",\n  short: \"MM/dd/yyyy\"\n};\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\",\n  long: \"h:mm:ss a z\",\n  medium: \"h:mm:ss a\",\n  short: \"h:mm a\"\n};\nconst dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\"\n};\nconst formatLong = exports.formatLong = {\n  date: (0, _index.buildFormatLongFn)({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: (0, _index.buildFormatLongFn)({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: (0, _index.buildFormatLongFn)({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};", "map": {"version": 3, "names": ["exports", "formatLong", "_index", "require", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "date", "buildFormatLongFn", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/locale/en-US/_lib/formatLong.js"], "sourcesContent": ["\"use strict\";\nexports.formatLong = void 0;\nvar _index = require(\"../../_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n  full: \"EEEE, MMMM do, y\",\n  long: \"MMMM do, y\",\n  medium: \"MMM d, y\",\n  short: \"MM/dd/yyyy\",\n};\n\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\",\n  long: \"h:mm:ss a z\",\n  medium: \"h:mm:ss a\",\n  short: \"h:mm a\",\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\",\n};\n\nconst formatLong = (exports.formatLong = {\n  date: (0, _index.buildFormatLongFn)({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: (0, _index.buildFormatLongFn)({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: (0, _index.buildFormatLongFn)({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n});\n"], "mappings": "AAAA,YAAY;;AACZA,OAAO,CAACC,UAAU,GAAG,KAAK,CAAC;AAC3B,IAAIC,MAAM,GAAGC,OAAO,CAAC,iCAAiC,CAAC;AAEvD,MAAMC,WAAW,GAAG;EAClBC,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,YAAY;EAClBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAG;EAClBJ,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE;AACT,CAAC;AAED,MAAME,eAAe,GAAG;EACtBL,IAAI,EAAE,wBAAwB;EAC9BC,IAAI,EAAE,wBAAwB;EAC9BC,MAAM,EAAE,oBAAoB;EAC5BC,KAAK,EAAE;AACT,CAAC;AAED,MAAMP,UAAU,GAAID,OAAO,CAACC,UAAU,GAAG;EACvCU,IAAI,EAAE,CAAC,CAAC,EAAET,MAAM,CAACU,iBAAiB,EAAE;IAClCC,OAAO,EAAET,WAAW;IACpBU,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,IAAI,EAAE,CAAC,CAAC,EAAEb,MAAM,CAACU,iBAAiB,EAAE;IAClCC,OAAO,EAAEJ,WAAW;IACpBK,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFE,QAAQ,EAAE,CAAC,CAAC,EAAEd,MAAM,CAACU,iBAAiB,EAAE;IACtCC,OAAO,EAAEH,eAAe;IACxBI,YAAY,EAAE;EAChB,CAAC;AACH,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}