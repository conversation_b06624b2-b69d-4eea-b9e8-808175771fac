{"ast": null, "code": "/**\n * @license lucide-react v0.469.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst BriefcaseMedical = createLucideIcon(\"BriefcaseMedical\", [[\"path\", {\n  d: \"M12 11v4\",\n  key: \"a6ujw6\"\n}], [\"path\", {\n  d: \"M14 13h-4\",\n  key: \"1pl8zg\"\n}], [\"path\", {\n  d: \"M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2\",\n  key: \"1ksdt3\"\n}], [\"path\", {\n  d: \"M18 6v14\",\n  key: \"1mu4gy\"\n}], [\"path\", {\n  d: \"M6 6v14\",\n  key: \"1s15cj\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"6\",\n  rx: \"2\",\n  key: \"i6l2r4\"\n}]]);\nexport { BriefcaseMedical as default };", "map": {"version": 3, "names": ["BriefcaseMedical", "createLucideIcon", "d", "key", "width", "height", "x", "y", "rx"], "sources": ["C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\node_modules\\lucide-react\\src\\icons\\briefcase-medical.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BriefcaseMedical\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTF2NCIgLz4KICA8cGF0aCBkPSJNMTQgMTNoLTQiIC8+CiAgPHBhdGggZD0iTTE2IDZWNGEyIDIgMCAwIDAtMi0yaC00YTIgMiAwIDAgMC0yIDJ2MiIgLz4KICA8cGF0aCBkPSJNMTggNnYxNCIgLz4KICA8cGF0aCBkPSJNNiA2djE0IiAvPgogIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNCIgeD0iMiIgeT0iNiIgcng9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/briefcase-medical\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BriefcaseMedical = createLucideIcon('BriefcaseMedical', [\n  ['path', { d: 'M12 11v4', key: 'a6ujw6' }],\n  ['path', { d: 'M14 13h-4', key: '1pl8zg' }],\n  ['path', { d: 'M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2', key: '1ksdt3' }],\n  ['path', { d: 'M18 6v14', key: '1mu4gy' }],\n  ['path', { d: 'M6 6v14', key: '1s15cj' }],\n  ['rect', { width: '20', height: '14', x: '2', y: '6', rx: '2', key: 'i6l2r4' }],\n]);\n\nexport default BriefcaseMedical;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,gBAAA,GAAmBC,gBAAA,CAAiB,kBAAoB,GAC5D,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}