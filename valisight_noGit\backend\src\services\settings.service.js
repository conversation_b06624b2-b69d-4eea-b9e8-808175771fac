
import { prisma } from "../db/prisma.js"
import { HttpStatusCode } from "../enums/error.enum.js";

export const defaultUsersList = async (loggedInUserId, page, pageSize, search, isUserAssociation, userIds, ownerCompanyId) => {
    try {
        const skip = (page - 1) * pageSize;

        // Get users who were shared *with the logged-in user*, not globally
        const sharedWithCurrentUser = await prisma.defaultUserSettings.findMany({
            where: { sharedBy: loggedInUserId, deleted: false },
            select: { sharedWith: true }
        });

        // Get company owner userId
        const ownerCompany = await prisma.company.findFirst({
            where: { id: ownerCompanyId },
            select: { userId: true },
        });

        // Build excluded user IDs
        const excludedUserIds = [
            ...(isUserAssociation ? userIds : sharedWithCurrentUser?.map(item => item.sharedWith) || []),
            ...(isUserAssociation && ownerCompany?.userId ? [ownerCompany.userId] : []),
            ...(ownerCompany?.userId ? [] : [loggedInUserId])
        ];


        const where = {
            isAdmin: false,
            id: { notIn: excludedUserIds }, // Exclude only for this user, not globally
            // NOT: { id: loggedInUserId }, // Don't show the logged-in user in the list
            OR: [
                { username: { contains: search, mode: 'insensitive' } },
                { email: { contains: search, mode: 'insensitive' } }
            ],
        };

        const [users, totalUsers] = await Promise.all([
            prisma.user.findMany({
                where,
                select: { id: true, username: true, email: true, createdAt: true },
                skip,
                take: pageSize
            }),
            prisma.user.count({ where })
        ]);

        return {
            success: true,
            statusCode: 200,
            users,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(totalUsers / pageSize),
                totalUsers,
                pageSize,
            },
        };
    } catch (err) {
        console.log("Error in defaultUsersList", err);
        return {
            success: false,
            statusCode: 500,
            message: err || 'Error fetching users',
        };
    }
};

export const addUserToShareCompanyWith = async (shareBy, body) => {
    try {
        const { shareWith, username } = body;


        if (!shareWith) {
            return {
                success: false,
                statusCode: HttpStatusCode.BAD_REQUEST,
                message: 'Invalid input',
            };
        }

        const userAdded = await prisma.defaultUserSettings.create({
            data: {
                username,
                sharedBy: shareBy,
                sharedWith: shareWith,
            }
        })


        let userCompanies = await prisma.company.findMany({
            where: {
                OR: [
                    {
                        sharedCompanies: {
                            some: {
                                userId: shareBy
                            }
                        },
                        NOT: {
                            userId: shareWith
                        }
                    },
                    {
                        userId: shareBy
                    }
                ]
            },
            select: {
                id: true
            }
        });

        let companyiesId = userCompanies.map((item) => item.id)

        const sharedCompaniesPayload = companyiesId.map((item) => (
            {
                userId: shareWith,
                companyId: item
            })
        )

        // gave access to user
        await prisma.sharedCompanies.createMany({
            data: sharedCompaniesPayload,
            skipDuplicates: true
        })

        return {
            success: true,
            statusCode: HttpStatusCode.OK,
            user: userAdded,
        };
    } catch (err) {
        console.log("error in addUserToShareCompanyWith", err);
        return {
            success: false,
            statusCode: 500,
            message: err || 'Error adding user',
        };
    }
}

export const getSharedUsersList = async (userId) => {
    try {
        const sharedUsers = await prisma.defaultUserSettings.findMany({
            where: {
                sharedBy: userId,
                deleted: false
            },
            include: {
                sharedByUser: {
                    select: {
                        id: true,
                        username: true,
                        email: true
                    }
                },
                sharedWithUser: {
                    select: {
                        id: true,
                        username: true,
                        email: true
                    }
                }
            }
        });

        return {
            success: true,
            statusCode: HttpStatusCode.OK,
            users: sharedUsers,
        };
    } catch (err) {
        console.log("error in getSharedUsersList", err);
        return {
            success: false,
            statusCode: 500,
            message: err || 'Error fetching shared users',
        };
    }
}

export const revokeAccessForSharedUser = async (sharedBy, id) => {
    try {
        await prisma.defaultUserSettings.delete({
            where: { id, sharedBy },
        });

        return {
            success: true,
            statusCode: HttpStatusCode.OK,
            message: 'Shared user deleted successfully',
        };
    } catch (err) {
        console.log("error in revokeAccessForSharedUser", err);
        return {
            success: false,
            statusCode: 500,
            message: err || 'Error deleting shared user',
        };
    }
}