// backend\src\controllers\contentSettings.controller.js
import * as contentSettingsService from '../services/contentSettings.service.js';

export const getAllContentSettings = async (req, res) => {
  try {
    res.json(await contentSettingsService.getAll(req));
  } catch (error) {
    console.error(error.message);
    const { status } = error;
    res.status(status || 500).json({
      success: false,
      statusCode: status || 500,
      message: error.message,
    });
  }
};

export const getContentSettingsByReportType = async (req, res) => {
  try {
    res.json(await contentSettingsService.getByReportType(req));
  } catch (error) {
    console.error('contentSettingsService error: ', error);
    const { status } = error;
    res.status(status || 500).json({
      success: false,
      statusCode: status || 500,
      message: error.message,
    });
  }
};

export const updateContentSettings = async (req, res) => {
  try {
    res.json(await contentSettingsService.updateByReportType(req));
  } catch (error) {
    console.error(error.message);
    const { status } = error;
    res.status(status || 500).json({
      success: false,
      statusCode: status || 500,
      message: error.message,
    });
  }
};
