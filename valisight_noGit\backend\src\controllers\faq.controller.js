
import * as faqService from '../services/faq.service.js';
import { faqsValidation } from '../validations/faqs.validation.js';

export const createFaq = async (req, res) => {
    try {
        let {question,answer} = req.body
        
        // add validation
        const { error } = faqsValidation.createFaqs.validate(req.body);
        if (error) {
            throw {
            status: 200,
            success: false,
            statusCode: 400,
            message: error.details[0].message,
            };
        }
        
        res.json(await faqService.createFaq(req.body));
    } catch (error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: false,
            statusCode: error.statusCode || 500,
            message: error.message,
        });
    }
}


export const getFaqs = async (req, res) => {
    try {
        res.json(await faqService.getFaqs());
    } catch (error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: false,
            statusCode: error.statusCode || 500,
            message: error.message,
        });
    }
}

export const deleteFaq = async (req, res) => {
    try {
        // Validate and split the IDs into an array
        if (!req.params.ids) {
            return res.status(400).json({
                success: false,
                message: 'No IDs provided. Please include IDs in the URL params.',
            });
        }
        res.json(await faqService.deleteFaq(req.params.ids));
    } catch (error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: false,
            statusCode: error.statusCode || 500,
            message: error.message,
        });
    }
}

export const updateFaq = async (req, res) => {
    try {
        res.json(await faqService.updateFaq(req.params.id, req.body));
    } catch (error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: false,
            statusCode: error.statusCode || 500,
            message: error.message,
        });
    }
}