{"name": "fintuiton", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "npx mocha 'tests/**/*.test.js' --timeout 10000", "dev": "nodemon node src/index.js", "migrate": "npx prisma migrate dev", "start": "node src/index.js", "build": "echo \"No build step required (example).\"", "studio": "npx prisma studio", "migration:generate": "npx prisma migrate dev", "migration:run": "npx prisma migrate deploy", "seed:templates": "node seeds/template-settings.seed.js", "seed:all": "npm run seed:templates"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.717.0", "@aws-sdk/s3-request-presigner": "^3.717.0", "@prisma/client": "^6.10.1", "@sendgrid/mail": "^8.1.4", "archiver": "^7.0.1", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "chai": "^5.2.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "file-type": "^19.6.0", "fs": "^0.0.1-security", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.16", "nodemon": "^3.1.9", "playwright": "^1.54.1", "prettier": "^3.4.2", "supertest": "^7.0.0", "uuid": "^11.1.0"}, "devDependencies": {"prisma": "^6.10.1"}}