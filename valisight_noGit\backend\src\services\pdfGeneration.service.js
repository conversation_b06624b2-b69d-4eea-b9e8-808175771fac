import { chromium } from 'playwright';
import { createSuccessResponse, createErrorResponse } from '../utils/response.js';
import { HttpStatusCode } from '../enums/error.enum.js';

export const generateReportPDF = async (htmlContent, options = {}) => {
  let browser;

  try {
    // Launch browser
    browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Set viewport for consistent rendering
    await page.setViewportSize({ width: 1200, height: 800 });

    // Set content with proper styling
    await page.setContent(htmlContent, {
      waitUntil: 'networkidle',
      timeout: 30000
    });

    // Generate PDF with A4 size and portrait orientation
    const pdfBuffer = await page.pdf({
      format: 'A4',
      orientation: 'portrait',
      printBackground: true,
      margin: {
        top: '0.1in',    // Small top margin to prevent content overlap
        right: '0in',
        bottom: '0.75in', // Bottom margin to accommodate footer
        left: '0in'
      },
      displayHeaderFooter: true,
      headerTemplate: '<div></div>', // Empty header template
      footerTemplate: `
        <div style="
          font-size: 10px; 
          width: 100%; 
          text-align: center; 
          color: #666;
          margin: 0 auto;
          padding: 5px 0;
        ">
          <span class="pageNumber"></span>
        </div>
      `,
      ...options
    });

    // Convert to base64
    const base64PDF = pdfBuffer.toString('base64');

    return createSuccessResponse(
      HttpStatusCode.OK,
      'PDF generated successfully',
      {
        pdf: base64PDF,
        size: pdfBuffer.length
      }
    );

  } catch (error) {
    console.error('PDF Generation Error:', error);
    return createErrorResponse(
      HttpStatusCode.INTERNAL_SERVER_ERROR,
      'Failed to generate PDF',
      error.message
    );
  } finally {
    if (browser) {
      await browser.close();
    }
  }
};

// export const generateCustomReportPDF = async (reportData, templateSettings) => {
//   try {
//     // Create HTML content for the specific components
//     const htmlContent = generateReportHTML(reportData, templateSettings);

//     // Generate PDF with custom options
//     const result = await generateReportPDF(htmlContent, {
//       format: 'A4',
//       orientation: 'portrait',
//       printBackground: true,
//       margin: {
//         top: '0in',
//         right: '0in',
//         bottom: '0in',
//         left: '0in'
//       },
//       preferCSSPageSize: true
//     });

//     return result;

//   } catch (error) {
//     console.error('Custom Report PDF Generation Error:', error);
//     return createErrorResponse(
//       HttpStatusCode.INTERNAL_SERVER_ERROR,
//       'Failed to generate custom report PDF',
//       error.message
//     );
//   }
// };

// const generateReportHTML = (reportData, templateSettings) => {
//   // Basic HTML structure with Tailwind CSS for PDF generation
//   return `
//     <!DOCTYPE html>
//     <html lang="en">
//     <head>
//       <meta charset="UTF-8">
//       <meta name="viewport" content="width=device-width, initial-scale=1.0">
//       <title>Custom Report</title>
//       <script src="https://cdn.tailwindcss.com"></script>
//       <style>
//         @page {
//           size: A4 portrait;
//           margin: 0in;
//         }

//         body {
//           font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
//           line-height: 1.6;
//           color: #374151;
//           background: white;
//         }

//         .page-break {
//           page-break-before: always;
//         }

//         .no-break {
//           page-break-inside: avoid;
//         }

//         .header-repeat {
//           display: table-header-group;
//         }

//         .border-blue-900 {
//           border-color: #1e3a8a !important;
//         }

//         .text-teal-600 {
//           color: #0d9488 !important;
//         }

//         .bg-gray-200 {
//           background-color: #e5e7eb !important;
//         }

//         .bg-white {
//           background-color: #ffffff !important;
//         }

//         /* Ensure headers repeat on page breaks */
//         .component-header {
//           display: table-header-group;
//           page-break-inside: avoid;
//           border-bottom: 4px solid #1e3a8a;
//           margin-bottom: 1.5rem;
//           padding-bottom: 0.5rem;
//         }

//         .component-content {
//           display: table-row-group;
//         }

//         .flex {
//           display: flex;
//           align-items: center;
//           justify-content: space-between;
//           }

//         /* Print-specific styles */
//         @media print {
//           .component-header {
//             display: table-header-group;
//           }
//         }
//       </style>
//     </head>
//     <body class="bg-white">
//       <div class="report-container">
//         ${generateComponentsHTML(reportData, templateSettings)}
//       </div>
//     </body>
//     </html>
//   `;
// };

// const generateComponentsHTML = (reportData, templateSettings) => {
//   // Generate basic structure for the components
//   // In a real implementation, this would be more sophisticated
//   const components = [
//     'Table of Contents',
//     'Report Summary',
//     'Fiscal Year Dashboard',
//     'Operational Efficiency Dashboard',
//     'Liquidity Summary Dashboard',
//     'Profit & Loss - 13 Month Dashboard',
//     'Profit & Loss - Monthly Dashboard',
//     'Profit & Loss - YTD Dashboard',
//     'Balance Sheet Dashboard'
//   ];

//   return components.map((componentName, index) => `
//     <div class="component no-break ${index > 0 ? 'page-break' : ''}">
//       <div class="component-header flex">
//         <h1 class="text-3xl font-light text-gray-800 mb-4">${componentName}</h1>
//         <p class="text-sm text-gray-500 mb-6">January 2025 | Acme Print</p>
//       </div>
//       <div class="component-content">
//         <div class="bg-white p-6 rounded-lg shadow-sm">
//           <p class="text-lg text-gray-700 mb-4">
//             This section contains the ${componentName.toLowerCase()} data and analysis.
//           </p>
//           <div class="grid grid-cols-2 gap-4 mb-6">
//             <div class="bg-gray-50 p-4 rounded">
//               <h3 class="font-semibold text-gray-800 mb-2">Key Metrics</h3>
//               <p class="text-gray-600">Sample data would be displayed here based on the report data.</p>
//             </div>
//             <div class="bg-gray-50 p-4 rounded">
//               <h3 class="font-semibold text-gray-800 mb-2">Analysis</h3>
//               <p class="text-gray-600">Detailed analysis and insights would be shown here.</p>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   `).join('');
// };