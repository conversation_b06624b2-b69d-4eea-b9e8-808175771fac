/*
  Warnings:

  - You are about to drop the `report_content_settings` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "report_content_settings" DROP CONSTRAINT "report_content_settings_updatedBy_fkey";

-- DropTable
DROP TABLE "report_content_settings";

-- CreateTable
CREATE TABLE "ReportContentSettings" (
    "id" SERIAL NOT NULL,
    "reportType" "ReportTemplate" NOT NULL,
    "chartSettings" JSONB NOT NULL,
    "promptDescription" TEXT NOT NULL,
    "updatedBy" INTEGER,
    "isGlobalSetting" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ReportContentSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ReportContentSettings_reportType_idx" ON "ReportContentSettings"("reportType");

-- CreateIndex
CREATE INDEX "ReportContentSettings_updatedBy_idx" ON "ReportContentSettings"("updatedBy");

-- AddForeignKey
ALTER TABLE "ReportContentSettings" ADD CONSTRAINT "ReportContentSettings_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
