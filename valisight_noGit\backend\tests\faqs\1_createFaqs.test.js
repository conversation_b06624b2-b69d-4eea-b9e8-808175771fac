import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
const baseUrl = `/api/v1/faqs`;

describe('POST Create FAQS', () => {

    it('should create FAQs successfully', async () => {
        const faqData = [
            { question: 'What is your return policy?', answer: '30 days return.' },
            { question: 'Do you offer international shipping?', answer: 'Yes, worldwide.' },
        ];

        const res = await request(app)
            .post(`${baseUrl}/`)
            .send(faqData)
            .expect(200);        

        expect(res._body.success).to.be.true;
        expect(res._body.message).to.equal('FAQ created successfully');
        expect(res._body.faqs).to.be.an('array');
    });
    it("should return error if FAQ array is empty", async () => {
        const res = await request(app)
            .post(baseUrl)
            .send([])        

        expect(res._body.success).to.be.false;
        expect(res._body.message).to.equal("At least one FAQ is required.");
    });

    it("should return error if question is missing", async () => {
        const faqData = [{ answer: "30 days return." }];

        const res = await request(app)
            .post(baseUrl)
            .send(faqData)        

        expect(res.body.success).to.be.false;
        expect(res.body.message).to.equal("Question is required");
    });

    it("should return error if answer is missing", async () => {
        const faqData = [{ question: "What is your return policy?" }];

        const res = await request(app)
            .post(baseUrl)
            .send(faqData)

        expect(res._body.success).to.be.false;
        expect(res._body.message).to.equal("Answer is required");
    });


    
    it("should return error if payload is not an array", async () => {
        const faqData = { question: "What is your return policy?", answer: "30 days return." };

        const res = await request(app)
            .post(baseUrl)
            .send(faqData) // Sending an object instead of an array

        expect(res._body.success).to.be.false;
        expect(res._body.message).to.equal("FAQs must be an array.");
    });

});
