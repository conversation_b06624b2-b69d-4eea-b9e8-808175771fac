{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"defaultLocale\", {\n  enumerable: true,\n  get: function () {\n    return _index.enUS;\n  }\n});\nvar _index = require(\"../locale/en-US.js\");", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "enumerable", "get", "_index", "enUS", "require"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/_lib/defaultLocale.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"defaultLocale\", {\n  enumerable: true,\n  get: function () {\n    return _index.enUS;\n  },\n});\nvar _index = require(\"../locale/en-US.js\");\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,eAAe,EAAE;EAC9CC,UAAU,EAAE,IAAI;EAChBC,GAAG,EAAE,SAAAA,CAAA,EAAY;IACf,OAAOC,MAAM,CAACC,IAAI;EACpB;AACF,CAAC,CAAC;AACF,IAAID,MAAM,GAAGE,OAAO,CAAC,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}