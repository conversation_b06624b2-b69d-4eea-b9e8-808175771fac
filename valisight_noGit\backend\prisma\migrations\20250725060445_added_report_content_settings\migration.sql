-- CreateTable
CREATE TABLE "report_content_settings" (
    "id" SERIAL NOT NULL,
    "reportType" TEXT NOT NULL,
    "chartSettings" JSONB NOT NULL,
    "promptDescription" TEXT NOT NULL,
    "updatedBy" INTEGER,
    "isGlobalSetting" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "report_content_settings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "report_content_settings_reportType_idx" ON "report_content_settings"("reportType");

-- CreateIndex
CREATE INDEX "report_content_settings_updatedBy_idx" ON "report_content_settings"("updatedBy");

-- AddForeignKey
ALTER TABLE "report_content_settings" ADD CONSTRAINT "report_content_settings_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
