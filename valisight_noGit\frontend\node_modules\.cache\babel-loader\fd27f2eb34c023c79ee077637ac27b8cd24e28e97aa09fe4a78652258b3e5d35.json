{"ast": null, "code": "\"use strict\";\n\nexports.previousFriday = previousFriday;\nvar _index = require(\"./previousDay.js\");\n\n/**\n * @name previousFriday\n * @category Weekday Helpers\n * @summary When is the previous Friday?\n *\n * @description\n * When is the previous Friday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to start counting from\n *\n * @returns The previous Friday\n *\n * @example\n * // When is the previous Friday before Jun, 19, 2021?\n * const result = previousFriday(new Date(2021, 5, 19))\n * //=> Fri June 18 2021 00:00:00\n */\nfunction previousFriday(date) {\n  return (0, _index.previousDay)(date, 5);\n}", "map": {"version": 3, "names": ["exports", "previousFriday", "_index", "require", "date", "previousDay"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/previousFriday.js"], "sourcesContent": ["\"use strict\";\nexports.previousFriday = previousFriday;\nvar _index = require(\"./previousDay.js\");\n\n/**\n * @name previousFriday\n * @category Weekday Helpers\n * @summary When is the previous Friday?\n *\n * @description\n * When is the previous Friday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to start counting from\n *\n * @returns The previous Friday\n *\n * @example\n * // When is the previous Friday before Jun, 19, 2021?\n * const result = previousFriday(new Date(2021, 5, 19))\n * //=> Fri June 18 2021 00:00:00\n */\nfunction previousFriday(date) {\n  return (0, _index.previousDay)(date, 5);\n}\n"], "mappings": "AAAA,YAAY;;AACZA,OAAO,CAACC,cAAc,GAAGA,cAAc;AACvC,IAAIC,MAAM,GAAGC,OAAO,CAAC,kBAAkB,CAAC;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,cAAcA,CAACG,IAAI,EAAE;EAC5B,OAAO,CAAC,CAAC,EAAEF,MAAM,CAACG,WAAW,EAAED,IAAI,EAAE,CAAC,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}