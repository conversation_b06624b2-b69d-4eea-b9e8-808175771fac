{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid as createGrid2 } from '@mui/system/Grid';\nimport requirePropFactory from \"../utils/requirePropFactory.js\";\nimport { styled } from \"../styles/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport useTheme from \"../styles/useTheme.js\";\n/**\n *\n * Demos:\n *\n * - [Grid version 2](https://v6.mui.com/material-ui/react-grid2/)\n *\n * API:\n *\n * - [Grid2 API](https://v6.mui.com/material-ui/api/grid-2/)\n */\nconst Grid2 = createGrid2({\n  createStyledComponent: styled('div', {\n    name: 'MuiGrid2',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, ownerState.container && styles.container];\n    }\n  }),\n  componentName: 'MuiGrid2',\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: 'MuiGrid2'\n  }),\n  useTheme\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid2.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The number of columns.\n   * @default 12\n   */\n  columns: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number, PropTypes.object]),\n  /**\n   * Defines the horizontal space between the type `item` components.\n   * It overrides the value of the `spacing` prop.\n   */\n  columnSpacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * If `true`, the component will have the flex *container* behavior.\n   * You should be wrapping *items* with a *container*.\n   * @default false\n   */\n  container: PropTypes.bool,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'row'\n   */\n  direction: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Defines the offset value for the type `item` components.\n   */\n  offset: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.string, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])), PropTypes.object]),\n  /**\n   * Defines the vertical space between the type `item` components.\n   * It overrides the value of the `spacing` prop.\n   */\n  rowSpacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * Defines the size of the the type `item` components.\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number])), PropTypes.object]),\n  /**\n   * Defines the space between the type `item` components.\n   * It can only be used on a type `container` component.\n   * @default 0\n   */\n  spacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @internal\n   * The level of the grid starts from `0` and increases when the grid nests\n   * inside another grid. Nesting is defined as a container Grid being a direct\n   * child of a container Grid.\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <Grid container> // level 1\n   *     <Grid container> // level 2\n   * ```\n   *\n   * Only consecutive grid is considered nesting. A grid container will start at\n   * `0` if there are non-Grid container element above it.\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <div>\n   *     <Grid container> // level 0\n   * ```\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <Grid>\n   *     <Grid container> // level 0\n   * ```\n   */\n  unstable_level: PropTypes.number,\n  /**\n   * Defines the `flex-wrap` style property.\n   * It's applied for all screen sizes.\n   * @default 'wrap'\n   */\n  wrap: PropTypes.oneOf(['nowrap', 'wrap-reverse', 'wrap'])\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  const Component = Grid2;\n  const requireProp = requirePropFactory('Grid2', Component);\n  // eslint-disable-next-line no-useless-concat\n  Component['propTypes' + ''] = {\n    // eslint-disable-next-line react/forbid-foreign-prop-types\n    ...Component.propTypes,\n    direction: requireProp('container'),\n    spacing: requireProp('container'),\n    wrap: requireProp('container')\n  };\n}\nexport default Grid2;", "map": {"version": 3, "names": ["PropTypes", "createGrid", "createGrid2", "requirePropFactory", "styled", "useDefaultProps", "useTheme", "Grid2", "createStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "ownerState", "root", "container", "componentName", "useThemeProps", "inProps", "process", "env", "NODE_ENV", "propTypes", "children", "node", "columns", "oneOfType", "arrayOf", "number", "object", "columnSpacing", "string", "bool", "direction", "oneOf", "offset", "rowSpacing", "size", "spacing", "sx", "func", "unstable_level", "wrap", "Component", "requireProp"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/@mui/material/Grid2/Grid2.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid as createGrid2 } from '@mui/system/Grid';\nimport requirePropFactory from \"../utils/requirePropFactory.js\";\nimport { styled } from \"../styles/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport useTheme from \"../styles/useTheme.js\";\n/**\n *\n * Demos:\n *\n * - [Grid version 2](https://v6.mui.com/material-ui/react-grid2/)\n *\n * API:\n *\n * - [Grid2 API](https://v6.mui.com/material-ui/api/grid-2/)\n */\nconst Grid2 = createGrid2({\n  createStyledComponent: styled('div', {\n    name: 'MuiGrid2',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, ownerState.container && styles.container];\n    }\n  }),\n  componentName: 'MuiGrid2',\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: 'MuiGrid2'\n  }),\n  useTheme\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid2.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The number of columns.\n   * @default 12\n   */\n  columns: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.number), PropTypes.number, PropTypes.object]),\n  /**\n   * Defines the horizontal space between the type `item` components.\n   * It overrides the value of the `spacing` prop.\n   */\n  columnSpacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * If `true`, the component will have the flex *container* behavior.\n   * You should be wrapping *items* with a *container*.\n   * @default false\n   */\n  container: PropTypes.bool,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'row'\n   */\n  direction: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Defines the offset value for the type `item` components.\n   */\n  offset: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.string, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])), PropTypes.object]),\n  /**\n   * Defines the vertical space between the type `item` components.\n   * It overrides the value of the `spacing` prop.\n   */\n  rowSpacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * Defines the size of the the type `item` components.\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.bool, PropTypes.number])), PropTypes.object]),\n  /**\n   * Defines the space between the type `item` components.\n   * It can only be used on a type `container` component.\n   * @default 0\n   */\n  spacing: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * @internal\n   * The level of the grid starts from `0` and increases when the grid nests\n   * inside another grid. Nesting is defined as a container Grid being a direct\n   * child of a container Grid.\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <Grid container> // level 1\n   *     <Grid container> // level 2\n   * ```\n   *\n   * Only consecutive grid is considered nesting. A grid container will start at\n   * `0` if there are non-Grid container element above it.\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <div>\n   *     <Grid container> // level 0\n   * ```\n   *\n   * ```js\n   * <Grid container> // level 0\n   *   <Grid>\n   *     <Grid container> // level 0\n   * ```\n   */\n  unstable_level: PropTypes.number,\n  /**\n   * Defines the `flex-wrap` style property.\n   * It's applied for all screen sizes.\n   * @default 'wrap'\n   */\n  wrap: PropTypes.oneOf(['nowrap', 'wrap-reverse', 'wrap'])\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  const Component = Grid2;\n  const requireProp = requirePropFactory('Grid2', Component);\n  // eslint-disable-next-line no-useless-concat\n  Component['propTypes' + ''] = {\n    // eslint-disable-next-line react/forbid-foreign-prop-types\n    ...Component.propTypes,\n    direction: requireProp('container'),\n    spacing: requireProp('container'),\n    wrap: requireProp('container')\n  };\n}\nexport default Grid2;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,IAAIC,WAAW,QAAQ,kBAAkB;AAC5D,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAGL,WAAW,CAAC;EACxBM,qBAAqB,EAAEJ,MAAM,CAAC,KAAK,EAAE;IACnCK,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACpC,MAAM;QACJC;MACF,CAAC,GAAGF,KAAK;MACT,OAAO,CAACC,MAAM,CAACE,IAAI,EAAED,UAAU,CAACE,SAAS,IAAIH,MAAM,CAACG,SAAS,CAAC;IAChE;EACF,CAAC,CAAC;EACFC,aAAa,EAAE,UAAU;EACzBC,aAAa,EAAEC,OAAO,IAAId,eAAe,CAAC;IACxCO,KAAK,EAAEO,OAAO;IACdV,IAAI,EAAE;EACR,CAAC,CAAC;EACFH;AACF,CAAC,CAAC;AACFc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,KAAK,CAACgB,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAExB,SAAS,CAACyB,IAAI;EACxB;AACF;AACA;AACA;EACEC,OAAO,EAAE1B,SAAS,CAAC,sCAAsC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAAC6B,MAAM,CAAC,EAAE7B,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAAC8B,MAAM,CAAC,CAAC;EAC7I;AACF;AACA;AACA;EACEC,aAAa,EAAE/B,SAAS,CAAC,sCAAsC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAAC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAACgC,MAAM,CAAC,CAAC,CAAC,EAAEhC,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAAC8B,MAAM,EAAE9B,SAAS,CAACgC,MAAM,CAAC,CAAC;EAC9M;AACF;AACA;AACA;AACA;EACEhB,SAAS,EAAEhB,SAAS,CAACiC,IAAI;EACzB;AACF;AACA;AACA;AACA;EACEC,SAAS,EAAElC,SAAS,CAAC,sCAAsC2B,SAAS,CAAC,CAAC3B,SAAS,CAACmC,KAAK,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,EAAEnC,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAACmC,KAAK,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,EAAEnC,SAAS,CAAC8B,MAAM,CAAC,CAAC;EACrP;AACF;AACA;EACEM,MAAM,EAAEpC,SAAS,CAAC,sCAAsC2B,SAAS,CAAC,CAAC3B,SAAS,CAACgC,MAAM,EAAEhC,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAAC2B,SAAS,CAAC,CAAC3B,SAAS,CAACgC,MAAM,EAAEhC,SAAS,CAAC6B,MAAM,CAAC,CAAC,CAAC,EAAE7B,SAAS,CAAC8B,MAAM,CAAC,CAAC;EACvM;AACF;AACA;AACA;EACEO,UAAU,EAAErC,SAAS,CAAC,sCAAsC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAAC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAACgC,MAAM,CAAC,CAAC,CAAC,EAAEhC,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAAC8B,MAAM,EAAE9B,SAAS,CAACgC,MAAM,CAAC,CAAC;EAC3M;AACF;AACA;EACEM,IAAI,EAAEtC,SAAS,CAAC,sCAAsC2B,SAAS,CAAC,CAAC3B,SAAS,CAACgC,MAAM,EAAEhC,SAAS,CAACiC,IAAI,EAAEjC,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAAC2B,SAAS,CAAC,CAAC3B,SAAS,CAACgC,MAAM,EAAEhC,SAAS,CAACiC,IAAI,EAAEjC,SAAS,CAAC6B,MAAM,CAAC,CAAC,CAAC,EAAE7B,SAAS,CAAC8B,MAAM,CAAC,CAAC;EACrO;AACF;AACA;AACA;AACA;EACES,OAAO,EAAEvC,SAAS,CAAC,sCAAsC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAAC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAACgC,MAAM,CAAC,CAAC,CAAC,EAAEhC,SAAS,CAAC6B,MAAM,EAAE7B,SAAS,CAAC8B,MAAM,EAAE9B,SAAS,CAACgC,MAAM,CAAC,CAAC;EACxM;AACF;AACA;EACEQ,EAAE,EAAExC,SAAS,CAAC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAAC2B,SAAS,CAAC,CAAC3B,SAAS,CAACyC,IAAI,EAAEzC,SAAS,CAAC8B,MAAM,EAAE9B,SAAS,CAACiC,IAAI,CAAC,CAAC,CAAC,EAAEjC,SAAS,CAACyC,IAAI,EAAEzC,SAAS,CAAC8B,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEY,cAAc,EAAE1C,SAAS,CAAC6B,MAAM;EAChC;AACF;AACA;AACA;AACA;EACEc,IAAI,EAAE3C,SAAS,CAACmC,KAAK,CAAC,CAAC,QAAQ,EAAE,cAAc,EAAE,MAAM,CAAC;AAC1D,CAAC,GAAG,KAAK,CAAC;AACV,IAAIf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,MAAMsB,SAAS,GAAGrC,KAAK;EACvB,MAAMsC,WAAW,GAAG1C,kBAAkB,CAAC,OAAO,EAAEyC,SAAS,CAAC;EAC1D;EACAA,SAAS,CAAC,WAAW,GAAG,EAAE,CAAC,GAAG;IAC5B;IACA,GAAGA,SAAS,CAACrB,SAAS;IACtBW,SAAS,EAAEW,WAAW,CAAC,WAAW,CAAC;IACnCN,OAAO,EAAEM,WAAW,CAAC,WAAW,CAAC;IACjCF,IAAI,EAAEE,WAAW,CAAC,WAAW;EAC/B,CAAC;AACH;AACA,eAAetC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}