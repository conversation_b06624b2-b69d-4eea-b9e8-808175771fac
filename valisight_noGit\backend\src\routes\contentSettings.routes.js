import { Router } from 'express';
import {
  getAllContentSettings,
  getContentSettingsByReportType,
  updateContentSettings,
} from '../controllers/contentSettings.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';

export const contentSettingsRoute = Router();
contentSettingsRoute.get('/company/:companyId', authenticate, getAllContentSettings);
contentSettingsRoute.get(
  '/company/:companyId/:reportType',
  authenticate,
  getContentSettingsByReportType,
);
contentSettingsRoute.put('/company/:companyId/:reportType', authenticate, updateContentSettings);
