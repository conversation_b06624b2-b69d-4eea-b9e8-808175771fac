import { Router } from "express";
export const sharedCompaniesRoute = Router();
import * as sharedCompaniesController from "../controllers/sharedCompanies.controller.js"
import { authenticate } from "../middleware/auth.middleware.js";




sharedCompaniesRoute.get("/users", authenticate, sharedCompaniesController.getAllUsers);   
sharedCompaniesRoute.get("/shared-users", authenticate, sharedCompaniesController.getSharedUsers);  
sharedCompaniesRoute.post("/shared-users", authenticate, sharedCompaniesController.addSharedUser);  
sharedCompaniesRoute.delete("/shared-users/:id", authenticate, sharedCompaniesController.deleteSharedUser);  

