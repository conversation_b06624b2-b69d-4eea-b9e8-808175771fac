import * as templateService from '../services/template.service.js';
export const template = async (req, res) => {
    try {
        if (!req.files.length) {
            return res.status(400).json({ message: 'No file uploaded' });
        }
        res.json(await templateService.createExampleTemplate(req));
    } catch (error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: error.success,
            statusCode: error.statusCode,
            message: error.message,
        });
    }
};

export const getTemplates = async (req, res) => {
    try {
        res.json(await templateService.getAllTemplates(req));
    } catch (error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: error.success,
            statusCode: error.statusCode,
            message: error.message,
        });
    }
}

export const downloadTemplateFiles = async (req, res) => {
    try{
        await templateService.downloadTemplateFiles(req, res);
    }catch(error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: error.success,
            statusCode: error.statusCode,
            message: error.message,
        });
    }
}

export const deleteTemplate = async (req, res) => {
    try{
        res.json(await templateService.deleteTemplateByID(req));
    }catch(error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: error.success,
            statusCode: error.statusCode,
            message: error.message,
        });
    }
}

export const updateTemplate = async (req, res) => {
    try{
        res.json(await templateService.updateTemplateByID(req))
    }catch(error) {
        console.log(error.message);
        const { status } = error;
        const s = status || 500;
        res.status(s).json({
            success: error.success,
            statusCode: error.statusCode,
            message: error.message,
        });
    }  
}