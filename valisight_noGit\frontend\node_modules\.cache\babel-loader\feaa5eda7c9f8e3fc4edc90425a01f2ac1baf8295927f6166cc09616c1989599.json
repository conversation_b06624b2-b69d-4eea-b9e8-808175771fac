{"ast": null, "code": "\"use strict\";\n\nexports.eachMinuteOfInterval = eachMinuteOfInterval;\nvar _index = require(\"./addMinutes.js\");\nvar _index2 = require(\"./startOfMinute.js\");\nvar _index3 = require(\"./toDate.js\");\n\n/**\n * The {@link eachMinuteOfInterval} function options.\n */\n\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of minutes from the minute of the interval start to the minute of the interval end\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nfunction eachMinuteOfInterval(interval, options) {\n  const startDate = (0, _index2.startOfMinute)((0, _index3.toDate)(interval.start));\n  const endDate = (0, _index3.toDate)(interval.end);\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startDate : +endDate;\n  let currentDate = reversed ? endDate : startDate;\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+currentDate <= endTime) {\n    dates.push((0, _index3.toDate)(currentDate));\n    currentDate = (0, _index.addMinutes)(currentDate, step);\n  }\n  return reversed ? dates.reverse() : dates;\n}", "map": {"version": 3, "names": ["exports", "eachMinuteOfInterval", "_index", "require", "_index2", "_index3", "interval", "options", "startDate", "startOfMinute", "toDate", "start", "endDate", "end", "reversed", "endTime", "currentDate", "step", "dates", "push", "addMinutes", "reverse"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/eachMinuteOfInterval.js"], "sourcesContent": ["\"use strict\";\nexports.eachMinuteOfInterval = eachMinuteOfInterval;\nvar _index = require(\"./addMinutes.js\");\nvar _index2 = require(\"./startOfMinute.js\");\nvar _index3 = require(\"./toDate.js\");\n\n/**\n * The {@link eachMinuteOfInterval} function options.\n */\n\n/**\n * @name eachMinuteOfInterval\n * @category Interval Helpers\n * @summary Return the array of minutes within the specified time interval.\n *\n * @description\n * Returns the array of minutes within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of minutes from the minute of the interval start to the minute of the interval end\n *\n * @example\n * // Each minute between 14 October 2020, 13:00 and 14 October 2020, 13:03\n * const result = eachMinuteOfInterval({\n *   start: new Date(2014, 9, 14, 13),\n *   end: new Date(2014, 9, 14, 13, 3)\n * })\n * //=> [\n * //   Wed Oct 14 2014 13:00:00,\n * //   Wed Oct 14 2014 13:01:00,\n * //   Wed Oct 14 2014 13:02:00,\n * //   Wed Oct 14 2014 13:03:00\n * // ]\n */\nfunction eachMinuteOfInterval(interval, options) {\n  const startDate = (0, _index2.startOfMinute)(\n    (0, _index3.toDate)(interval.start),\n  );\n  const endDate = (0, _index3.toDate)(interval.end);\n\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startDate : +endDate;\n  let currentDate = reversed ? endDate : startDate;\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    dates.push((0, _index3.toDate)(currentDate));\n    currentDate = (0, _index.addMinutes)(currentDate, step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n"], "mappings": "AAAA,YAAY;;AACZA,OAAO,CAACC,oBAAoB,GAAGA,oBAAoB;AACnD,IAAIC,MAAM,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AACvC,IAAIC,OAAO,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAC3C,IAAIE,OAAO,GAAGF,OAAO,CAAC,aAAa,CAAC;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,oBAAoBA,CAACK,QAAQ,EAAEC,OAAO,EAAE;EAC/C,MAAMC,SAAS,GAAG,CAAC,CAAC,EAAEJ,OAAO,CAACK,aAAa,EACzC,CAAC,CAAC,EAAEJ,OAAO,CAACK,MAAM,EAAEJ,QAAQ,CAACK,KAAK,CACpC,CAAC;EACD,MAAMC,OAAO,GAAG,CAAC,CAAC,EAAEP,OAAO,CAACK,MAAM,EAAEJ,QAAQ,CAACO,GAAG,CAAC;EAEjD,IAAIC,QAAQ,GAAG,CAACN,SAAS,GAAG,CAACI,OAAO;EACpC,MAAMG,OAAO,GAAGD,QAAQ,GAAG,CAACN,SAAS,GAAG,CAACI,OAAO;EAChD,IAAII,WAAW,GAAGF,QAAQ,GAAGF,OAAO,GAAGJ,SAAS;EAEhD,IAAIS,IAAI,GAAGV,OAAO,EAAEU,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZH,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMI,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACF,WAAW,IAAID,OAAO,EAAE;IAC9BG,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEd,OAAO,CAACK,MAAM,EAAEM,WAAW,CAAC,CAAC;IAC5CA,WAAW,GAAG,CAAC,CAAC,EAAEd,MAAM,CAACkB,UAAU,EAAEJ,WAAW,EAAEC,IAAI,CAAC;EACzD;EAEA,OAAOH,QAAQ,GAAGI,KAAK,CAACG,OAAO,CAAC,CAAC,GAAGH,KAAK;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}