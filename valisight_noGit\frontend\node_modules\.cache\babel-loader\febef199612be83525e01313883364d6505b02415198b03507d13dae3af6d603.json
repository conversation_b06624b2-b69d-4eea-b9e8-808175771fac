{"ast": null, "code": "\"use strict\";\n\nexports.getMilliseconds = getMilliseconds;\nvar _index = require(\"./toDate.js\");\n\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The milliseconds\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\nfunction getMilliseconds(date) {\n  const _date = (0, _index.toDate)(date);\n  const milliseconds = _date.getMilliseconds();\n  return milliseconds;\n}", "map": {"version": 3, "names": ["exports", "getMilliseconds", "_index", "require", "date", "_date", "toDate", "milliseconds"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/getMilliseconds.js"], "sourcesContent": ["\"use strict\";\nexports.getMilliseconds = getMilliseconds;\nvar _index = require(\"./toDate.js\");\n\n/**\n * @name getMilliseconds\n * @category Millisecond Helpers\n * @summary Get the milliseconds of the given date.\n *\n * @description\n * Get the milliseconds of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The milliseconds\n *\n * @example\n * // Get the milliseconds of 29 February 2012 11:45:05.123:\n * const result = getMilliseconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 123\n */\nfunction getMilliseconds(date) {\n  const _date = (0, _index.toDate)(date);\n  const milliseconds = _date.getMilliseconds();\n  return milliseconds;\n}\n"], "mappings": "AAAA,YAAY;;AACZA,OAAO,CAACC,eAAe,GAAGA,eAAe;AACzC,IAAIC,MAAM,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,eAAeA,CAACG,IAAI,EAAE;EAC7B,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEH,MAAM,CAACI,MAAM,EAAEF,IAAI,CAAC;EACtC,MAAMG,YAAY,GAAGF,KAAK,CAACJ,eAAe,CAAC,CAAC;EAC5C,OAAOM,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}