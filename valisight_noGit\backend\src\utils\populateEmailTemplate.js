// import fs from 'fs';
// import path from 'path';

// export const populateEmailTemplate = async (templateName, replaceValues) => {
//   // Derive the directory from import.meta.url
//   const __dirname = path.dirname(new URL(import.meta.url).pathname);

//   // Remove the leading slash if present (for Windows paths)
//   const normalizedDirname = __dirname.replace(/^\/+/, '');

//   // Correct path joining using normalized __dirname
//   const filePath = path.join(normalizedDirname, '..', 'public', templateName);

//   // Normalize the path to avoid any inconsistencies
//   const normalizedPath = path.normalize(filePath);

//   // Log the normalized path
//   console.log('Normalized File Path:', normalizedPath);

//   // Check if the file exists at the path
//   if (!fs.existsSync(normalizedPath)) {
//     console.error(`File does not exist: ${normalizedPath}`);
//     throw new Error(`File not found: ${normalizedPath}`);
//   }

//   // Read the file content
//   const content = fs.readFileSync(normalizedPath, 'utf-8');

//   // Replace values in the template
//   let emailTemplate = content;
//   replaceValues.forEach((item_) => {
//     let key_ = new RegExp('\\$\\{' + item_.original + '\\}', 'g'); // Step 1
//     emailTemplate = emailTemplate.replace(key_, item_.newVal); // Step 2
//   });

//   return emailTemplate;
// };

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

export const populateEmailTemplate = async (templateName, replaceValues) => {
  // Derive the directory from import.meta.url and decode the path
  const __dirname = path.dirname(fileURLToPath(import.meta.url));

  // Join the path to the template file
  const filePath = path.join(__dirname, '..', 'public', templateName);

  // Normalize the path to avoid inconsistencies
  const normalizedPath = path.resolve(filePath);


  // Check if the file exists
  if (!fs.existsSync(normalizedPath)) {
    console.error(`File does not exist: ${normalizedPath}`);
    throw new Error(`File not found: ${normalizedPath}`);
  }

  // Read the file content
  const content = fs.readFileSync(normalizedPath, 'utf-8');

  // Replace values in the template
  let emailTemplate = content;
  replaceValues.forEach((item_) => {
    const key_ = new RegExp(`\\$\\{${item_.original}\\}`, 'g'); // Step 1
    emailTemplate = emailTemplate.replace(key_, item_.newVal); // Step 2
  });

  return emailTemplate;
};
