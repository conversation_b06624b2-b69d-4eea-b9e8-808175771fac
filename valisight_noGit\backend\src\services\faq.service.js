
import { prisma } from '../db/prisma.js';

export const createFaq = async (body) => {
    try {
        return await prisma.$transaction(async (tx) => {
            const faqs = await tx.faqs.createMany({
                data: body
            });

            // Fetch the created FAQs (if you have unique identifiers, like titles or IDs)
            const createdFaqs = await tx.faqs.findMany({
                where: {
                    OR: body.map((faq) => ({
                        question: faq.question,
                    })),
                },
            });
            return {
                success: true,
                message: 'FAQ created successfully',
                faqs: createdFaqs,
            };
        })
    } catch (error) {
        throw {
            status: error.status,
            success: false,
            statusCode: error.statusCode,
            message: error.message || 'Internal Server Error',
        };
    }
}

export const getFaqs = async () => {
    try {
        const faqs = await prisma.faqs.findMany({
            orderBy: {
                createdAt: 'desc',
            }
        });
        return {
            success: true,
            faqs,
        };
    } catch (error) {
        throw {
            status: error.status,
            success: false,
            statusCode: error.statusCode,
            message: error.message || 'Internal Server Error',
        };
    }
}

export const deleteFaq = async (ids) => {
    try {
        const idArray = ids.split(',').map((id) => parseInt(id.trim(), 10));

        // Check if all IDs are valid numbers
        if (idArray.some(isNaN)) {
            return {
                status: 400,
                success: false,
                message: 'Invalid IDs. Ensure all IDs are numeric.',
            };
        }
        return await prisma.$transaction(async (tx) => {
            const faqs = await tx.faqs.deleteMany({
                where: {
                    id: {
                        in: idArray,
                    },
                },
            });
            return {
                success: true,
                message: `${faqs.count} FAQs deleted successfully.`,
                faqs,
            };
        });
    } catch (error) {
        throw {
            status: error.status,
            success: false,
            statusCode: error.statusCode,
            message: error.message || 'Internal Server Error',
        };
    }
}

export  const updateFaq = async (id, body) => {
    try {
        return await prisma.$transaction(async (tx) => {
            const faq = await tx.faqs.update({
                where: {
                    id: Number(id),
                },
                data: body,
            });
            return {
                success: true,
                message: 'FAQ updated successfully',
                faq,
            };
        });
    }catch (error) {
        throw {
            status: error.status,
            success: false,
            statusCode: error.statusCode,
            message: error.message || 'Internal Server Error',
        };
    }
}