import express from 'express';
import bodyParser from 'body-parser';
const app = express();
import { router } from './routes/index.js';
import cors from 'cors';
import path from 'path';
import compression from "compression";
import './jobs/refreshQboTokens.js';
import { globalErrorHandler } from './middleware/error.middleware.js';

app.use(compression());

const __dirname = path.resolve();
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

app.use(cors({ origin: true, credentials: true }));
app.use(bodyParser.json({ limit: '10mb' }));

// api route
app.use('/api/v1/', router);

app.use(globalErrorHandler);

const PORT = process.env.PORT || 8000;
console.log({PORT}, process.env.PORT)
app.listen(PORT, () => {
  console.log(`Server is connected on port ${PORT}`);
});


export {app}
