/*
  Warnings:

  - You are about to drop the `DefaultUsersSettings` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "DefaultUsersSettings" DROP CONSTRAINT "DefaultUsersSettings_sharedBy_fkey";

-- DropForeignKey
ALTER TABLE "DefaultUsersSettings" DROP CONSTRAINT "DefaultUsersSettings_sharedWith_fkey";

-- DropTable
DROP TABLE "DefaultUsersSettings";

-- CreateTable
CREATE TABLE "DefaultUserSettings" (
    "id" SERIAL NOT NULL,
    "username" TEXT NOT NULL,
    "sharedBy" INTEGER,
    "sharedWith" INTEGER,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),

    CONSTRAINT "DefaultUserSettings_pkey" PRIMARY KEY ("id")
);

-- Add<PERSON><PERSON><PERSON>Key
ALTER TABLE "DefaultUserSettings" ADD CONSTRAINT "DefaultUserSettings_sharedBy_fkey" FOREIGN KEY ("sharedBy") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DefaultUserSettings" ADD CONSTRAINT "DefaultUserSettings_sharedWith_fkey" FOREIGN KEY ("sharedWith") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
