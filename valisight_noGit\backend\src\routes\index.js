import { Router } from 'express';
import { authRoute } from './auth.routes.js';
import { companyRoute } from './company.routes.js';
import { faqRoute } from './faq.route.js';
import { financialRoute } from './financial.routes.js';
import qboRoute from './qbo.routes.js';
import { reportRoute } from './report.route.js';
import { settingsRoute } from './settings.route.js';
import { sharedCompaniesRoute } from './sharedCompanies.routes.js';
import { templateRoute } from './template.route.js';
import { pdfRoute } from './pdf.routes.js';
import { templateSettingRoute } from './templateSettings.routes.js';
import { contentSettingsRoute } from './contentSettings.routes.js';

export const router = Router();

router.get('/health', (req, res) => {
  res.status(200).json({ status: 'Valisight backend is working...' });
});

// auth routes
router.use('/auth', authRoute);

//company routes
router.use('/company', companyRoute);
router.use('/report', reportRoute);
router.use('/templates', templateRoute);
router.use('/faqs', faqRoute);
router.use('/shared-companies', sharedCompaniesRoute);
router.use('/settings', settingsRoute);
router.use('/qbo', qboRoute);
router.use('/financial', financialRoute);
router.use('/pdf', pdfRoute);
router.use('/template-settings', templateSettingRoute);
router.use('/content-settings', contentSettingsRoute);
