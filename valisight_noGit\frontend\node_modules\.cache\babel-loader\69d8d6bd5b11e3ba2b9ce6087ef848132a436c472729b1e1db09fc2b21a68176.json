{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\LiquiditySummary.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport ApexCharts from 'apexcharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LiquiditySummaryDashboard = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  liquidityData = null\n}) => {\n  _s();\n  const netChangeRef = useRef(null);\n  const quickRatioRef = useRef(null);\n  const monthsCashRef = useRef(null);\n\n  // Enhanced data validation function\n  const isDataLoaded = () => {\n    var _liquidityData$netCha, _liquidityData$quickR, _liquidityData$monthO;\n    if (!liquidityData) {\n      console.log('LiquiditySummary - No liquidityData provided');\n      return false;\n    }\n    console.log('LiquiditySummary - liquidityData keys:', Object.keys(liquidityData));\n    console.log('LiquiditySummary - Full liquidityData:', liquidityData);\n\n    // Check if data might be nested under a different structure\n    const possibleDataPaths = [liquidityData, liquidityData.liquiditySummary, liquidityData.liquidity, liquidityData.data, liquidityData.reportData];\n    console.log('LiquiditySummary - Checking possible data paths:', possibleDataPaths.map(path => path ? Object.keys(path) : 'null'));\n\n    // Check if at least some required data exists\n    const hasNetChangeData = liquidityData.netChangeInCash && Array.isArray(liquidityData.netChangeInCash) && liquidityData.netChangeInCash.length > 0;\n    const hasQuickRatioData = liquidityData.quickRatio && Array.isArray(liquidityData.quickRatio) && liquidityData.quickRatio.length > 0;\n    const hasMonthsCashData = liquidityData.monthOnCash && Array.isArray(liquidityData.monthOnCash) && liquidityData.monthOnCash.length > 0;\n    console.log('LiquiditySummary - Data validation:', {\n      hasNetChangeData,\n      hasQuickRatioData,\n      hasMonthsCashData,\n      netChangeLength: ((_liquidityData$netCha = liquidityData.netChangeInCash) === null || _liquidityData$netCha === void 0 ? void 0 : _liquidityData$netCha.length) || 0,\n      quickRatioLength: ((_liquidityData$quickR = liquidityData.quickRatio) === null || _liquidityData$quickR === void 0 ? void 0 : _liquidityData$quickR.length) || 0,\n      monthsCashLength: ((_liquidityData$monthO = liquidityData.monthOnCash) === null || _liquidityData$monthO === void 0 ? void 0 : _liquidityData$monthO.length) || 0\n    });\n\n    // Log sample data for debugging\n    if (liquidityData.monthOnCash && liquidityData.monthOnCash.length > 0) {\n      console.log('LiquiditySummary - Sample months cash on hand data:', liquidityData.monthOnCash[0]);\n    }\n\n    // Return true if we have at least some data to work with\n    return hasNetChangeData || hasQuickRatioData || hasMonthsCashData;\n  };\n  useEffect(() => {\n    if (isDataLoaded()) {\n      initializeCharts();\n    }\n  }, [liquidityData]);\n  const formatMonthYear = (year, month) => {\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n  const initializeCharts = () => {\n    var _dataToUse$netChangeI, _dataToUse$netChangeI2, _dataToUse$quickRatio, _dataToUse$monthOnCas;\n    if (!liquidityData) return;\n    console.log('LiquiditySummary - Initializing charts with data:', liquidityData);\n\n    // Try to find the correct data structure\n    let dataToUse = liquidityData;\n\n    // Check if data is nested under different possible paths\n    if (liquidityData.liquiditySummary) {\n      dataToUse = liquidityData.liquiditySummary;\n      console.log('LiquiditySummary - Using nested liquiditySummary data');\n    } else if (liquidityData.liquidity) {\n      dataToUse = liquidityData.liquidity;\n      console.log('LiquiditySummary - Using nested liquidity data');\n    } else if (liquidityData.data) {\n      dataToUse = liquidityData.data;\n      console.log('LiquiditySummary - Using nested data');\n    } else if (liquidityData.reportData) {\n      dataToUse = liquidityData.reportData;\n      console.log('LiquiditySummary - Using nested reportData');\n    }\n    console.log('LiquiditySummary - Final data structure to use:', dataToUse);\n\n    // Prepare data from API response\n    const categories = ((_dataToUse$netChangeI = dataToUse.netChangeInCash) === null || _dataToUse$netChangeI === void 0 ? void 0 : _dataToUse$netChangeI.map(item => formatMonthYear(item.year, item.month))) || [];\n    const netChangeInCashData = ((_dataToUse$netChangeI2 = dataToUse.netChangeInCash) === null || _dataToUse$netChangeI2 === void 0 ? void 0 : _dataToUse$netChangeI2.map(item => parseFloat(item.net_change_in_cash) / 1000 || 0 // Convert to thousands for better display\n    )) || [];\n    const quickRatioData = ((_dataToUse$quickRatio = dataToUse.quickRatio) === null || _dataToUse$quickRatio === void 0 ? void 0 : _dataToUse$quickRatio.map(item => parseFloat(item.quick_ratio) || 0)) || [];\n    const monthsCashOnHandData = ((_dataToUse$monthOnCas = dataToUse.monthOnCash) === null || _dataToUse$monthOnCas === void 0 ? void 0 : _dataToUse$monthOnCas.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.monthsOfCash || item.months_cash || item.monthsCashOnHand || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    console.log('LiquiditySummary - Processed data:', {\n      categoriesLength: categories.length,\n      netChangeLength: netChangeInCashData.length,\n      netChangeData: netChangeInCashData,\n      quickRatioLength: quickRatioData.length,\n      quickRatioData: quickRatioData,\n      monthsCashLength: monthsCashOnHandData.length,\n      monthsCashData: monthsCashOnHandData\n    });\n\n    // 1. Net Change in Cash Chart\n    const netChangeOptions = {\n      series: [{\n        name: 'Net Change in Cash',\n        data: netChangeInCashData\n      }],\n      chart: {\n        type: 'line',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent'\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val >= 1) {\n            return '$' + val.toFixed(1) + 'k';\n          } else if (val >= 0.1) {\n            return '$' + val.toFixed(2) + 'k';\n          } else if (val <= -0.1) {\n            return '-$' + Math.abs(val).toFixed(2) + 'k';\n          } else {\n            return '$' + val.toFixed(3) + 'k';\n          }\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -15,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'straight',\n        width: 3 // Changed from 2 to 3 to match netProfitMarginOptions\n      },\n      fill: {\n        type: 'solid'\n      },\n      markers: {\n        size: 5,\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        colors: netChangeInCashData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\n        hover: {\n          size: 7\n        },\n        discrete: netChangeInCashData.map((val, index) => ({\n          seriesIndex: 0,\n          dataPointIndex: index,\n          fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\n          strokeColor: '#fff',\n          size: 5\n        }))\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#1E7C8C'],\n      // Default line color\n      plotOptions: {\n        line: {\n          colors: {\n            threshold: 0,\n            colorAboveThreshold: '#1E7C8C',\n            colorBelowThreshold: '#d70015'\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val >= 1) {\n              return '$' + val.toFixed(1) + ' thousand';\n            } else if (val >= 0.1) {\n              return '$' + val.toFixed(2) + ' thousand';\n            } else if (val <= -0.1) {\n              return '-$' + Math.abs(val).toFixed(2) + ' thousand';\n            } else {\n              return '$' + val.toFixed(3) + ' thousand';\n            }\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 25,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: '#666',\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 0.8\n        }]\n      }\n    };\n    // 2. Quick Ratio Chart\n    const quickRatioOptions = {\n      series: [{\n        name: 'Quick Ratio',\n        data: quickRatioData\n      }],\n      chart: {\n        type: 'area',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent'\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return val.toFixed(2);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: ['#5457a3']\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: '#5457a3',\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: '#5457a3',\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: ['#5457a3'],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#5457a3'],\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return val.toFixed(2);\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      }\n    };\n\n    // 3. Months Cash on Hand Chart\n    const monthsCashOptions = {\n      series: [{\n        name: 'Months Cash on Hand',\n        data: monthsCashOnHandData\n      }],\n      chart: {\n        type: 'area',\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent'\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return val.toFixed(2);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: ['#298478']\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: '#298478',\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: '#298478',\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: ['#298478'],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: ['#298478'],\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return val.toFixed(2) + ' months';\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      }\n    };\n\n    // Clear existing charts before rendering new ones\n    const clearAndRenderChart = (ref, options, chartName) => {\n      if (ref.current) {\n        // Clear any existing chart\n        ref.current.innerHTML = '';\n\n        // Wait a tick before rendering to ensure DOM is cleared\n        setTimeout(() => {\n          if (ref.current) {\n            try {\n              console.log(`LiquiditySummary - Rendering ${chartName} chart`);\n              const chart = new ApexCharts(ref.current, options);\n              chart.render();\n            } catch (error) {\n              console.error(`LiquiditySummary - Error rendering ${chartName} chart:`, error);\n            }\n          }\n        }, 10);\n      }\n    };\n\n    // Only render charts if we have data for them\n    console.log('LiquiditySummary - Chart rendering check:', {\n      netChangeDataLength: netChangeInCashData.length,\n      netChangeData: netChangeInCashData,\n      quickRatioDataLength: quickRatioData.length,\n      quickRatioData: quickRatioData,\n      monthsCashDataLength: monthsCashOnHandData.length,\n      monthsCashData: monthsCashOnHandData\n    });\n    if (netChangeInCashData.length > 0) {\n      clearAndRenderChart(netChangeRef, netChangeOptions, 'Net Change in Cash');\n    } else if (netChangeRef.current) {\n      netChangeRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No net change in cash data available</div>';\n    }\n    if (quickRatioData.length > 0) {\n      clearAndRenderChart(quickRatioRef, quickRatioOptions, 'Quick Ratio');\n    } else if (quickRatioRef.current) {\n      quickRatioRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No quick ratio data available</div>';\n    }\n    if (monthsCashOnHandData.length > 0) {\n      clearAndRenderChart(monthsCashRef, monthsCashOptions, 'Months Cash on Hand');\n    } else if (monthsCashRef.current) {\n      monthsCashRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No months cash data available</div>';\n    }\n  };\n\n  // Enhanced loading component\n  const LoadingComponent = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col gap-1 p-10 mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl text-gray-600 mb-2\",\n            children: \"Loading liquidity data...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Please wait while we fetch your liquidity information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 486,\n    columnNumber: 5\n  }, this);\n\n  // Show loading if data is not properly loaded\n  if (!isDataLoaded()) {\n    return /*#__PURE__*/_jsxDEV(LoadingComponent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Add a fallback if liquidityData exists but has no usable data\n  const hasAnyUsableData = () => {\n    var _liquidityData$netCha2, _liquidityData$quickR2, _liquidityData$monthO2;\n    const netChangeInCashData = ((_liquidityData$netCha2 = liquidityData.netChangeInCash) === null || _liquidityData$netCha2 === void 0 ? void 0 : _liquidityData$netCha2.map(item => parseFloat(item.net_change_in_cash) || 0)) || [];\n    const quickRatioData = ((_liquidityData$quickR2 = liquidityData.quickRatio) === null || _liquidityData$quickR2 === void 0 ? void 0 : _liquidityData$quickR2.map(item => parseFloat(item.quick_ratio) || 0)) || [];\n    const monthsCashOnHandData = ((_liquidityData$monthO2 = liquidityData.monthOnCash) === null || _liquidityData$monthO2 === void 0 ? void 0 : _liquidityData$monthO2.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.monthsOfCash || item.months_cash || item.monthsCashOnHand || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    console.log('LiquiditySummary - Months Cash on Hand Data:', monthsCashOnHandData);\n    return netChangeInCashData.length > 0 || quickRatioData.length > 0 || monthsCashOnHandData.length > 0;\n  };\n  if (!hasAnyUsableData()) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen p-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl text-gray-600 mb-2\",\n              children: \"No liquidity data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"The data structure is available but charts cannot be rendered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400 mt-2\",\n              children: [\"Available data: \", Object.keys(liquidityData || {}).join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Liquidity Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: \"January 2025 | Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Net Change in Cash\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: netChangeRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Quick Ratio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: quickRatioRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Months Cash on Hand\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: monthsCashRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-teal-600 text-2xl\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter'\n            },\n            children: \"Months Cash on Hand\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: contentTextStyle,\n            children: \"Given the amount of cash available, the number of months that a business can continue to pay for its core and operating expenses. Under 3 months or a downward trend may be cause for concern.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 552,\n    columnNumber: 5\n  }, this);\n};\n_s(LiquiditySummaryDashboard, \"68uIpoO+VVhsTFN3ehX92Vi/wyU=\");\n_c = LiquiditySummaryDashboard;\nexport default LiquiditySummaryDashboard;\nvar _c;\n$RefreshReg$(_c, \"LiquiditySummaryDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "LiquiditySummaryDashboard", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "contentTextStyle", "liquidityData", "_s", "netChangeRef", "quickRatioRef", "monthsCashRef", "isDataLoaded", "_liquidityData$netCha", "_liquidityData$quickR", "_liquidityData$monthO", "console", "log", "Object", "keys", "possibleDataPaths", "liquiditySummary", "liquidity", "data", "reportData", "map", "path", "hasNetChangeData", "netChangeInCash", "Array", "isArray", "length", "hasQuickRatioData", "quickRatio", "hasMonthsCashData", "monthOnCash", "netChangeLength", "quickRatioLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "_dataToUse$netChangeI", "_dataToUse$netChangeI2", "_dataToUse$quickRatio", "_dataToUse$monthOnCas", "dataToUse", "categories", "item", "netChangeInCashData", "parseFloat", "net_change_in_cash", "quickRatioData", "quick_ratio", "monthsCashOnHandData", "value", "monthsOfCash", "months_cash", "monthsCashOnHand", "categoriesLength", "netChangeData", "monthsCashData", "netChangeOptions", "series", "name", "chart", "type", "height", "toolbar", "show", "background", "dataLabels", "enabled", "formatter", "val", "toFixed", "Math", "abs", "style", "fontSize", "colors", "fontWeight", "offsetY", "dropShadow", "stroke", "curve", "width", "fill", "markers", "size", "strokeColors", "strokeWidth", "hover", "discrete", "index", "seriesIndex", "dataPointIndex", "fillColor", "strokeColor", "xaxis", "labels", "axisBorder", "axisTicks", "yaxis", "plotOptions", "line", "threshold", "colorAboveThreshold", "colorBelowThreshold", "tooltip", "y", "grid", "padding", "left", "right", "top", "bottom", "annotations", "borderColor", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity", "quickRatioOptions", "gradient", "shadeIntensity", "colorStops", "offset", "color", "legend", "monthsCashOptions", "clearAndRender<PERSON>hart", "ref", "options", "chartName", "current", "innerHTML", "setTimeout", "render", "error", "netChangeDataLength", "quickRatioDataLength", "monthsCashDataLength", "LoadingComponent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hasAnyUsableData", "_liquidityData$netCha2", "_liquidityData$quickR2", "_liquidityData$monthO2", "join", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/reports/ReportPages/LiquiditySummary.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport ApexCharts from 'apexcharts';\r\n\r\nconst LiquiditySummaryDashboard = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  liquidityData = null\r\n}) => {\r\n  const netChangeRef = useRef(null);\r\n  const quickRatioRef = useRef(null);\r\n  const monthsCashRef = useRef(null);\r\n\r\n  // Enhanced data validation function\r\n  const isDataLoaded = () => {\r\n    if (!liquidityData) {\r\n      console.log('LiquiditySummary - No liquidityData provided');\r\n      return false;\r\n    }\r\n\r\n    console.log('LiquiditySummary - liquidityData keys:', Object.keys(liquidityData));\r\n    console.log('LiquiditySummary - Full liquidityData:', liquidityData);\r\n\r\n    // Check if data might be nested under a different structure\r\n    const possibleDataPaths = [\r\n      liquidityData,\r\n      liquidityData.liquiditySummary,\r\n      liquidityData.liquidity,\r\n      liquidityData.data,\r\n      liquidityData.reportData\r\n    ];\r\n\r\n    console.log('LiquiditySummary - Checking possible data paths:', possibleDataPaths.map(path => path ? Object.keys(path) : 'null'));\r\n\r\n    // Check if at least some required data exists\r\n    const hasNetChangeData = liquidityData.netChangeInCash &&\r\n      Array.isArray(liquidityData.netChangeInCash) &&\r\n      liquidityData.netChangeInCash.length > 0;\r\n\r\n    const hasQuickRatioData = liquidityData.quickRatio &&\r\n      Array.isArray(liquidityData.quickRatio) &&\r\n      liquidityData.quickRatio.length > 0;\r\n\r\n    const hasMonthsCashData = liquidityData.monthOnCash &&\r\n      Array.isArray(liquidityData.monthOnCash) &&\r\n      liquidityData.monthOnCash.length > 0;\r\n\r\n    console.log('LiquiditySummary - Data validation:', {\r\n      hasNetChangeData,\r\n      hasQuickRatioData,\r\n      hasMonthsCashData,\r\n      netChangeLength: liquidityData.netChangeInCash?.length || 0,\r\n      quickRatioLength: liquidityData.quickRatio?.length || 0,\r\n      monthsCashLength: liquidityData.monthOnCash?.length || 0\r\n    });\r\n\r\n    // Log sample data for debugging\r\n    if (liquidityData.monthOnCash && liquidityData.monthOnCash.length > 0) {\r\n      console.log('LiquiditySummary - Sample months cash on hand data:', liquidityData.monthOnCash[0]);\r\n    }\r\n\r\n    // Return true if we have at least some data to work with\r\n    return hasNetChangeData || hasQuickRatioData || hasMonthsCashData;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      initializeCharts();\r\n    }\r\n  }, [liquidityData]);\r\n\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\r\n      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  const initializeCharts = () => {\r\n    if (!liquidityData) return;\r\n\r\n    console.log('LiquiditySummary - Initializing charts with data:', liquidityData);\r\n\r\n    // Try to find the correct data structure\r\n    let dataToUse = liquidityData;\r\n\r\n    // Check if data is nested under different possible paths\r\n    if (liquidityData.liquiditySummary) {\r\n      dataToUse = liquidityData.liquiditySummary;\r\n      console.log('LiquiditySummary - Using nested liquiditySummary data');\r\n    } else if (liquidityData.liquidity) {\r\n      dataToUse = liquidityData.liquidity;\r\n      console.log('LiquiditySummary - Using nested liquidity data');\r\n    } else if (liquidityData.data) {\r\n      dataToUse = liquidityData.data;\r\n      console.log('LiquiditySummary - Using nested data');\r\n    } else if (liquidityData.reportData) {\r\n      dataToUse = liquidityData.reportData;\r\n      console.log('LiquiditySummary - Using nested reportData');\r\n    }\r\n\r\n    console.log('LiquiditySummary - Final data structure to use:', dataToUse);\r\n\r\n    // Prepare data from API response\r\n    const categories = dataToUse.netChangeInCash?.map(item =>\r\n      formatMonthYear(item.year, item.month)\r\n    ) || [];\r\n\r\n    const netChangeInCashData = dataToUse.netChangeInCash?.map(item =>\r\n      parseFloat(item.net_change_in_cash) / 1000 || 0  // Convert to thousands for better display\r\n    ) || [];\r\n\r\n    const quickRatioData = dataToUse.quickRatio?.map(item =>\r\n      parseFloat(item.quick_ratio) || 0\r\n    ) || [];\r\n\r\n    const monthsCashOnHandData = dataToUse.monthOnCash?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.monthsOfCash || item.months_cash || item.monthsCashOnHand || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    console.log('LiquiditySummary - Processed data:', {\r\n      categoriesLength: categories.length,\r\n      netChangeLength: netChangeInCashData.length,\r\n      netChangeData: netChangeInCashData,\r\n      quickRatioLength: quickRatioData.length,\r\n      quickRatioData: quickRatioData,\r\n      monthsCashLength: monthsCashOnHandData.length,\r\n      monthsCashData: monthsCashOnHandData\r\n    });\r\n\r\n    // 1. Net Change in Cash Chart\r\nconst netChangeOptions = {\r\n  series: [{\r\n    name: 'Net Change in Cash',\r\n    data: netChangeInCashData\r\n  }],\r\n  chart: {\r\n    type: 'line',\r\n    height: 300,\r\n    toolbar: { show: false },\r\n    background: 'transparent'\r\n  },\r\n  dataLabels: {\r\n    enabled: true,\r\n    formatter: function (val) {\r\n      if (val >= 1) {\r\n        return '$' + val.toFixed(1) + 'k';\r\n      } else if (val >= 0.1) {\r\n        return '$' + val.toFixed(2) + 'k';\r\n      } else if (val <= -0.1) {\r\n        return '-$' + Math.abs(val).toFixed(2) + 'k';\r\n      } else {\r\n        return '$' + val.toFixed(3) + 'k';\r\n      }\r\n    },\r\n    style: {\r\n      fontSize: '14px',\r\n      colors: ['#333'],\r\n      fontWeight: '500'\r\n    },\r\n    offsetY: -15,\r\n    background: {\r\n      enabled: false\r\n    },\r\n    dropShadow: {\r\n      enabled: false\r\n    }\r\n  },\r\n  stroke: {\r\n    curve: 'straight',\r\n    width: 3 // Changed from 2 to 3 to match netProfitMarginOptions\r\n  },\r\n  fill: {\r\n    type: 'solid'\r\n  },\r\n  markers: {\r\n    size: 5,\r\n    strokeColors: '#fff',\r\n    strokeWidth: 2,\r\n    colors: netChangeInCashData.map(val => val >= 0 ? '#1E7C8C' : '#d70015'),\r\n    hover: {\r\n      size: 7\r\n    },\r\n    discrete: netChangeInCashData.map((val, index) => ({\r\n      seriesIndex: 0,\r\n      dataPointIndex: index,\r\n      fillColor: val >= 0 ? '#1E7C8C' : '#d70015',\r\n      strokeColor: '#fff',\r\n      size: 5\r\n    }))\r\n  },\r\n  xaxis: {\r\n    categories: categories,\r\n    labels: {\r\n      style: {\r\n        colors: '#666',\r\n        fontSize: '14px'\r\n      }\r\n    },\r\n    axisBorder: { show: false },\r\n    axisTicks: { show: false }\r\n  },\r\n  yaxis: {\r\n    show: false\r\n  },\r\n  colors: ['#1E7C8C'], // Default line color\r\n  plotOptions: {\r\n    line: {\r\n      colors: {\r\n        threshold: 0,\r\n        colorAboveThreshold: '#1E7C8C',\r\n        colorBelowThreshold: '#d70015',\r\n      },\r\n    }\r\n  },\r\n  tooltip: {\r\n    y: {\r\n      formatter: function (val) {\r\n        if (val >= 1) {\r\n          return '$' + val.toFixed(1) + ' thousand';\r\n        } else if (val >= 0.1) {\r\n          return '$' + val.toFixed(2) + ' thousand';\r\n        } else if (val <= -0.1) {\r\n          return '-$' + Math.abs(val).toFixed(2) + ' thousand';\r\n        } else {\r\n          return '$' + val.toFixed(3) + ' thousand';\r\n        }\r\n      }\r\n    }\r\n  },\r\n  grid: {\r\n    show: false,\r\n    padding: {\r\n      left: 25,\r\n      right: 25,\r\n      top: 25,\r\n      bottom: 0\r\n    }\r\n  },\r\n  annotations: {\r\n    yaxis: [{\r\n      y: 0,\r\n      borderColor: '#666',\r\n      borderWidth: 1,\r\n      strokeDashArray: 0,\r\n      opacity: 0.8\r\n    }]\r\n  }\r\n};\r\n    // 2. Quick Ratio Chart\r\n    const quickRatioOptions = {\r\n      series: [{\r\n        name: 'Quick Ratio',\r\n        data: quickRatioData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent'\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return val.toFixed(2);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: ['#5457a3']\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: '#5457a3', opacity: 0.4 },\r\n            { offset: 100, color: '#5457a3', opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: ['#5457a3'],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#5457a3'],\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return val.toFixed(2);\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      }\r\n    };\r\n\r\n    // 3. Months Cash on Hand Chart\r\n    const monthsCashOptions = {\r\n      series: [{\r\n        name: 'Months Cash on Hand',\r\n        data: monthsCashOnHandData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: 'transparent'\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return val.toFixed(2);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: ['#298478']\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: '#298478', opacity: 0.4 },\r\n            { offset: 100, color: '#298478', opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: ['#298478'],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: {\r\n            colors: '#666',\r\n            fontSize: '14px'\r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: ['#298478'],\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return val.toFixed(2) + ' months';\r\n          }\r\n        }\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: {\r\n        show: false\r\n      }\r\n    };\r\n\r\n    // Clear existing charts before rendering new ones\r\n    const clearAndRenderChart = (ref, options, chartName) => {\r\n      if (ref.current) {\r\n        // Clear any existing chart\r\n        ref.current.innerHTML = '';\r\n\r\n        // Wait a tick before rendering to ensure DOM is cleared\r\n        setTimeout(() => {\r\n          if (ref.current) {\r\n            try {\r\n              console.log(`LiquiditySummary - Rendering ${chartName} chart`);\r\n              const chart = new ApexCharts(ref.current, options);\r\n              chart.render();\r\n            } catch (error) {\r\n              console.error(`LiquiditySummary - Error rendering ${chartName} chart:`, error);\r\n            }\r\n          }\r\n        }, 10);\r\n      }\r\n    };\r\n\r\n    // Only render charts if we have data for them\r\n    console.log('LiquiditySummary - Chart rendering check:', {\r\n      netChangeDataLength: netChangeInCashData.length,\r\n      netChangeData: netChangeInCashData,\r\n      quickRatioDataLength: quickRatioData.length,\r\n      quickRatioData: quickRatioData,\r\n      monthsCashDataLength: monthsCashOnHandData.length,\r\n      monthsCashData: monthsCashOnHandData\r\n    });\r\n\r\n    if (netChangeInCashData.length > 0) {\r\n      clearAndRenderChart(netChangeRef, netChangeOptions, 'Net Change in Cash');\r\n    } else if (netChangeRef.current) {\r\n      netChangeRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No net change in cash data available</div>';\r\n    }\r\n\r\n    if (quickRatioData.length > 0) {\r\n      clearAndRenderChart(quickRatioRef, quickRatioOptions, 'Quick Ratio');\r\n    } else if (quickRatioRef.current) {\r\n      quickRatioRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No quick ratio data available</div>';\r\n    }\r\n\r\n    if (monthsCashOnHandData.length > 0) {\r\n      clearAndRenderChart(monthsCashRef, monthsCashOptions, 'Months Cash on Hand');\r\n    } else if (monthsCashRef.current) {\r\n      monthsCashRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No months cash data available</div>';\r\n    }\r\n  };\r\n\r\n  // Enhanced loading component\r\n  const LoadingComponent = () => (\r\n    <div className=\"min-h-screen p-5\">\r\n      <div className=\"max-w-6xl mx-auto bg-white flex flex-col gap-1 p-10 mb-8\">\r\n        <div className=\"flex items-center justify-center h-64\">\r\n          <div className=\"text-center\">\r\n            {/* Loading spinner */}\r\n            <div className=\"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4\"></div>\r\n            <div className=\"text-xl text-gray-600 mb-2\">\r\n              Loading liquidity data...\r\n            </div>\r\n            <div className=\"text-sm text-gray-500\">\r\n              Please wait while we fetch your liquidity information\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Show loading if data is not properly loaded\r\n  if (!isDataLoaded()) {\r\n    return <LoadingComponent />;\r\n  }\r\n\r\n  // Add a fallback if liquidityData exists but has no usable data\r\n  const hasAnyUsableData = () => {\r\n    const netChangeInCashData = liquidityData.netChangeInCash?.map(item =>\r\n      parseFloat(item.net_change_in_cash) || 0\r\n    ) || [];\r\n\r\n    const quickRatioData = liquidityData.quickRatio?.map(item =>\r\n      parseFloat(item.quick_ratio) || 0\r\n    ) || [];\r\n\r\n    const monthsCashOnHandData = liquidityData.monthOnCash?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.monthsOfCash || item.months_cash || item.monthsCashOnHand || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n    console.log('LiquiditySummary - Months Cash on Hand Data:', monthsCashOnHandData);\r\n\r\n    return (netChangeInCashData.length > 0 || quickRatioData.length > 0 || monthsCashOnHandData.length > 0);\r\n  };\r\n\r\n  if (!hasAnyUsableData()) {\r\n    return (\r\n      <div className=\"min-h-screen p-5\">\r\n        <div className=\"max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8\">\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-xl text-gray-600 mb-2\">\r\n                No liquidity data available\r\n              </div>\r\n              <div className=\"text-sm text-gray-500\">\r\n                The data structure is available but charts cannot be rendered\r\n              </div>\r\n              <div className=\"text-xs text-gray-400 mt-2\">\r\n                Available data: {Object.keys(liquidityData || {}).join(', ')}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-5\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl mx-auto bg-white flex flex-col p-10 mb-8\">\r\n\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n           Liquidity Summary\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            January 2025 | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Net Change in Cash Chart */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Net Change in Cash\r\n          </div>\r\n          <div ref={netChangeRef}></div>\r\n        </div>\r\n\r\n        {/* Quick Ratio Chart */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Quick Ratio\r\n          </div>\r\n          <div ref={quickRatioRef}></div>\r\n        </div>\r\n\r\n        {/* Months Cash on Hand Chart */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Months Cash on Hand\r\n          </div>\r\n          <div ref={monthsCashRef}></div>\r\n          <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n            <div\r\n              className=\"text-teal-600 text-2xl\"\r\n              style={{ ...subHeadingTextStyle, fontWeight: 'lighter' }}\r\n            >\r\n              Months Cash on Hand\r\n            </div>\r\n            <div style={contentTextStyle}>\r\n              Given the amount of cash available, the number of months that a business can continue to pay for its core and operating expenses. Under 3 months or a downward trend may be cause for concern.\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LiquiditySummaryDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,yBAAyB,GAAGA,CAAC;EACjCC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,YAAY,GAAGX,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMY,aAAa,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMa,aAAa,GAAGb,MAAM,CAAC,IAAI,CAAC;;EAElC;EACA,MAAMc,YAAY,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACzB,IAAI,CAACR,aAAa,EAAE;MAClBS,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,OAAO,KAAK;IACd;IAEAD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,MAAM,CAACC,IAAI,CAACZ,aAAa,CAAC,CAAC;IACjFS,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEV,aAAa,CAAC;;IAEpE;IACA,MAAMa,iBAAiB,GAAG,CACxBb,aAAa,EACbA,aAAa,CAACc,gBAAgB,EAC9Bd,aAAa,CAACe,SAAS,EACvBf,aAAa,CAACgB,IAAI,EAClBhB,aAAa,CAACiB,UAAU,CACzB;IAEDR,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEG,iBAAiB,CAACK,GAAG,CAACC,IAAI,IAAIA,IAAI,GAAGR,MAAM,CAACC,IAAI,CAACO,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;;IAEjI;IACA,MAAMC,gBAAgB,GAAGpB,aAAa,CAACqB,eAAe,IACpDC,KAAK,CAACC,OAAO,CAACvB,aAAa,CAACqB,eAAe,CAAC,IAC5CrB,aAAa,CAACqB,eAAe,CAACG,MAAM,GAAG,CAAC;IAE1C,MAAMC,iBAAiB,GAAGzB,aAAa,CAAC0B,UAAU,IAChDJ,KAAK,CAACC,OAAO,CAACvB,aAAa,CAAC0B,UAAU,CAAC,IACvC1B,aAAa,CAAC0B,UAAU,CAACF,MAAM,GAAG,CAAC;IAErC,MAAMG,iBAAiB,GAAG3B,aAAa,CAAC4B,WAAW,IACjDN,KAAK,CAACC,OAAO,CAACvB,aAAa,CAAC4B,WAAW,CAAC,IACxC5B,aAAa,CAAC4B,WAAW,CAACJ,MAAM,GAAG,CAAC;IAEtCf,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;MACjDU,gBAAgB;MAChBK,iBAAiB;MACjBE,iBAAiB;MACjBE,eAAe,EAAE,EAAAvB,qBAAA,GAAAN,aAAa,CAACqB,eAAe,cAAAf,qBAAA,uBAA7BA,qBAAA,CAA+BkB,MAAM,KAAI,CAAC;MAC3DM,gBAAgB,EAAE,EAAAvB,qBAAA,GAAAP,aAAa,CAAC0B,UAAU,cAAAnB,qBAAA,uBAAxBA,qBAAA,CAA0BiB,MAAM,KAAI,CAAC;MACvDO,gBAAgB,EAAE,EAAAvB,qBAAA,GAAAR,aAAa,CAAC4B,WAAW,cAAApB,qBAAA,uBAAzBA,qBAAA,CAA2BgB,MAAM,KAAI;IACzD,CAAC,CAAC;;IAEF;IACA,IAAIxB,aAAa,CAAC4B,WAAW,IAAI5B,aAAa,CAAC4B,WAAW,CAACJ,MAAM,GAAG,CAAC,EAAE;MACrEf,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEV,aAAa,CAAC4B,WAAW,CAAC,CAAC,CAAC,CAAC;IAClG;;IAEA;IACA,OAAOR,gBAAgB,IAAIK,iBAAiB,IAAIE,iBAAiB;EACnE,CAAC;EAEDrC,SAAS,CAAC,MAAM;IACd,IAAIe,YAAY,CAAC,CAAC,EAAE;MAClB2B,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAChC,aAAa,CAAC,CAAC;EAEnB,MAAMiC,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAC1D,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC3C,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;EAED,MAAMN,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAO,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAC7B,IAAI,CAAC1C,aAAa,EAAE;IAEpBS,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEV,aAAa,CAAC;;IAE/E;IACA,IAAI2C,SAAS,GAAG3C,aAAa;;IAE7B;IACA,IAAIA,aAAa,CAACc,gBAAgB,EAAE;MAClC6B,SAAS,GAAG3C,aAAa,CAACc,gBAAgB;MAC1CL,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACtE,CAAC,MAAM,IAAIV,aAAa,CAACe,SAAS,EAAE;MAClC4B,SAAS,GAAG3C,aAAa,CAACe,SAAS;MACnCN,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC/D,CAAC,MAAM,IAAIV,aAAa,CAACgB,IAAI,EAAE;MAC7B2B,SAAS,GAAG3C,aAAa,CAACgB,IAAI;MAC9BP,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACrD,CAAC,MAAM,IAAIV,aAAa,CAACiB,UAAU,EAAE;MACnC0B,SAAS,GAAG3C,aAAa,CAACiB,UAAU;MACpCR,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;IAC3D;IAEAD,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEiC,SAAS,CAAC;;IAEzE;IACA,MAAMC,UAAU,GAAG,EAAAL,qBAAA,GAAAI,SAAS,CAACtB,eAAe,cAAAkB,qBAAA,uBAAzBA,qBAAA,CAA2BrB,GAAG,CAAC2B,IAAI,IACpDZ,eAAe,CAACY,IAAI,CAACX,IAAI,EAAEW,IAAI,CAACV,KAAK,CACvC,CAAC,KAAI,EAAE;IAEP,MAAMW,mBAAmB,GAAG,EAAAN,sBAAA,GAAAG,SAAS,CAACtB,eAAe,cAAAmB,sBAAA,uBAAzBA,sBAAA,CAA2BtB,GAAG,CAAC2B,IAAI,IAC7DE,UAAU,CAACF,IAAI,CAACG,kBAAkB,CAAC,GAAG,IAAI,IAAI,CAAC,CAAE;IACnD,CAAC,KAAI,EAAE;IAEP,MAAMC,cAAc,GAAG,EAAAR,qBAAA,GAAAE,SAAS,CAACjB,UAAU,cAAAe,qBAAA,uBAApBA,qBAAA,CAAsBvB,GAAG,CAAC2B,IAAI,IACnDE,UAAU,CAACF,IAAI,CAACK,WAAW,CAAC,IAAI,CAClC,CAAC,KAAI,EAAE;IAEP,MAAMC,oBAAoB,GAAG,EAAAT,qBAAA,GAAAC,SAAS,CAACf,WAAW,cAAAc,qBAAA,uBAArBA,qBAAA,CAAuBxB,GAAG,CAAC2B,IAAI,IAAI;MAC9D;MACA,MAAMO,KAAK,GAAGP,IAAI,CAACQ,YAAY,IAAIR,IAAI,CAACS,WAAW,IAAIT,IAAI,CAACU,gBAAgB,IAAI,CAAC;MACjF,OAAOR,UAAU,CAACK,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER3C,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;MAChD8C,gBAAgB,EAAEZ,UAAU,CAACpB,MAAM;MACnCK,eAAe,EAAEiB,mBAAmB,CAACtB,MAAM;MAC3CiC,aAAa,EAAEX,mBAAmB;MAClChB,gBAAgB,EAAEmB,cAAc,CAACzB,MAAM;MACvCyB,cAAc,EAAEA,cAAc;MAC9BlB,gBAAgB,EAAEoB,oBAAoB,CAAC3B,MAAM;MAC7CkC,cAAc,EAAEP;IAClB,CAAC,CAAC;;IAEF;IACJ,MAAMQ,gBAAgB,GAAG;MACvBC,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,oBAAoB;QAC1B7C,IAAI,EAAE8B;MACR,CAAC,CAAC;MACFgB,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;YACZ,OAAO,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAID,GAAG,IAAI,GAAG,EAAE;YACrB,OAAO,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC,CAAC,MAAM,IAAID,GAAG,IAAI,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACH,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UAC9C,CAAC,MAAM;YACL,OAAO,GAAG,GAAGD,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;UACnC;QACF,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZZ,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDW,UAAU,EAAE;UACVX,OAAO,EAAE;QACX;MACF,CAAC;MACDY,MAAM,EAAE;QACNC,KAAK,EAAE,UAAU;QACjBC,KAAK,EAAE,CAAC,CAAC;MACX,CAAC;MACDC,IAAI,EAAE;QACJrB,IAAI,EAAE;MACR,CAAC;MACDsB,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdX,MAAM,EAAE/B,mBAAmB,CAAC5B,GAAG,CAACqD,GAAG,IAAIA,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC;QACxEkB,KAAK,EAAE;UACLH,IAAI,EAAE;QACR,CAAC;QACDI,QAAQ,EAAE5C,mBAAmB,CAAC5B,GAAG,CAAC,CAACqD,GAAG,EAAEoB,KAAK,MAAM;UACjDC,WAAW,EAAE,CAAC;UACdC,cAAc,EAAEF,KAAK;UACrBG,SAAS,EAAEvB,GAAG,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;UAC3CwB,WAAW,EAAE,MAAM;UACnBT,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC;MACDU,KAAK,EAAE;QACLpD,UAAU,EAAEA,UAAU;QACtBqD,MAAM,EAAE;UACNtB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDsB,UAAU,EAAE;UAAEhC,IAAI,EAAE;QAAM,CAAC;QAC3BiC,SAAS,EAAE;UAAEjC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDkC,KAAK,EAAE;QACLlC,IAAI,EAAE;MACR,CAAC;MACDW,MAAM,EAAE,CAAC,SAAS,CAAC;MAAE;MACrBwB,WAAW,EAAE;QACXC,IAAI,EAAE;UACJzB,MAAM,EAAE;YACN0B,SAAS,EAAE,CAAC;YACZC,mBAAmB,EAAE,SAAS;YAC9BC,mBAAmB,EAAE;UACvB;QACF;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,IAAIA,GAAG,IAAI,CAAC,EAAE;cACZ,OAAO,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW;YAC3C,CAAC,MAAM,IAAID,GAAG,IAAI,GAAG,EAAE;cACrB,OAAO,GAAG,GAAGA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW;YAC3C,CAAC,MAAM,IAAID,GAAG,IAAI,CAAC,GAAG,EAAE;cACtB,OAAO,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACH,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW;YACtD,CAAC,MAAM;cACL,OAAO,GAAG,GAAGD,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW;YAC3C;UACF;QACF;MACF,CAAC;MACDoC,IAAI,EAAE;QACJ1C,IAAI,EAAE,KAAK;QACX2C,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXd,KAAK,EAAE,CAAC;UACNO,CAAC,EAAE,CAAC;UACJQ,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClBC,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;IACG;IACA,MAAMC,iBAAiB,GAAG;MACxB3D,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,aAAa;QACnB7C,IAAI,EAAEiC;MACR,CAAC,CAAC;MACFa,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;QACvB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZZ,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDW,UAAU,EAAE;UACVX,OAAO,EAAE;QACX;MACF,CAAC;MACDY,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACRN,MAAM,EAAE,CAAC,SAAS;MACpB,CAAC;MACDO,IAAI,EAAE;QACJrB,IAAI,EAAE,UAAU;QAChByD,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjB1D,IAAI,EAAE,UAAU;UAChB2D,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAE,SAAS;YAAEN,OAAO,EAAE;UAAI,CAAC,EAC7C;YAAEK,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEN,OAAO,EAAE;UAAI,CAAC;QAEnD;MACF,CAAC;MACDjC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPT,MAAM,EAAE,CAAC,SAAS,CAAC;QACnBU,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDU,KAAK,EAAE;QACLpD,UAAU,EAAEA,UAAU;QACtBqD,MAAM,EAAE;UACNtB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDsB,UAAU,EAAE;UAAEhC,IAAI,EAAE;QAAM,CAAC;QAC3BiC,SAAS,EAAE;UAAEjC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDkC,KAAK,EAAE;QACLlC,IAAI,EAAE;MACR,CAAC;MACDW,MAAM,EAAE,CAAC,SAAS,CAAC;MACnB6B,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;UACvB;QACF;MACF,CAAC;MACDoC,IAAI,EAAE;QACJ1C,IAAI,EAAE,KAAK;QACX2C,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDY,MAAM,EAAE;QACN3D,IAAI,EAAE;MACR;IACF,CAAC;;IAED;IACA,MAAM4D,iBAAiB,GAAG;MACxBlE,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,qBAAqB;QAC3B7C,IAAI,EAAEmC;MACR,CAAC,CAAC;MACFW,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;QACvB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBC,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZZ,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDW,UAAU,EAAE;UACVX,OAAO,EAAE;QACX;MACF,CAAC;MACDY,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACRN,MAAM,EAAE,CAAC,SAAS;MACpB,CAAC;MACDO,IAAI,EAAE;QACJrB,IAAI,EAAE,UAAU;QAChByD,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjB1D,IAAI,EAAE,UAAU;UAChB2D,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAE,SAAS;YAAEN,OAAO,EAAE;UAAI,CAAC,EAC7C;YAAEK,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAE,SAAS;YAAEN,OAAO,EAAE;UAAI,CAAC;QAEnD;MACF,CAAC;MACDjC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPT,MAAM,EAAE,CAAC,SAAS,CAAC;QACnBU,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDU,KAAK,EAAE;QACLpD,UAAU,EAAEA,UAAU;QACtBqD,MAAM,EAAE;UACNtB,KAAK,EAAE;YACLE,MAAM,EAAE,MAAM;YACdD,QAAQ,EAAE;UACZ;QACF,CAAC;QACDsB,UAAU,EAAE;UAAEhC,IAAI,EAAE;QAAM,CAAC;QAC3BiC,SAAS,EAAE;UAAEjC,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDkC,KAAK,EAAE;QACLlC,IAAI,EAAE;MACR,CAAC;MACDW,MAAM,EAAE,CAAC,SAAS,CAAC;MACnB6B,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;YACxB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,SAAS;UACnC;QACF;MACF,CAAC;MACDoC,IAAI,EAAE;QACJ1C,IAAI,EAAE,KAAK;QACX2C,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDY,MAAM,EAAE;QACN3D,IAAI,EAAE;MACR;IACF,CAAC;;IAED;IACA,MAAM6D,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,OAAO,EAAEC,SAAS,KAAK;MACvD,IAAIF,GAAG,CAACG,OAAO,EAAE;QACf;QACAH,GAAG,CAACG,OAAO,CAACC,SAAS,GAAG,EAAE;;QAE1B;QACAC,UAAU,CAAC,MAAM;UACf,IAAIL,GAAG,CAACG,OAAO,EAAE;YACf,IAAI;cACF1H,OAAO,CAACC,GAAG,CAAC,gCAAgCwH,SAAS,QAAQ,CAAC;cAC9D,MAAMpE,KAAK,GAAG,IAAItE,UAAU,CAACwI,GAAG,CAACG,OAAO,EAAEF,OAAO,CAAC;cAClDnE,KAAK,CAACwE,MAAM,CAAC,CAAC;YAChB,CAAC,CAAC,OAAOC,KAAK,EAAE;cACd9H,OAAO,CAAC8H,KAAK,CAAC,sCAAsCL,SAAS,SAAS,EAAEK,KAAK,CAAC;YAChF;UACF;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC;;IAED;IACA9H,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MACvD8H,mBAAmB,EAAE1F,mBAAmB,CAACtB,MAAM;MAC/CiC,aAAa,EAAEX,mBAAmB;MAClC2F,oBAAoB,EAAExF,cAAc,CAACzB,MAAM;MAC3CyB,cAAc,EAAEA,cAAc;MAC9ByF,oBAAoB,EAAEvF,oBAAoB,CAAC3B,MAAM;MACjDkC,cAAc,EAAEP;IAClB,CAAC,CAAC;IAEF,IAAIL,mBAAmB,CAACtB,MAAM,GAAG,CAAC,EAAE;MAClCuG,mBAAmB,CAAC7H,YAAY,EAAEyD,gBAAgB,EAAE,oBAAoB,CAAC;IAC3E,CAAC,MAAM,IAAIzD,YAAY,CAACiI,OAAO,EAAE;MAC/BjI,YAAY,CAACiI,OAAO,CAACC,SAAS,GAAG,6GAA6G;IAChJ;IAEA,IAAInF,cAAc,CAACzB,MAAM,GAAG,CAAC,EAAE;MAC7BuG,mBAAmB,CAAC5H,aAAa,EAAEoH,iBAAiB,EAAE,aAAa,CAAC;IACtE,CAAC,MAAM,IAAIpH,aAAa,CAACgI,OAAO,EAAE;MAChChI,aAAa,CAACgI,OAAO,CAACC,SAAS,GAAG,sGAAsG;IAC1I;IAEA,IAAIjF,oBAAoB,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACnCuG,mBAAmB,CAAC3H,aAAa,EAAE0H,iBAAiB,EAAE,qBAAqB,CAAC;IAC9E,CAAC,MAAM,IAAI1H,aAAa,CAAC+H,OAAO,EAAE;MAChC/H,aAAa,CAAC+H,OAAO,CAACC,SAAS,GAAG,sGAAsG;IAC1I;EACF,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAGA,CAAA,kBACvBjJ,OAAA;IAAKkJ,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/BnJ,OAAA;MAAKkJ,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEnJ,OAAA;QAAKkJ,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpDnJ,OAAA;UAAKkJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAE1BnJ,OAAA;YAAKkJ,SAAS,EAAC;UAAkF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxGvJ,OAAA;YAAKkJ,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNvJ,OAAA;YAAKkJ,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,IAAI,CAAC5I,YAAY,CAAC,CAAC,EAAE;IACnB,oBAAOX,OAAA,CAACiJ,gBAAgB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;;EAEA;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC7B,MAAMvG,mBAAmB,GAAG,EAAAqG,sBAAA,GAAAnJ,aAAa,CAACqB,eAAe,cAAA8H,sBAAA,uBAA7BA,sBAAA,CAA+BjI,GAAG,CAAC2B,IAAI,IACjEE,UAAU,CAACF,IAAI,CAACG,kBAAkB,CAAC,IAAI,CACzC,CAAC,KAAI,EAAE;IAEP,MAAMC,cAAc,GAAG,EAAAmG,sBAAA,GAAApJ,aAAa,CAAC0B,UAAU,cAAA0H,sBAAA,uBAAxBA,sBAAA,CAA0BlI,GAAG,CAAC2B,IAAI,IACvDE,UAAU,CAACF,IAAI,CAACK,WAAW,CAAC,IAAI,CAClC,CAAC,KAAI,EAAE;IAEP,MAAMC,oBAAoB,GAAG,EAAAkG,sBAAA,GAAArJ,aAAa,CAAC4B,WAAW,cAAAyH,sBAAA,uBAAzBA,sBAAA,CAA2BnI,GAAG,CAAC2B,IAAI,IAAI;MAClE;MACA,MAAMO,KAAK,GAAGP,IAAI,CAACQ,YAAY,IAAIR,IAAI,CAACS,WAAW,IAAIT,IAAI,CAACU,gBAAgB,IAAI,CAAC;MACjF,OAAOR,UAAU,CAACK,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IACR3C,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEyC,oBAAoB,CAAC;IAEjF,OAAQL,mBAAmB,CAACtB,MAAM,GAAG,CAAC,IAAIyB,cAAc,CAACzB,MAAM,GAAG,CAAC,IAAI2B,oBAAoB,CAAC3B,MAAM,GAAG,CAAC;EACxG,CAAC;EAED,IAAI,CAAC0H,gBAAgB,CAAC,CAAC,EAAE;IACvB,oBACExJ,OAAA;MAAKkJ,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BnJ,OAAA;QAAKkJ,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxEnJ,OAAA;UAAKkJ,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDnJ,OAAA;YAAKkJ,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BnJ,OAAA;cAAKkJ,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNvJ,OAAA;cAAKkJ,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNvJ,OAAA;cAAKkJ,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,kBAC1B,EAAClI,MAAM,CAACC,IAAI,CAACZ,aAAa,IAAI,CAAC,CAAC,CAAC,CAACsJ,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEvJ,OAAA;IAAKkJ,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAE/BnJ,OAAA;MAAKkJ,SAAS,EAAC,oDAAoD;MAAAC,QAAA,gBAGjEnJ,OAAA;QAAKkJ,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvGnJ,OAAA;UACEkJ,SAAS,EAAC,sCAAsC;UAChDjE,KAAK,EAAE/E,eAAgB;UAAAiJ,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvJ,OAAA;UAAGkJ,SAAS,EAAC,2BAA2B;UAACjE,KAAK,EAAE7E,mBAAoB;UAAA+I,QAAA,EAAC;QAErE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvJ,OAAA;QAAKkJ,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDnJ,OAAA;UACEkJ,SAAS,EAAC,2CAA2C;UACrDjE,KAAK,EAAE7E,mBAAoB;UAAA+I,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvJ,OAAA;UAAKsI,GAAG,EAAE9H;QAAa;UAAA4I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAGNvJ,OAAA;QAAKkJ,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDnJ,OAAA;UACEkJ,SAAS,EAAC,2CAA2C;UACrDjE,KAAK,EAAE7E,mBAAoB;UAAA+I,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvJ,OAAA;UAAKsI,GAAG,EAAE7H;QAAc;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAGNvJ,OAAA;QAAKkJ,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtDnJ,OAAA;UACEkJ,SAAS,EAAC,2CAA2C;UACrDjE,KAAK,EAAE7E,mBAAoB;UAAA+I,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvJ,OAAA;UAAKsI,GAAG,EAAE5H;QAAc;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/BvJ,OAAA;UAAKkJ,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACzEnJ,OAAA;YACEkJ,SAAS,EAAC,wBAAwB;YAClCjE,KAAK,EAAE;cAAE,GAAG7E,mBAAmB;cAAEgF,UAAU,EAAE;YAAU,CAAE;YAAA+D,QAAA,EAC1D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNvJ,OAAA;YAAKiF,KAAK,EAAE5E,gBAAiB;YAAA8I,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChJ,EAAA,CAnmBIN,yBAAyB;AAAA4J,EAAA,GAAzB5J,yBAAyB;AAqmB/B,eAAeA,yBAAyB;AAAC,IAAA4J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}