import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
const baseUrl = `/api/v1/company`;
import { getcompanyId, getToken } from "../helpers/companyHelpers.js";
describe("Get Company Detail API", async () => {
  let authToken;
  let companyId;

  before(async function () {
    authToken = await getToken();
    companyId = await getcompanyId()
  });

  it("should fetch company details successfully", async () => {
    
    const res = await request(app)
      .get(`${baseUrl}/${companyId}`)
      .set("Authorization", `Bearer ${authToken}`);    

    expect(res._body.statusCode).to.equal(200);
    expect(res._body.success).to.be.true;
    expect(res._body.company).to.have.property("id", companyId);
    expect(res._body.company).to.have.property("companyFiles").that.is.an("array");
  });
  it("should return 404 if the company does not exist", async () => {
    const res = await request(app)
      .get(`${baseUrl}/999999`) // Non-existent company ID
      .set("Authorization", `Bearer ${authToken}`);    
      
    expect(res._body.statusCode).to.equal(404);
    expect(res._body.success).to.be.false;
    expect(res._body.message).to.contain("not found.");
  });
  it("should return validation error for invalid company ID", async () => {
    const res = await request(app)
      .get(`${baseUrl}/invalidId`)
      .set("Authorization", `Bearer ${authToken}`);
    
    expect(res._body.statusCode).to.equal(400);
    expect(res._body.success).to.be.false;
  });

  it("should return unauthorized error if no token is provided", async () => {
    const res = await request(app).get(`${baseUrl}/${companyId}`);

    expect(res._body.statusCode).to.equal(401);
    expect(res._body.success).to.be.false;
    expect(res._body.message).to.contain("Unauthorized");
  });
});
