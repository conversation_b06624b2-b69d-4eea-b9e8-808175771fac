[{"C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\layout\\LayOut.jsx": "4", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\EditReportPage.js": "5", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Auth.jsx": "6", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\CustomizeReport.jsx": "7", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\Login.jsx": "8", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\ResetPassword.jsx": "9", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\CheckEmail.jsx": "10", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\ForgotPassword.jsx": "11", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\ResetLink.jsx": "12", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\examples\\Examples.jsx": "13", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Dashboard\\Dashboard.jsx": "14", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\faqs\\Faqs.jsx": "15", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\QboCallback\\qboCallback.jsx": "16", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\View.jsx": "17", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\shared-companies\\SharedCompanies.jsx": "18", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\EditReport.jsx": "19", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\report.js": "20", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\axiosInstance.js": "21", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\pdf.js": "22", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\example.js": "23", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\auth.js": "24", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\faq.js": "25", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\company.js": "26", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\qbo.js": "27", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\shared-components\\TopBar.jsx": "28", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\ExpenseSummary.jsx": "29", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\TableOfContents.jsx": "30", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\MonthTrailing.jsx": "31", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\LiquiditySummary.jsx": "32", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\Monthly.jsx": "33", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\ReportSummary.jsx": "34", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\FiscalYear.jsx": "35", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\OperationalEfficiency.jsx": "36", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\YearToDate.jsx": "37", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\BalanceSheet.jsx": "38", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\CoverPage.jsx": "39", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Companies.jsx": "40", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\settings.js": "41", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\Create.jsx": "42", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\utils\\shared.js": "43", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\utils\\cookies.js": "44", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\Reports.jsx": "45", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\Edit.jsx": "46", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\shared-components\\BreadCrumbs.jsx": "47", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\hooks\\useDebounceSearch.jsx": "48", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\CSVModal.jsx": "49", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\UploadableSection.jsx": "50", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\ReportSettings.jsx": "51", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\QuickBooksConnection.jsx": "52", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\shared-companies\\SharedCompaniesList.jsx": "53", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\shared-companies\\SharedCompaniesModal.jsx": "54", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\data.js": "55", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\markets.js": "56", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\contentSettings.js": "57", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\ShareUsersModal.jsx": "58", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\shared-components\\NoDataFound.jsx": "59", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\ReportModal.jsx": "60", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\shared-components\\Table.jsx": "61", "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\enums\\report.enum.js": "62"}, {"size": 507, "mtime": 1754457903921, "results": "63", "hashOfConfig": "64"}, {"size": 2742, "mtime": 1754457903905, "results": "65", "hashOfConfig": "64"}, {"size": 375, "mtime": 1754457903972, "results": "66", "hashOfConfig": "64"}, {"size": 305, "mtime": 1754457903921, "results": "67", "hashOfConfig": "64"}, {"size": 886, "mtime": 1754457903972, "results": "68", "hashOfConfig": "64"}, {"size": 182, "mtime": 1754457903921, "results": "69", "hashOfConfig": "64"}, {"size": 57987, "mtime": 1754543772354, "results": "70", "hashOfConfig": "64"}, {"size": 5637, "mtime": 1754457903921, "results": "71", "hashOfConfig": "64"}, {"size": 6189, "mtime": 1754457903921, "results": "72", "hashOfConfig": "64"}, {"size": 2441, "mtime": 1754457903921, "results": "73", "hashOfConfig": "64"}, {"size": 4322, "mtime": 1754457903921, "results": "74", "hashOfConfig": "64"}, {"size": 2138, "mtime": 1754457903921, "results": "75", "hashOfConfig": "64"}, {"size": 13394, "mtime": 1754457903954, "results": "76", "hashOfConfig": "64"}, {"size": 2268, "mtime": 1754457903954, "results": "77", "hashOfConfig": "64"}, {"size": 2963, "mtime": 1754457903954, "results": "78", "hashOfConfig": "64"}, {"size": 16366, "mtime": 1754457903954, "results": "79", "hashOfConfig": "64"}, {"size": 45318, "mtime": 1754457903954, "results": "80", "hashOfConfig": "64"}, {"size": 2343, "mtime": 1754457903954, "results": "81", "hashOfConfig": "64"}, {"size": 34297, "mtime": 1754457903938, "results": "82", "hashOfConfig": "64"}, {"size": 801, "mtime": 1754457903982, "results": "83", "hashOfConfig": "64"}, {"size": 1370, "mtime": 1754457903982, "results": "84", "hashOfConfig": "64"}, {"size": 1331, "mtime": 1754457903982, "results": "85", "hashOfConfig": "64"}, {"size": 311, "mtime": 1754457903982, "results": "86", "hashOfConfig": "64"}, {"size": 549, "mtime": 1754457903982, "results": "87", "hashOfConfig": "64"}, {"size": 139, "mtime": 1754457903982, "results": "88", "hashOfConfig": "64"}, {"size": 1491, "mtime": 1754457903982, "results": "89", "hashOfConfig": "64"}, {"size": 1198, "mtime": 1754457903982, "results": "90", "hashOfConfig": "64"}, {"size": 3619, "mtime": 1754457903982, "results": "91", "hashOfConfig": "64"}, {"size": 31402, "mtime": 1754542681324, "results": "92", "hashOfConfig": "64"}, {"size": 2849, "mtime": 1754476378596, "results": "93", "hashOfConfig": "64"}, {"size": 8237, "mtime": 1754475073558, "results": "94", "hashOfConfig": "64"}, {"size": 18509, "mtime": 1754541341987, "results": "95", "hashOfConfig": "64"}, {"size": 13248, "mtime": 1754464377414, "results": "96", "hashOfConfig": "64"}, {"size": 4791, "mtime": 1754457903972, "results": "97", "hashOfConfig": "64"}, {"size": 29538, "mtime": 1754482470752, "results": "98", "hashOfConfig": "64"}, {"size": 30396, "mtime": 1754542275896, "results": "99", "hashOfConfig": "64"}, {"size": 14051, "mtime": 1754480523512, "results": "100", "hashOfConfig": "64"}, {"size": 17675, "mtime": 1754457903972, "results": "101", "hashOfConfig": "64"}, {"size": 2070, "mtime": 1754457903972, "results": "102", "hashOfConfig": "64"}, {"size": 13474, "mtime": 1754457903921, "results": "103", "hashOfConfig": "64"}, {"size": 557, "mtime": 1754457903982, "results": "104", "hashOfConfig": "64"}, {"size": 26562, "mtime": 1754457903938, "results": "105", "hashOfConfig": "64"}, {"size": 688, "mtime": 1754457903982, "results": "106", "hashOfConfig": "64"}, {"size": 756, "mtime": 1754457903982, "results": "107", "hashOfConfig": "64"}, {"size": 13462, "mtime": 1754457903954, "results": "108", "hashOfConfig": "64"}, {"size": 25971, "mtime": 1754457903938, "results": "109", "hashOfConfig": "64"}, {"size": 1201, "mtime": 1754457903982, "results": "110", "hashOfConfig": "64"}, {"size": 398, "mtime": 1754457903921, "results": "111", "hashOfConfig": "64"}, {"size": 9364, "mtime": 1754457903938, "results": "112", "hashOfConfig": "64"}, {"size": 14104, "mtime": 1754457903954, "results": "113", "hashOfConfig": "64"}, {"size": 14127, "mtime": 1754457903938, "results": "114", "hashOfConfig": "64"}, {"size": 10932, "mtime": 1754457903938, "results": "115", "hashOfConfig": "64"}, {"size": 4023, "mtime": 1754457903954, "results": "116", "hashOfConfig": "64"}, {"size": 5490, "mtime": 1754457903954, "results": "117", "hashOfConfig": "64"}, {"size": 54967, "mtime": 1754457903972, "results": "118", "hashOfConfig": "64"}, {"size": 703, "mtime": 1754457903982, "results": "119", "hashOfConfig": "64"}, {"size": 1226, "mtime": 1754457903982, "results": "120", "hashOfConfig": "64"}, {"size": 6399, "mtime": 1754457903954, "results": "121", "hashOfConfig": "64"}, {"size": 1596, "mtime": 1754457903982, "results": "122", "hashOfConfig": "64"}, {"size": 15897, "mtime": 1754457903938, "results": "123", "hashOfConfig": "64"}, {"size": 4226, "mtime": 1754457903982, "results": "124", "hashOfConfig": "64"}, {"size": 191, "mtime": 1754457903921, "results": "125", "hashOfConfig": "64"}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1p0eeo1", {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\layout\\LayOut.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\EditReportPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Auth.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\CustomizeReport.jsx", ["312", "313", "314"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\Login.jsx", ["315"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\ResetPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\CheckEmail.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Auth\\Components\\ResetLink.jsx", ["316"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\examples\\Examples.jsx", ["317"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Dashboard\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\faqs\\Faqs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\QboCallback\\qboCallback.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\View.jsx", ["318", "319", "320", "321"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\shared-companies\\SharedCompanies.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\EditReport.jsx", ["322", "323", "324", "325", "326", "327", "328", "329", "330", "331"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\report.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\axiosInstance.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\pdf.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\example.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\faq.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\company.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\qbo.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\shared-components\\TopBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\ExpenseSummary.jsx", ["332"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\TableOfContents.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\MonthTrailing.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\LiquiditySummary.jsx", ["333"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\Monthly.jsx", ["334"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\ReportSummary.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\FiscalYear.jsx", ["335", "336", "337"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\OperationalEfficiency.jsx", ["338"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\YearToDate.jsx", ["339"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\BalanceSheet.jsx", ["340"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\CoverPage.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Companies.jsx", ["341"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\settings.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\Create.jsx", ["342", "343"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\utils\\shared.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\utils\\cookies.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\Reports.jsx", ["344", "345"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\Edit.jsx", ["346", "347", "348", "349"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\shared-components\\BreadCrumbs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\hooks\\useDebounceSearch.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\CSVModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\UploadableSection.jsx", ["350", "351", "352"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\ReportSettings.jsx", ["353", "354", "355", "356", "357", "358", "359", "360", "361", "362"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\QuickBooksConnection.jsx", ["363", "364", "365", "366", "367"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\shared-companies\\SharedCompaniesList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\shared-companies\\SharedCompaniesModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\reports\\ReportPages\\data.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\markets.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\services\\contentSettings.js", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\ShareUsersModal.jsx", ["368", "369"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\shared-components\\NoDataFound.jsx", ["370"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\pages\\Companies\\Components\\ReportModal.jsx", ["371", "372"], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\shared-components\\Table.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\JASON_NEW\\valisight_noGit\\frontend\\src\\enums\\report.enum.js", [], [], {"ruleId": "373", "severity": 1, "message": "374", "line": 48, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 48, "endColumn": 26}, {"ruleId": "373", "severity": 1, "message": "377", "line": 484, "column": 13, "nodeType": "375", "messageId": "376", "endLine": 484, "endColumn": 23}, {"ruleId": "378", "severity": 1, "message": "379", "line": 768, "column": 6, "nodeType": "380", "endLine": 768, "endColumn": 17, "suggestions": "381"}, {"ruleId": "373", "severity": 1, "message": "382", "line": 23, "column": 32, "nodeType": "375", "messageId": "376", "endLine": 23, "endColumn": 42}, {"ruleId": "373", "severity": 1, "message": "383", "line": 2, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 2, "endColumn": 21}, {"ruleId": "384", "severity": 1, "message": "385", "line": 363, "column": 21, "nodeType": "386", "endLine": 363, "endColumn": 66}, {"ruleId": "373", "severity": 1, "message": "387", "line": 16, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 16, "endColumn": 14}, {"ruleId": "373", "severity": 1, "message": "388", "line": 31, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 31, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "389", "line": 31, "column": 21, "nodeType": "375", "messageId": "376", "endLine": 31, "endColumn": 30}, {"ruleId": "373", "severity": 1, "message": "390", "line": 59, "column": 24, "nodeType": "375", "messageId": "376", "endLine": 59, "endColumn": 39}, {"ruleId": "391", "severity": 1, "message": "392", "line": 222, "column": 37, "nodeType": "393", "messageId": "394", "endLine": 232, "endColumn": 12}, {"ruleId": "391", "severity": 1, "message": "395", "line": 235, "column": 37, "nodeType": "393", "messageId": "394", "endLine": 376, "endColumn": 12}, {"ruleId": "373", "severity": 1, "message": "396", "line": 237, "column": 28, "nodeType": "375", "messageId": "376", "endLine": 237, "endColumn": 33}, {"ruleId": "373", "severity": 1, "message": "397", "line": 237, "column": 42, "nodeType": "375", "messageId": "376", "endLine": 237, "endColumn": 48}, {"ruleId": "398", "severity": 1, "message": "399", "line": 282, "column": 30, "nodeType": "400", "messageId": "401", "endLine": 282, "endColumn": 32}, {"ruleId": "398", "severity": 1, "message": "399", "line": 283, "column": 30, "nodeType": "400", "messageId": "401", "endLine": 283, "endColumn": 32}, {"ruleId": "398", "severity": 1, "message": "399", "line": 284, "column": 30, "nodeType": "400", "messageId": "401", "endLine": 284, "endColumn": 32}, {"ruleId": "398", "severity": 1, "message": "399", "line": 285, "column": 30, "nodeType": "400", "messageId": "401", "endLine": 285, "endColumn": 32}, {"ruleId": "378", "severity": 1, "message": "402", "line": 578, "column": 6, "nodeType": "380", "endLine": 578, "endColumn": 28, "suggestions": "403"}, {"ruleId": "373", "severity": 1, "message": "404", "line": 729, "column": 13, "nodeType": "375", "messageId": "376", "endLine": 729, "endColumn": 21}, {"ruleId": "378", "severity": 1, "message": "405", "line": 86, "column": 6, "nodeType": "380", "endLine": 86, "endColumn": 35, "suggestions": "406"}, {"ruleId": "378", "severity": 1, "message": "405", "line": 71, "column": 6, "nodeType": "380", "endLine": 71, "endColumn": 21, "suggestions": "407"}, {"ruleId": "373", "severity": 1, "message": "408", "line": 65, "column": 15, "nodeType": "375", "messageId": "376", "endLine": 65, "endColumn": 30}, {"ruleId": "378", "severity": 1, "message": "405", "line": 58, "column": 6, "nodeType": "380", "endLine": 58, "endColumn": 35, "suggestions": "409"}, {"ruleId": "398", "severity": 1, "message": "399", "line": 328, "column": 90, "nodeType": "400", "messageId": "401", "endLine": 328, "endColumn": 92}, {"ruleId": "398", "severity": 1, "message": "399", "line": 330, "column": 83, "nodeType": "400", "messageId": "401", "endLine": 330, "endColumn": 85}, {"ruleId": "378", "severity": 1, "message": "405", "line": 90, "column": 6, "nodeType": "380", "endLine": 90, "endColumn": 23, "suggestions": "410"}, {"ruleId": "373", "severity": 1, "message": "408", "line": 65, "column": 15, "nodeType": "375", "messageId": "376", "endLine": 65, "endColumn": 30}, {"ruleId": "373", "severity": 1, "message": "411", "line": 100, "column": 17, "nodeType": "375", "messageId": "376", "endLine": 100, "endColumn": 36}, {"ruleId": "378", "severity": 1, "message": "412", "line": 138, "column": 6, "nodeType": "380", "endLine": 138, "endColumn": 58, "suggestions": "413"}, {"ruleId": "373", "severity": 1, "message": "414", "line": 29, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 29, "endColumn": 15}, {"ruleId": "373", "severity": 1, "message": "415", "line": 58, "column": 26, "nodeType": "375", "messageId": "376", "endLine": 58, "endColumn": 43}, {"ruleId": "373", "severity": 1, "message": "416", "line": 46, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 46, "endColumn": 26}, {"ruleId": "378", "severity": 1, "message": "417", "line": 124, "column": 6, "nodeType": "380", "endLine": 124, "endColumn": 38, "suggestions": "418"}, {"ruleId": "373", "severity": 1, "message": "415", "line": 48, "column": 26, "nodeType": "375", "messageId": "376", "endLine": 48, "endColumn": 43}, {"ruleId": "373", "severity": 1, "message": "419", "line": 64, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 64, "endColumn": 25}, {"ruleId": "420", "severity": 1, "message": "421", "line": 614, "column": 21, "nodeType": "422", "messageId": "401", "endLine": 614, "endColumn": 36}, {"ruleId": "420", "severity": 1, "message": "423", "line": 615, "column": 21, "nodeType": "422", "messageId": "401", "endLine": 615, "endColumn": 32}, {"ruleId": "373", "severity": 1, "message": "424", "line": 78, "column": 13, "nodeType": "375", "messageId": "376", "endLine": 78, "endColumn": 20}, {"ruleId": "373", "severity": 1, "message": "425", "line": 79, "column": 13, "nodeType": "375", "messageId": "376", "endLine": 79, "endColumn": 17}, {"ruleId": "373", "severity": 1, "message": "426", "line": 80, "column": 13, "nodeType": "375", "messageId": "376", "endLine": 80, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "427", "line": 7, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 8}, {"ruleId": "373", "severity": 1, "message": "428", "line": 8, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 8, "endColumn": 10}, {"ruleId": "373", "severity": 1, "message": "429", "line": 10, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 10, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "430", "line": 11, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 11, "endColumn": 13}, {"ruleId": "373", "severity": 1, "message": "431", "line": 12, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 12, "endColumn": 10}, {"ruleId": "373", "severity": 1, "message": "432", "line": 26, "column": 19, "nodeType": "375", "messageId": "376", "endLine": 26, "endColumn": 31}, {"ruleId": "373", "severity": 1, "message": "433", "line": 27, "column": 19, "nodeType": "375", "messageId": "376", "endLine": 27, "endColumn": 27}, {"ruleId": "373", "severity": 1, "message": "434", "line": 28, "column": 26, "nodeType": "375", "messageId": "376", "endLine": 28, "endColumn": 35}, {"ruleId": "373", "severity": 1, "message": "435", "line": 29, "column": 19, "nodeType": "375", "messageId": "376", "endLine": 29, "endColumn": 27}, {"ruleId": "373", "severity": 1, "message": "436", "line": 38, "column": 9, "nodeType": "375", "messageId": "376", "endLine": 38, "endColumn": 14}, {"ruleId": "373", "severity": 1, "message": "437", "line": 10, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 10, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "431", "line": 18, "column": 3, "nodeType": "375", "messageId": "376", "endLine": 18, "endColumn": 10}, {"ruleId": "373", "severity": 1, "message": "433", "line": 24, "column": 11, "nodeType": "375", "messageId": "376", "endLine": 24, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "438", "line": 25, "column": 13, "nodeType": "375", "messageId": "376", "endLine": 25, "endColumn": 23}, {"ruleId": "373", "severity": 1, "message": "439", "line": 46, "column": 24, "nodeType": "375", "messageId": "376", "endLine": 46, "endColumn": 39}, {"ruleId": "373", "severity": 1, "message": "440", "line": 63, "column": 11, "nodeType": "375", "messageId": "376", "endLine": 63, "endColumn": 24}, {"ruleId": "378", "severity": 1, "message": "441", "line": 77, "column": 8, "nodeType": "380", "endLine": 77, "endColumn": 45, "suggestions": "442"}, {"ruleId": "373", "severity": 1, "message": "443", "line": 7, "column": 10, "nodeType": "375", "messageId": "376", "endLine": 7, "endColumn": 20}, {"ruleId": "398", "severity": 1, "message": "399", "line": 81, "column": 77, "nodeType": "400", "messageId": "401", "endLine": 81, "endColumn": 79}, {"ruleId": "398", "severity": 1, "message": "399", "line": 85, "column": 73, "nodeType": "400", "messageId": "401", "endLine": 85, "endColumn": 75}, "no-unused-vars", "'selectedTemplate' is assigned a value but never used.", "Identifier", "unusedVar", "'metricGrid' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchContentSettings' and 'initializeSettings'. Either include them or remove the dependency array.", "ArrayExpression", ["444"], "'rememberMe' is assigned a value but never used.", "'useNavigate' is defined but never used.", "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'Link' is defined but never used.", "'getCookie' is defined but never used.", "'setCookie' is defined but never used.", "'setSelectedFile' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'sectionHeaderX'.", "ArrowFunctionExpression", "unsafeRefs", "Function declared in a loop contains unsafe references to variable(s) 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'currentSection', 'sectionHeaderX', 'isReportSummaryHeading', 'hasInsertedLineBreak', 'styledHTML', 'hasInsertedLineBreak', 'styledHTML', 'currentSection', 'currentSection', 'isReportSummaryHeading', 'isReportSummaryHeading', 'styledHTML'.", "'skewX' is assigned a value but never used.", "'scaleY' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "React Hook React.useEffect has a missing dependency: 'report?.request_type'. Either include it or remove the dependency array.", ["445"], "'response' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'initializeCharts' and 'isDataLoaded'. Either include them or remove the dependency array.", ["446"], ["447"], "'variancePercent' is assigned a value but never used.", ["448"], ["449"], "'isNegativePriorYear' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCompanies'. Either include it or remove the dependency array.", ["450"], "'revokeAccess' is defined but never used.", "'setIsCreatedModal' is assigned a value but never used.", "'requestTypeLabels' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'companyReports'. Either include it or remove the dependency array.", ["451"], "'handleRemoveUser' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'backgroundColor'.", "ObjectExpression", "Duplicate key 'borderColor'.", "'minutes' is assigned a value but never used.", "'ampm' is assigned a value but never used.", "'displayHours' is assigned a value but never used.", "'Stack' is defined but never used.", "'Divider' is defined but never used.", "'Chip' is defined but never used.", "'IconButton' is defined but never used.", "'Tooltip' is defined but never used.", "'SettingsIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'CheckIcon' is defined but never used.", "'SaveIcon' is defined but never used.", "'theme' is assigned a value but never used.", "'Fade' is defined but never used.", "'LaunchIcon' is defined but never used.", "'setShowBenefits' is assigned a value but never used.", "'handleAddUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array. If 'fetchUsers' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["452"], "'dialogOpen' is assigned a value but never used.", {"desc": "453", "fix": "454"}, {"desc": "455", "fix": "456"}, {"desc": "457", "fix": "458"}, {"desc": "459", "fix": "460"}, {"desc": "461", "fix": "462"}, {"desc": "463", "fix": "464"}, {"desc": "465", "fix": "466"}, {"desc": "467", "fix": "468"}, {"desc": "469", "fix": "470"}, "Update the dependencies array to be: [companyId, fetchContentSettings, initializeSettings]", {"range": "471", "text": "472"}, "Update the dependencies array to be: [pdfUrl, report?.request_type, report.text]", {"range": "473", "text": "474"}, "Update the dependencies array to be: [reportData, contentSettings, isDataLoaded, initializeCharts]", {"range": "475", "text": "476"}, "Update the dependencies array to be: [initializeCharts, isDataLoaded, liquidityData]", {"range": "477", "text": "478"}, "Update the dependencies array to be: [fiscalData, contentSettings, isDataLoaded, initializeCharts]", {"range": "479", "text": "480"}, "Update the dependencies array to be: [initializeCharts, isDataLoaded, operationalData]", {"range": "481", "text": "482"}, "Update the dependencies array to be: [page, rowsPerPage, refresh, searchTerm, sortConfig, fetchCompanies]", {"range": "483", "text": "484"}, "Update the dependencies array to be: [companyReports, currentCompanyId, reportDetail]", {"range": "485", "text": "486"}, "Update the dependencies array to be: [page, itemsPerPage, debouncedSearch, fetchUsers]", {"range": "487", "text": "488"}, [29627, 29638], "[companyId, fetchContentSettings, initializeSettings]", [19774, 19796], "[pdfUrl, report?.request_type, report.text]", [3286, 3315], "[reportData, contentSettings, isDataLoaded, initializeCharts]", [2483, 2498], "[initializeCharts, isDataLoaded, liquidityData]", [2083, 2112], "[fiscalData, contentSettings, isDataLoaded, initializeCharts]", [4338, 4355], "[initializeCharts, isDataLoaded, operationalData]", [4476, 4528], "[page, rowsPerPage, refresh, searchTerm, sortConfig, fetchCompanies]", [3636, 3668], "[companyReports, currentCompanyId, reportDetail]", [2176, 2213], "[page, itemsPerPage, debouncedSearch, fetchUsers]"]