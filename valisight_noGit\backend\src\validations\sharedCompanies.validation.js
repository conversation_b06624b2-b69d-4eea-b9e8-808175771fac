import Joi from "joi";

export const sharedCompaniesValidation = {
    getAllUsers: Joi.object().keys({
        page: Joi.number().integer().min(1).default(1).messages({
            "number.base": "Page must be a number",
            "number.integer": "Page must be an integer"
        }),
        pageSize: Joi.number().integer().min(1).default(15).messages({
            "number.base": `"pageSize" must be a number`,
            "number.integer": `"pageSize" must be an integer`
        }),
        userId: Joi.number().integer().required().messages({
            "any.required": "userId is required",
            "number.base": "userId must be a number",
            "number.integer": "userId must be an integer"
        })
    }),
    addSharedUser: Joi.object().keys({
        userId: Joi.number().integer().required().messages({
            "any.required": "userId is required",
            "number.base": "userId must be a number",
            "number.integer": "userId must be an integer"
        })
    })
};
