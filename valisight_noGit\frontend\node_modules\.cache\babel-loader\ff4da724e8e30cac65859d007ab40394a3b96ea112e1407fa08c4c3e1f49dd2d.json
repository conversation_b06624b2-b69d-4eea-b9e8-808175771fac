{"ast": null, "code": "// frontend\\src\\services\\qbo.js\nimport axiosInstance from \"./axiosInstance\";\nexport const connectQBO = async companyId => {\n  return await axiosInstance.get(`/qbo/connect?companyId=${companyId}`);\n};\nexport const getQBOStatus = async companyId => {\n  return await axiosInstance.get(`/qbo/status?companyId=${companyId}`);\n};\nexport const disconnectQBO = async companyId => {\n  return await axiosInstance.post(`/qbo/disconnect?companyId=${companyId}`);\n};\nexport const syncAccounts = async data => {\n  return await axiosInstance.post(`/qbo/sync/accounts`, data);\n};\nexport const syncTrialBalance = async data => {\n  return await axiosInstance.post(`/qbo/sync/trial-balance`, data);\n};\nexport const syncProfitLoss = async data => {\n  return await axiosInstance.post(`/qbo/sync/profit-loss`, data);\n};\nexport const syncBalanceSheet = async data => {\n  return await axiosInstance.post(`/qbo/sync/balance-sheet`, data);\n};\nexport const syncAPAging = async data => {\n  return await axiosInstance.post(`/qbo/sync/ap-aging`, data);\n};\nexport const syncARAging = async data => {\n  return await axiosInstance.post(`/qbo/sync/ar-aging`, data);\n};", "map": {"version": 3, "names": ["axiosInstance", "connectQBO", "companyId", "get", "getQBOStatus", "disconnectQBO", "post", "syncAccounts", "data", "syncTrialBalance", "syncProfitLoss", "syncBalanceSheet", "syncAPAging", "syncARAging"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/services/qbo.js"], "sourcesContent": ["// frontend\\src\\services\\qbo.js\r\nimport axiosInstance from \"./axiosInstance\";\r\n\r\nexport const connectQBO = async (companyId) => {\r\n  return await axiosInstance.get(`/qbo/connect?companyId=${companyId}`);\r\n};\r\n\r\nexport const getQBOStatus = async (companyId) => {\r\n  return await axiosInstance.get(`/qbo/status?companyId=${companyId}`);\r\n};\r\n\r\nexport const disconnectQBO = async (companyId) => {\r\n  return await axiosInstance.post(`/qbo/disconnect?companyId=${companyId}`);\r\n};\r\nexport const syncAccounts = async (data) => {\r\n  return await axiosInstance.post(`/qbo/sync/accounts`, data);\r\n};\r\n\r\nexport const syncTrialBalance = async (data) => {\r\n  return await axiosInstance.post(`/qbo/sync/trial-balance`, data);\r\n};\r\n\r\nexport const syncProfitLoss = async (data) => {\r\n  return await axiosInstance.post(`/qbo/sync/profit-loss`, data);\r\n};\r\n\r\nexport const syncBalanceSheet = async (data) => {\r\n  return await axiosInstance.post(`/qbo/sync/balance-sheet`, data);\r\n};\r\n\r\nexport const syncAPAging = async (data) => {\r\n  return await axiosInstance.post(`/qbo/sync/ap-aging`, data);\r\n};\r\n\r\nexport const syncARAging = async (data) => {\r\n  return await axiosInstance.post(`/qbo/sync/ar-aging`, data);\r\n};\r\n"], "mappings": "AAAA;AACA,OAAOA,aAAa,MAAM,iBAAiB;AAE3C,OAAO,MAAMC,UAAU,GAAG,MAAOC,SAAS,IAAK;EAC7C,OAAO,MAAMF,aAAa,CAACG,GAAG,CAAC,0BAA0BD,SAAS,EAAE,CAAC;AACvE,CAAC;AAED,OAAO,MAAME,YAAY,GAAG,MAAOF,SAAS,IAAK;EAC/C,OAAO,MAAMF,aAAa,CAACG,GAAG,CAAC,yBAAyBD,SAAS,EAAE,CAAC;AACtE,CAAC;AAED,OAAO,MAAMG,aAAa,GAAG,MAAOH,SAAS,IAAK;EAChD,OAAO,MAAMF,aAAa,CAACM,IAAI,CAAC,6BAA6BJ,SAAS,EAAE,CAAC;AAC3E,CAAC;AACD,OAAO,MAAMK,YAAY,GAAG,MAAOC,IAAI,IAAK;EAC1C,OAAO,MAAMR,aAAa,CAACM,IAAI,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AAC7D,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG,MAAOD,IAAI,IAAK;EAC9C,OAAO,MAAMR,aAAa,CAACM,IAAI,CAAC,yBAAyB,EAAEE,IAAI,CAAC;AAClE,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAOF,IAAI,IAAK;EAC5C,OAAO,MAAMR,aAAa,CAACM,IAAI,CAAC,uBAAuB,EAAEE,IAAI,CAAC;AAChE,CAAC;AAED,OAAO,MAAMG,gBAAgB,GAAG,MAAOH,IAAI,IAAK;EAC9C,OAAO,MAAMR,aAAa,CAACM,IAAI,CAAC,yBAAyB,EAAEE,IAAI,CAAC;AAClE,CAAC;AAED,OAAO,MAAMI,WAAW,GAAG,MAAOJ,IAAI,IAAK;EACzC,OAAO,MAAMR,aAAa,CAACM,IAAI,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AAC7D,CAAC;AAED,OAAO,MAAMK,WAAW,GAAG,MAAOL,IAAI,IAAK;EACzC,OAAO,MAAMR,aAAa,CAACM,IAAI,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AAC7D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}