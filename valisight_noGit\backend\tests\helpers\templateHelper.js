import { prisma } from "../../src/db/prisma.js";
export const customTemplate  = async ()=>{
        // Create a new test report
        let template = await prisma.templates.findMany({
            where:{
                OR:[
                    {name:"Test Template"},
                    {name:"Multi-File Template"}
                ]
            },
            select:{
                id:true
            }
        })
        
        return template[0].id

}



export const deleteCustomTemplate = async ()=>{
    await prisma.templates.deleteMany({
        where:{
            OR:[
                {name:"Test Template"},
                {name:"Multi-File Template"}
            ]
        }
    })
}
