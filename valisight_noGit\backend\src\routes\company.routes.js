import { Router } from 'express';
import * as companyController from '../controllers/company.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';
export const companyRoute = Router();

companyRoute.post('/', authenticate, companyController.registerCompany);
companyRoute.get('/all', authenticate, companyController.getAllCompanies);
companyRoute.get('/:id', authenticate, companyController.getCompanyDetail);
companyRoute.put('/:id', authenticate, companyController.updateCompanyDetail); //update company Detail
companyRoute.delete('/:id', authenticate, companyController.deleteCompany); //update company Detail

companyRoute.post(
  '/:id/upload-file',
  authenticate,
  companyController.uploadCompanyInfoFiles,
);

companyRoute.get(
  '/location',
  authenticate,
  companyController.getCompanyLocation,
);

