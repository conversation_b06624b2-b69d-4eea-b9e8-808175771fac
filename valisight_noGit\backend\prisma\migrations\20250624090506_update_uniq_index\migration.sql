/*
  Warnings:

  - A unique constraint covering the columns `[userId,realmId,year,month,accountId]` on the table `ProfitLossReport` will be added. If there are existing duplicate values, this will fail.
  - Made the column `createdAt` on table `Account` required. This step will fail if there are existing NULL values in that column.
  - Made the column `modifiedAt` on table `Account` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `AccountPayableAgingSummaryReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `modifiedAt` on table `AccountPayableAgingSummaryReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `AccountReceivableAgingSummaryReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `modifiedAt` on table `AccountReceivableAgingSummaryReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `BalanceSheetReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `modifiedAt` on table `BalanceSheetReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `ProfitLossReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `modifiedAt` on table `ProfitLossReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `createdAt` on table `TrialBalanceReport` required. This step will fail if there are existing NULL values in that column.
  - Made the column `modifiedAt` on table `TrialBalanceReport` required. This step will fail if there are existing NULL values in that column.

*/
-- DropIndex
DROP INDEX "ProfitLossReport_userId_realmId_year_month_key";

-- AlterTable
ALTER TABLE "Account" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "modifiedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "AccountPayableAgingSummaryReport" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "modifiedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "AccountReceivableAgingSummaryReport" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "modifiedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "BalanceSheetReport" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "modifiedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "ProfitLossReport" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "modifiedAt" SET NOT NULL;

-- AlterTable
ALTER TABLE "TrialBalanceReport" ALTER COLUMN "createdAt" SET NOT NULL,
ALTER COLUMN "modifiedAt" SET NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ProfitLossReport_userId_realmId_year_month_accountId_key" ON "ProfitLossReport"("userId", "realmId", "year", "month", "accountId");
