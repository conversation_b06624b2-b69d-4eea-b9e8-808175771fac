// tests/helpers/authHelper.js

import request from "supertest";
import { app } from "../../src/index.js";
import { generateAuthTokens } from "../../src/middleware/auth.middleware.js";
import { prisma } from "../../src/db/prisma.js";
let authToken = null; // Store the token globally

before(async function () {
    if (!authToken) {
        console.log("Logging in..."); // This should appear only once
        const res = await request(app).post("/api/v1/auth/login").send({
            email: "<EMAIL>",
            password: "securepassword"
        });
        
        if (res._body.token) {
            authToken = res.body.token; // Store token globally
            return authToken
        }

        const response = await request(app).post("/api/v1/auth/register").send({
            username: "companyTestUser",
            email: "<EMAIL>",
            password: "securepassword",
            isAdmin:false
        });
        
        authToken = await generateAuthTokens(response._body.user.id)
        
        return authToken

    }
});


export const getcompanyId = async ()=>{
    let existenceCompany = await prisma.company.findFirst({
        where:{
            name:"Test Company"
        }
    })    
    return existenceCompany.id
}

export const getRandomCompanyId = async () => {
    // Check if "Test Company" already exists
    let existenceCompany = await prisma.company.findFirst({
      where: {
        name: "Test Company",
      },
      select: {
        id: true,
        companyReports:{
            select:{
                id:true
            }
        }
      },
    });
  
    // If company exists, return its ID
    if (existenceCompany) {
      return existenceCompany;
    }
  
    // If not, create "Test Company"
    const res = await request(app)
      .post(`/api/v1/company/`)
      .set("Authorization", `Bearer ${authToken}`)
      .send({
        name: "Test Company",
        fiscal_year_end: "2025-03-31",
        country: "USA",
        description: "A test company",
        naics: "12345",
        state: "Florida",
        city: "Anna Maria",
        users: [],
      });
  
    // Return the newly created company's ID
    return res._body.company;
  };
  

export const getToken = () => authToken;


