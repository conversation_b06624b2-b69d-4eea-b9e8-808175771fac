{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\faqs\\\\Faqs.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport RemoveCircleOutlineIcon from \"@mui/icons-material/RemoveCircleOutline\";\nimport AddCircleOutlineIcon from \"@mui/icons-material/AddCircleOutline\";\nimport { getAllFaqs } from \"../../services/faq\";\nimport CircularProgress from \"@mui/material/CircularProgress\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FAQs = () => {\n  _s();\n  const [openIndex, setOpenIndex] = useState(null);\n  const [faqs, setFaqs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    const getAll = async () => {\n      setLoading(true);\n      try {\n        var _result$data;\n        const result = await getAllFaqs();\n        if (result !== null && result !== void 0 && (_result$data = result.data) !== null && _result$data !== void 0 && _result$data.success && result.data.faqs.length > 0) {\n          setFaqs(result.data.faqs);\n        } else {\n          setFaqs([]);\n        }\n      } catch (error) {\n        console.error(\"Error fetching FAQs:\", error);\n        setFaqs([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    getAll();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-3xl mx-auto px-4 font-inter\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-[30px] font-semibold mb-2 mt-8\",\n        children: \"FAQs\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-[#2E3A44] text-[16px]\",\n        children: \"Need something cleared up? Here are our most frequently asked questions.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 9\n    }, this) : faqs.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-gray-600 text-[16px]\",\n      children: \"No FAQs available.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: faqs.map((faq, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `rounded-lg overflow-hidden ${openIndex === index ? \"bg-[#EFF3F6]\" : \"\"}`,\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full px-6 py-4 flex items-center gap-4 hover:bg-[#EFF3F6] transition-colors\",\n          onClick: () => setOpenIndex(openIndex === index ? null : index),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 flex items-center\",\n            children: openIndex === index ? /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {\n              className: \"text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n              className: \"text-gray-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-left text-[18px] font-medium flex-grow text-[#151F27]\",\n            children: faq.question\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 15\n        }, this), openIndex === index && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-16 pb-4 transition-all\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-[16px]\",\n            children: faq.answer\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 17\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(FAQs, \"cSz5FVdpWBPRJZsRR4ggEEM7cf0=\");\n_c = FAQs;\nexport default FAQs;\nvar _c;\n$RefreshReg$(_c, \"FAQs\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "RemoveCircleOutlineIcon", "AddCircleOutlineIcon", "getAllFaqs", "CircularProgress", "jsxDEV", "_jsxDEV", "FAQs", "_s", "openIndex", "setOpenIndex", "faqs", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "getAll", "_result$data", "result", "data", "success", "length", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "faq", "index", "onClick", "question", "answer", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/faqs/Faqs.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport RemoveCircleOutlineIcon from \"@mui/icons-material/RemoveCircleOutline\";\r\nimport AddCircleOutlineIcon from \"@mui/icons-material/AddCircleOutline\";\r\nimport { getAllFaqs } from \"../../services/faq\";\r\nimport CircularProgress from \"@mui/material/CircularProgress\"; \r\n\r\nconst FAQs = () => {\r\n  const [openIndex, setOpenIndex] = useState(null);\r\n  const [faqs, setFaqs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const getAll = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const result = await getAllFaqs();\r\n        if (result?.data?.success && result.data.faqs.length > 0) {\r\n          setFaqs(result.data.faqs);\r\n        } else {\r\n          setFaqs([]); \r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching FAQs:\", error);\r\n        setFaqs([]); \r\n      } finally {\r\n        setLoading(false); \r\n      }\r\n    };\r\n\r\n    getAll();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"max-w-3xl mx-auto px-4 font-inter\">\r\n      <div className=\"text-center mb-8\">\r\n        <h1 className=\"text-[30px] font-semibold mb-2 mt-8\">FAQs</h1>\r\n        <p className=\"text-[#2E3A44] text-[16px]\">\r\n          Need something cleared up? Here are our most frequently asked\r\n          questions.\r\n        </p>\r\n      </div>\r\n\r\n      {loading ? (\r\n        <div className=\"flex justify-center items-center\">\r\n          <CircularProgress />\r\n        </div>\r\n      ) : faqs.length === 0 ? (\r\n        <div className=\"text-center text-gray-600 text-[16px]\">\r\n          No FAQs available.\r\n        </div>\r\n      ) : (\r\n        <div className=\"space-y-6\">\r\n          {faqs.map((faq, index) => (\r\n            <div\r\n              key={index}\r\n              className={`rounded-lg overflow-hidden ${\r\n                openIndex === index ? \"bg-[#EFF3F6]\" : \"\"\r\n              }`}\r\n            >\r\n              <button\r\n                className=\"w-full px-6 py-4 flex items-center gap-4 hover:bg-[#EFF3F6] transition-colors\"\r\n                onClick={() => setOpenIndex(openIndex === index ? null : index)}\r\n              >\r\n                <div className=\"w-6 h-6 flex items-center\">\r\n                  {openIndex === index ? (\r\n                    <RemoveCircleOutlineIcon className=\"text-gray-600\" />\r\n                  ) : (\r\n                    <AddCircleOutlineIcon className=\"text-gray-600\" />\r\n                  )}\r\n                </div>\r\n                <span className=\"text-left text-[18px] font-medium flex-grow text-[#151F27]\">\r\n                  {faq.question}\r\n                </span>\r\n              </button>\r\n\r\n              {openIndex === index && (\r\n                <div className=\"px-16 pb-4 transition-all\">\r\n                  <p className=\"text-gray-600 text-[16px]\">{faq.answer}</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FAQs;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAOC,gBAAgB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACW,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACd,MAAMgB,MAAM,GAAG,MAAAA,CAAA,KAAY;MACzBD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QAAA,IAAAE,YAAA;QACF,MAAMC,MAAM,GAAG,MAAMd,UAAU,CAAC,CAAC;QACjC,IAAIc,MAAM,aAANA,MAAM,gBAAAD,YAAA,GAANC,MAAM,CAAEC,IAAI,cAAAF,YAAA,eAAZA,YAAA,CAAcG,OAAO,IAAIF,MAAM,CAACC,IAAI,CAACP,IAAI,CAACS,MAAM,GAAG,CAAC,EAAE;UACxDR,OAAO,CAACK,MAAM,CAACC,IAAI,CAACP,IAAI,CAAC;QAC3B,CAAC,MAAM;UACLC,OAAO,CAAC,EAAE,CAAC;QACb;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CT,OAAO,CAAC,EAAE,CAAC;MACb,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,MAAM,CAAC,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,oBACET,OAAA;IAAKiB,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDlB,OAAA;MAAKiB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BlB,OAAA;QAAIiB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7DtB,OAAA;QAAGiB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAG1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAELf,OAAO,gBACNP,OAAA;MAAKiB,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/ClB,OAAA,CAACF,gBAAgB;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,GACJjB,IAAI,CAACS,MAAM,KAAK,CAAC,gBACnBd,OAAA;MAAKiB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EAAC;IAEvD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,gBAENtB,OAAA;MAAKiB,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBb,IAAI,CAACkB,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACnBzB,OAAA;QAEEiB,SAAS,EAAE,8BACTd,SAAS,KAAKsB,KAAK,GAAG,cAAc,GAAG,EAAE,EACxC;QAAAP,QAAA,gBAEHlB,OAAA;UACEiB,SAAS,EAAC,+EAA+E;UACzFS,OAAO,EAAEA,CAAA,KAAMtB,YAAY,CAACD,SAAS,KAAKsB,KAAK,GAAG,IAAI,GAAGA,KAAK,CAAE;UAAAP,QAAA,gBAEhElB,OAAA;YAAKiB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EACvCf,SAAS,KAAKsB,KAAK,gBAClBzB,OAAA,CAACL,uBAAuB;cAACsB,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAErDtB,OAAA,CAACJ,oBAAoB;cAACqB,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAClD;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNtB,OAAA;YAAMiB,SAAS,EAAC,4DAA4D;YAAAC,QAAA,EACzEM,GAAG,CAACG;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAERnB,SAAS,KAAKsB,KAAK,iBAClBzB,OAAA;UAAKiB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxClB,OAAA;YAAGiB,SAAS,EAAC,2BAA2B;YAAAC,QAAA,EAAEM,GAAG,CAACI;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN;MAAA,GAzBIG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0BP,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpB,EAAA,CAhFID,IAAI;AAAA4B,EAAA,GAAJ5B,IAAI;AAkFV,eAAeA,IAAI;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}