{"ast": null, "code": "\"use strict\";\n\nexports.getTimezoneOffsetInMilliseconds = getTimezoneOffsetInMilliseconds;\nvar _index = require(\"../toDate.js\");\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nfunction getTimezoneOffsetInMilliseconds(date) {\n  const _date = (0, _index.toDate)(date);\n  const utcDate = new Date(Date.UTC(_date.getFullYear(), _date.getMonth(), _date.getDate(), _date.getHours(), _date.getMinutes(), _date.getSeconds(), _date.getMilliseconds()));\n  utcDate.setUTCFullYear(_date.getFullYear());\n  return +date - +utcDate;\n}", "map": {"version": 3, "names": ["exports", "getTimezoneOffsetInMilliseconds", "_index", "require", "date", "_date", "toDate", "utcDate", "Date", "UTC", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "getMilliseconds", "setUTCFullYear"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds.js"], "sourcesContent": ["\"use strict\";\nexports.getTimezoneOffsetInMilliseconds = getTimezoneOffsetInMilliseconds;\nvar _index = require(\"../toDate.js\");\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nfunction getTimezoneOffsetInMilliseconds(date) {\n  const _date = (0, _index.toDate)(date);\n  const utcDate = new Date(\n    Date.UTC(\n      _date.getFullYear(),\n      _date.getMonth(),\n      _date.getDate(),\n      _date.getHours(),\n      _date.getMinutes(),\n      _date.getSeconds(),\n      _date.getMilliseconds(),\n    ),\n  );\n  utcDate.setUTCFullYear(_date.getFullYear());\n  return +date - +utcDate;\n}\n"], "mappings": "AAAA,YAAY;;AACZA,OAAO,CAACC,+BAA+B,GAAGA,+BAA+B;AACzE,IAAIC,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,+BAA+BA,CAACG,IAAI,EAAE;EAC7C,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEH,MAAM,CAACI,MAAM,EAAEF,IAAI,CAAC;EACtC,MAAMG,OAAO,GAAG,IAAIC,IAAI,CACtBA,IAAI,CAACC,GAAG,CACNJ,KAAK,CAACK,WAAW,CAAC,CAAC,EACnBL,KAAK,CAACM,QAAQ,CAAC,CAAC,EAChBN,KAAK,CAACO,OAAO,CAAC,CAAC,EACfP,KAAK,CAACQ,QAAQ,CAAC,CAAC,EAChBR,KAAK,CAACS,UAAU,CAAC,CAAC,EAClBT,KAAK,CAACU,UAAU,CAAC,CAAC,EAClBV,KAAK,CAACW,eAAe,CAAC,CACxB,CACF,CAAC;EACDT,OAAO,CAACU,cAAc,CAACZ,KAAK,CAACK,WAAW,CAAC,CAAC,CAAC;EAC3C,OAAO,CAACN,IAAI,GAAG,CAACG,OAAO;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}