{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\YearToDate.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfitLossYTDDashboard = ({\n  headerTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  reportData = null // Add reportData prop to receive API data\n}) => {\n  // Extract background color from headerTextStyle for table header\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\n\n  // Function to transform API data into the required format\n  const transformApiData = apiData => {\n    if (!apiData || !apiData.profitAndLossMonthsYTD) {\n      return {\n        tableData: [],\n        monthInfo: {\n          current: 'Jan 25',\n          previous: 'Jan 24'\n        }\n      };\n    }\n    const rawData = apiData.profitAndLossMonthsYTD;\n\n    // Extract month information from the first item's keys\n    let currentMonth = 'Jan 25';\n    let previousMonth = 'Jan 24';\n    if (rawData.length > 0) {\n      const keys = Object.keys(rawData[0]);\n      const actualsKey = keys.find(key => key.includes('_YTD_Actuals'));\n      if (actualsKey) {\n        const monthMatch = actualsKey.match(/([A-Za-z]+_\\d+)_YTD_Actuals/);\n        if (monthMatch) {\n          currentMonth = monthMatch[1].replace('_', ' ');\n          // Assume previous year is same month but year-1\n          const [month, year] = currentMonth.split(' ');\n          previousMonth = `${month} ${parseInt(year) - 1}`;\n        }\n      }\n    }\n\n    // Group data by account_type (or accountClassification if needed)\n    const groupedData = rawData.reduce((acc, item) => {\n      const accountType = item.account_type || item.accountClassification;\n      if (!acc[accountType]) {\n        acc[accountType] = [];\n      }\n      acc[accountType].push(item);\n      return acc;\n    }, {});\n\n    // Transform grouped data into table format\n    const tableData = [];\n    Object.keys(groupedData).forEach(accountType => {\n      // Add header row for account type\n      tableData.push({\n        isHeader: true,\n        category: accountType\n      });\n\n      // Add data rows for this account type\n      groupedData[accountType].forEach(item => {\n        // Check if this is a total row (contains \"Total\" in account_name)\n        const isTotal = item.account_name.toLowerCase().includes('total');\n\n        // Determine if variance is positive or negative for styling\n        const varianceAmount = parseFloat(item.Variance_Amount || '0');\n        const variancePercent = parseFloat(item.Variance_Percentage || '0');\n        const isPositive = varianceAmount > 0;\n        const isNegative = varianceAmount < 0;\n        tableData.push({\n          label: item.account_name,\n          jan25: item.Apr_25_YTD_Actuals || '0',\n          // Using Apr as example, adjust field names as needed\n          jan25Percent: item.Apr_25_Percent_of_Income || '0%',\n          jan24: item.Apr_24_YTD_Prior_Year || '0',\n          jan24Percent: item.Apr_24_Percent_of_Income || '0%',\n          variance: item.Variance_Amount || '0',\n          variancePercent: item.Variance_Percentage || '0%',\n          isTotal: isTotal,\n          indented: !isTotal && !item.account_name.toLowerCase().includes('total'),\n          isPositive: isPositive,\n          isNegative: isNegative\n        });\n      });\n    });\n    return {\n      tableData,\n      monthInfo: {\n        current: currentMonth,\n        previous: previousMonth\n      }\n    };\n  };\n\n  // Transform the API data\n  const {\n    tableData,\n    monthInfo\n  } = transformApiData(reportData);\n\n  // Fallback to empty state if no data\n  if (!reportData || !reportData.profitAndLossMonthsYTD || tableData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen p-5 section profit-loss-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"report-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter',\n              color: \"black\"\n            },\n            children: \"Profit and Loss\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              ...contentTextStyle,\n              fontSize: \"20px\"\n            },\n            children: \"Year to Date Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              ...contentTextStyle,\n              fontSize: \"20px\",\n              fontWeight: \"100\"\n            },\n            children: \"Acme Print\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-gray-600\",\n              children: \"No data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500 mt-2\",\n              children: \"Please check your data source\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this);\n  }\n  const renderTableRow = (item, index) => {\n    if (item.isHeader) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"font-bold text-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-normal\",\n          style: contentTextStyle,\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this);\n    }\n    if (item.isTotal) {\n      return /*#__PURE__*/_jsxDEV(\"tr\", {\n        className: \"border-t-2 border-gray\",\n        children: [/*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-left pl-2 font-normal\",\n          style: contentTextStyle,\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: contentTextStyle,\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan25\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          style: contentTextStyle,\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan25Percent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right font-mono\",\n          style: contentTextStyle,\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: \"text-right\",\n          style: contentTextStyle,\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.jan24Percent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: `text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`,\n          style: contentTextStyle,\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.variance\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n          className: `text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`,\n          style: contentTextStyle,\n          children: /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: item.variancePercent\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"tr\", {\n      children: [/*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`,\n        style: contentTextStyle,\n        children: item.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: contentTextStyle,\n        children: item.jan25\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right\",\n        style: contentTextStyle,\n        children: item.jan25Percent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right font-mono\",\n        style: contentTextStyle,\n        children: item.jan24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: \"text-right\",\n        style: contentTextStyle,\n        children: item.jan24Percent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`,\n        style: contentTextStyle,\n        children: item.variance\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n        className: `text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`,\n        style: contentTextStyle,\n        children: item.variancePercent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, index, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Generate dynamic YTD date description\n  const getYTDDescription = () => {\n    if (monthInfo.current) {\n      const [month, year] = monthInfo.current.split(' ');\n      const monthNames = {\n        'Jan': 'January',\n        'Feb': 'February',\n        'Mar': 'March',\n        'Apr': 'April',\n        'May': 'May',\n        'Jun': 'June',\n        'Jul': 'July',\n        'Aug': 'August',\n        'Sep': 'September',\n        'Oct': 'October',\n        'Nov': 'November',\n        'Dec': 'December'\n      };\n      const fullMonth = monthNames[month] || month;\n      return `Year to Date As of ${fullMonth} 31st, 20${year}`;\n    }\n    return 'Year to Date As of January 31st, 2025';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5 section profit-loss-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"report-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-light text-gray-800 m-0\",\n          style: {\n            ...subHeadingTextStyle,\n            fontWeight: 'lighter',\n            color: \"black\"\n          },\n          children: \"Profit and Loss\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xl text-gray-800\",\n          style: {\n            ...contentTextStyle,\n            fontSize: \"20px\"\n          },\n          children: getYTDDescription()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            ...contentTextStyle,\n            fontSize: \"20px\",\n            fontWeight: \"100\"\n          },\n          children: \"Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full border-collapse text-sm mt-4 profit-loss-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"table-header-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"text-black text-center bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0\",\n                style: {\n                  ...contentTextStyle,\n                  color: 'black'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0 pl-10\",\n                style: {\n                  ...contentTextStyle,\n                  color: 'black'\n                },\n                colSpan: \"2\",\n                children: monthInfo.current\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0 pl-10\",\n                style: {\n                  ...contentTextStyle,\n                  color: 'black'\n                },\n                colSpan: \"2\",\n                children: monthInfo.previous\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-left bg-white border-0\",\n                style: {\n                  ...contentTextStyle,\n                  color: 'black'\n                },\n                colSpan: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-right text-white p-2 font-bold text-sm\",\n                style: {\n                  ...contentTextStyle,\n                  color: 'white'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  color: 'white'\n                },\n                children: \"Actuals\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  color: 'white',\n                  borderRight: '20px solid white'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"% of Income\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-1 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  color: 'white'\n                },\n                children: \"Prior Year\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-1 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  color: 'white',\n                  borderRight: '20px solid white'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: \"% of Income\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  color: 'white'\n                },\n                children: \"$ Variance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"text-center text-white p-2 font-bold text-sm\",\n                style: {\n                  backgroundColor: headerBgColor,\n                  ...contentTextStyle,\n                  color: 'white'\n                },\n                children: \"% Variance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 14\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"table-row-group\",\n            children: tableData.map((item, index) => renderTableRow(item, index))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or delivered information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 5\n  }, this);\n};\n_c = ProfitLossYTDDashboard;\nexport default ProfitLossYTDDashboard;\nvar _c;\n$RefreshReg$(_c, \"ProfitLossYTDDashboard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ProfitLossYTDDashboard", "headerTextStyle", "subHeadingTextStyle", "contentTextStyle", "reportData", "headerBgColor", "color", "transformApiData", "apiData", "profitAndLossMonthsYTD", "tableData", "monthInfo", "current", "previous", "rawData", "currentMonth", "previousMonth", "length", "keys", "Object", "actualsKey", "find", "key", "includes", "monthMatch", "match", "replace", "month", "year", "split", "parseInt", "groupedData", "reduce", "acc", "item", "accountType", "account_type", "accountClassification", "push", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "category", "isTotal", "account_name", "toLowerCase", "varianceAmount", "parseFloat", "Variance_Amount", "variancePercent", "Variance_Percentage", "isPositive", "isNegative", "label", "jan25", "Apr_25_YTD_Actuals", "jan25<PERSON><PERSON><PERSON>", "Apr_25_Percent_of_Income", "jan24", "Apr_24_YTD_Prior_Year", "jan24<PERSON>ercent", "Apr_24_Percent_of_Income", "variance", "indented", "className", "children", "style", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "renderTableRow", "index", "getYTDDescription", "monthNames", "fullMonth", "colSpan", "backgroundColor", "borderRight", "map", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/reports/ReportPages/YearToDate.jsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ProfitLossYTDDashboard = ({ \r\n  headerTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  reportData = null // Add reportData prop to receive API data\r\n}) => {\r\n  // Extract background color from headerTextStyle for table header\r\n  const headerBgColor = headerTextStyle.color || '#20b2aa';\r\n\r\n  // Function to transform API data into the required format\r\n  const transformApiData = (apiData) => {\r\n    if (!apiData || !apiData.profitAndLossMonthsYTD) {\r\n      return { tableData: [], monthInfo: { current: 'Jan 25', previous: 'Jan 24' } };\r\n    }\r\n\r\n    const rawData = apiData.profitAndLossMonthsYTD;\r\n    \r\n    // Extract month information from the first item's keys\r\n    let currentMonth = 'Jan 25';\r\n    let previousMonth = 'Jan 24';\r\n    \r\n    if (rawData.length > 0) {\r\n      const keys = Object.keys(rawData[0]);\r\n      const actualsKey = keys.find(key => key.includes('_YTD_Actuals'));\r\n      if (actualsKey) {\r\n        const monthMatch = actualsKey.match(/([A-Za-z]+_\\d+)_YTD_Actuals/);\r\n        if (monthMatch) {\r\n          currentMonth = monthMatch[1].replace('_', ' ');\r\n          // Assume previous year is same month but year-1\r\n          const [month, year] = currentMonth.split(' ');\r\n          previousMonth = `${month} ${parseInt(year) - 1}`;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Group data by account_type (or accountClassification if needed)\r\n    const groupedData = rawData.reduce((acc, item) => {\r\n      const accountType = item.account_type || item.accountClassification;\r\n      if (!acc[accountType]) {\r\n        acc[accountType] = [];\r\n      }\r\n      acc[accountType].push(item);\r\n      return acc;\r\n    }, {});\r\n\r\n    // Transform grouped data into table format\r\n    const tableData = [];\r\n    \r\n    Object.keys(groupedData).forEach(accountType => {\r\n      // Add header row for account type\r\n      tableData.push({\r\n        isHeader: true,\r\n        category: accountType\r\n      });\r\n\r\n      // Add data rows for this account type\r\n      groupedData[accountType].forEach(item => {\r\n        // Check if this is a total row (contains \"Total\" in account_name)\r\n        const isTotal = item.account_name.toLowerCase().includes('total');\r\n        \r\n        // Determine if variance is positive or negative for styling\r\n        const varianceAmount = parseFloat(item.Variance_Amount || '0');\r\n        const variancePercent = parseFloat(item.Variance_Percentage || '0');\r\n        const isPositive = varianceAmount > 0;\r\n        const isNegative = varianceAmount < 0;\r\n        \r\n        tableData.push({\r\n          label: item.account_name,\r\n          jan25: item.Apr_25_YTD_Actuals || '0', // Using Apr as example, adjust field names as needed\r\n          jan25Percent: item.Apr_25_Percent_of_Income || '0%',\r\n          jan24: item.Apr_24_YTD_Prior_Year || '0',\r\n          jan24Percent: item.Apr_24_Percent_of_Income || '0%',\r\n          variance: item.Variance_Amount || '0',\r\n          variancePercent: item.Variance_Percentage || '0%',\r\n          isTotal: isTotal,\r\n          indented: !isTotal && !item.account_name.toLowerCase().includes('total'),\r\n          isPositive: isPositive,\r\n          isNegative: isNegative\r\n        });\r\n      });\r\n    });\r\n\r\n    return { \r\n      tableData, \r\n      monthInfo: { \r\n        current: currentMonth, \r\n        previous: previousMonth \r\n      } \r\n    };\r\n  };\r\n\r\n  // Transform the API data\r\n  const { tableData, monthInfo } = transformApiData(reportData);\r\n\r\n  // Fallback to empty state if no data\r\n  if (!reportData || !reportData.profitAndLossMonthsYTD || tableData.length === 0) {\r\n    return (\r\n      <div className=\"min-h-screen p-5 section profit-loss-section\">\r\n        <div className=\"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container\">\r\n          \r\n          \r\n          <div className=\"report-header\">\r\n            <h2 style={{ ...subHeadingTextStyle, fontWeight: 'lighter', color: \"black\" }}>\r\n              Profit and Loss\r\n            </h2>\r\n            <div style={{ ...contentTextStyle, fontSize: \"20px\" }}>\r\n              Year to Date Report\r\n            </div>\r\n            <p style={{ ...contentTextStyle, fontSize: \"20px\", fontWeight: \"100\" }}>\r\n              Acme Print\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-lg text-gray-600\">No data available</div>\r\n              <div className=\"text-sm text-gray-500 mt-2\">Please check your data source</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const renderTableRow = (item, index) => {\r\n    if (item.isHeader) {\r\n      return (\r\n        <tr key={index} className=\"font-bold text-gray-800\">\r\n          <td className=\"text-left pl-2 font-normal\" style={contentTextStyle}>\r\n            <strong>{item.category}</strong>\r\n          </td>\r\n          <td></td>\r\n          <td></td>\r\n          <td></td>\r\n          <td></td>\r\n          <td></td>\r\n          <td></td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    if (item.isTotal) {\r\n      return (\r\n        <tr key={index} className=\"border-t-2 border-gray\">\r\n          <td className=\"text-left pl-2 font-normal\" style={contentTextStyle}>\r\n            <strong>{item.label}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono\" style={contentTextStyle}>\r\n            <strong>{item.jan25}</strong>\r\n          </td>\r\n          <td className=\"text-right\" style={contentTextStyle}>\r\n            <strong>{item.jan25Percent}</strong>\r\n          </td>\r\n          <td className=\"text-right font-mono\" style={contentTextStyle}>\r\n            <strong>{item.jan24}</strong>\r\n          </td>\r\n          <td className=\"text-right\" style={contentTextStyle}>\r\n            <strong>{item.jan24Percent}</strong>\r\n          </td>\r\n          <td \r\n            className={`text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}\r\n            style={contentTextStyle}\r\n          >\r\n            <strong>{item.variance}</strong>\r\n          </td>\r\n          <td \r\n            className={`text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}\r\n            style={contentTextStyle}\r\n          >\r\n            <strong>{item.variancePercent}</strong>\r\n          </td>\r\n        </tr>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <tr key={index}>\r\n        <td \r\n          className={`text-left font-normal ${item.indented ? 'pl-6' : 'pl-2'}`}\r\n          style={contentTextStyle}\r\n        >\r\n          {item.label}\r\n        </td>\r\n        <td className=\"text-right font-mono\" style={contentTextStyle}>\r\n          {item.jan25}\r\n        </td>\r\n        <td className=\"text-right\" style={contentTextStyle}>\r\n          {item.jan25Percent}\r\n        </td>\r\n        <td className=\"text-right font-mono\" style={contentTextStyle}>\r\n          {item.jan24}\r\n        </td>\r\n        <td className=\"text-right\" style={contentTextStyle}>\r\n          {item.jan24Percent}\r\n        </td>\r\n        <td \r\n          className={`text-right font-mono ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}\r\n          style={contentTextStyle}\r\n        >\r\n          {item.variance}\r\n        </td>\r\n        <td \r\n          className={`text-right ${item.isPositive ? 'text-green-600' : item.isNegative ? 'text-red-600' : ''}`}\r\n          style={contentTextStyle}\r\n        >\r\n          {item.variancePercent}\r\n        </td>\r\n      </tr>\r\n    );\r\n  };\r\n\r\n  // Generate dynamic YTD date description\r\n  const getYTDDescription = () => {\r\n    if (monthInfo.current) {\r\n      const [month, year] = monthInfo.current.split(' ');\r\n      const monthNames = {\r\n        'Jan': 'January', 'Feb': 'February', 'Mar': 'March', 'Apr': 'April',\r\n        'May': 'May', 'Jun': 'June', 'Jul': 'July', 'Aug': 'August',\r\n        'Sep': 'September', 'Oct': 'October', 'Nov': 'November', 'Dec': 'December'\r\n      };\r\n      const fullMonth = monthNames[month] || month;\r\n      return `Year to Date As of ${fullMonth} 31st, 20${year}`;\r\n    }\r\n    return 'Year to Date As of January 31st, 2025';\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-5 section profit-loss-section\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl mx-auto bg-white p-10 mx-auto overflow-x-auto report-container\">\r\n        {/* Header Section */}\r\n        <div className=\"report-header\">\r\n          <h2\r\n            className=\"text-2xl font-light text-gray-800 m-0\"\r\n           style={{...subHeadingTextStyle,  fontWeight: 'lighter', color : \"black\"}}\r\n          >\r\n            Profit and Loss\r\n          </h2>\r\n          <div\r\n            className=\"text-xl text-gray-800\"\r\n            style={{...contentTextStyle, fontSize : \"20px\"}}\r\n          >\r\n            {getYTDDescription()}\r\n          </div>\r\n          <p\r\n            style={{...contentTextStyle, fontSize : \"20px\", fontWeight : \"100\"}}\r\n          >\r\n            Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Table */}\r\n        <div className=\"overflow-x-auto\">\r\n          <table className=\"w-full border-collapse text-sm mt-4 profit-loss-table\">\r\n             <thead className=\"table-header-group\">\r\n              {/* Month Header Row */}\r\n              <tr className=\"text-black text-center bg-white\">\r\n                <th\r\n                  className=\"text-left bg-white border-0\"\r\n                  style={{ ...contentTextStyle, color: 'black' }}\r\n                ></th>\r\n                <th\r\n                  className=\"text-left bg-white border-0 pl-10\"\r\n                  style={{ ...contentTextStyle, color: 'black' }}\r\n                  colSpan=\"2\"\r\n                >\r\n                  {monthInfo.current}\r\n                </th>\r\n                <th\r\n                  className=\"text-left bg-white border-0 pl-10\"\r\n                  style={{ ...contentTextStyle, color: 'black' }}\r\n                  colSpan=\"2\"\r\n                >\r\n                  {monthInfo.previous}\r\n                </th>\r\n                <th\r\n                  className=\"text-left bg-white border-0\"\r\n                  style={{ ...contentTextStyle, color: 'black' }}\r\n                  colSpan=\"2\"\r\n                ></th>\r\n              </tr>\r\n              {/* Column Header Row */}\r\n              <tr>\r\n                <th\r\n                  className=\"text-right text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    ...contentTextStyle,\r\n                    color: 'white'\r\n                  }}\r\n                ></th>\r\n                {/* Current Year Group */}\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Actuals\r\n                </th>\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    color: 'white',\r\n                    borderRight: '20px solid white'\r\n                  }}\r\n                >\r\n                  <div>% of Income</div>\r\n                </th>\r\n\r\n                {/* Previous Year Group */}\r\n                <th\r\n                  className=\"text-center text-white p-1 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Prior Year\r\n                </th>\r\n                <th\r\n                  className=\"text-center text-white p-1 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    color: 'white',\r\n                    borderRight: '20px solid white'\r\n                  }}\r\n                >\r\n                  <div>% of Income</div>\r\n                </th>\r\n\r\n                {/* Variance Group */}\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  $ Variance\r\n                </th>\r\n                <th\r\n                  className=\"text-center text-white p-2 font-bold text-sm\"\r\n                  style={{\r\n                    backgroundColor: headerBgColor,\r\n                    ...contentTextStyle,\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  % Variance\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"table-row-group\">\r\n              {tableData.map((item, index) => renderTableRow(item, index))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n        <div className='text-center text-slate-300 text-xs border-b-4 border-blue-900 py-9'>\r\n          <p>\r\n            The information contained in this report is provided for informational purposes only and is not intended to substitute for obtaining accounting, tax, or financial advice from a professional accountant. Any tax advice\r\n            contained in this report is not intended to be used for the purpose of avoiding penalties under tax law. While we use reasonable efforts to furnish accurate and up-to-date information, we do not warrant that any\r\n            information contained in or made available through this report is accurate, complete, reliable, current or error-free. We assume no liability or responsibility for any errors or omissions in the content of this report or\r\n            delivered information.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfitLossYTDDashboard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,sBAAsB,GAAGA,CAAC;EAC9BC,eAAe,GAAG,CAAC,CAAC;EACpBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI,CAAC;AACpB,CAAC,KAAK;EACJ;EACA,MAAMC,aAAa,GAAGJ,eAAe,CAACK,KAAK,IAAI,SAAS;;EAExD;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACC,sBAAsB,EAAE;MAC/C,OAAO;QAAEC,SAAS,EAAE,EAAE;QAAEC,SAAS,EAAE;UAAEC,OAAO,EAAE,QAAQ;UAAEC,QAAQ,EAAE;QAAS;MAAE,CAAC;IAChF;IAEA,MAAMC,OAAO,GAAGN,OAAO,CAACC,sBAAsB;;IAE9C;IACA,IAAIM,YAAY,GAAG,QAAQ;IAC3B,IAAIC,aAAa,GAAG,QAAQ;IAE5B,IAAIF,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACJ,OAAO,CAAC,CAAC,CAAC,CAAC;MACpC,MAAMM,UAAU,GAAGF,IAAI,CAACG,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,cAAc,CAAC,CAAC;MACjE,IAAIH,UAAU,EAAE;QACd,MAAMI,UAAU,GAAGJ,UAAU,CAACK,KAAK,CAAC,6BAA6B,CAAC;QAClE,IAAID,UAAU,EAAE;UACdT,YAAY,GAAGS,UAAU,CAAC,CAAC,CAAC,CAACE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;UAC9C;UACA,MAAM,CAACC,KAAK,EAAEC,IAAI,CAAC,GAAGb,YAAY,CAACc,KAAK,CAAC,GAAG,CAAC;UAC7Cb,aAAa,GAAG,GAAGW,KAAK,IAAIG,QAAQ,CAACF,IAAI,CAAC,GAAG,CAAC,EAAE;QAClD;MACF;IACF;;IAEA;IACA,MAAMG,WAAW,GAAGjB,OAAO,CAACkB,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAChD,MAAMC,WAAW,GAAGD,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACG,qBAAqB;MACnE,IAAI,CAACJ,GAAG,CAACE,WAAW,CAAC,EAAE;QACrBF,GAAG,CAACE,WAAW,CAAC,GAAG,EAAE;MACvB;MACAF,GAAG,CAACE,WAAW,CAAC,CAACG,IAAI,CAACJ,IAAI,CAAC;MAC3B,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEN;IACA,MAAMvB,SAAS,GAAG,EAAE;IAEpBS,MAAM,CAACD,IAAI,CAACa,WAAW,CAAC,CAACQ,OAAO,CAACJ,WAAW,IAAI;MAC9C;MACAzB,SAAS,CAAC4B,IAAI,CAAC;QACbE,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAEN;MACZ,CAAC,CAAC;;MAEF;MACAJ,WAAW,CAACI,WAAW,CAAC,CAACI,OAAO,CAACL,IAAI,IAAI;QACvC;QACA,MAAMQ,OAAO,GAAGR,IAAI,CAACS,YAAY,CAACC,WAAW,CAAC,CAAC,CAACrB,QAAQ,CAAC,OAAO,CAAC;;QAEjE;QACA,MAAMsB,cAAc,GAAGC,UAAU,CAACZ,IAAI,CAACa,eAAe,IAAI,GAAG,CAAC;QAC9D,MAAMC,eAAe,GAAGF,UAAU,CAACZ,IAAI,CAACe,mBAAmB,IAAI,GAAG,CAAC;QACnE,MAAMC,UAAU,GAAGL,cAAc,GAAG,CAAC;QACrC,MAAMM,UAAU,GAAGN,cAAc,GAAG,CAAC;QAErCnC,SAAS,CAAC4B,IAAI,CAAC;UACbc,KAAK,EAAElB,IAAI,CAACS,YAAY;UACxBU,KAAK,EAAEnB,IAAI,CAACoB,kBAAkB,IAAI,GAAG;UAAE;UACvCC,YAAY,EAAErB,IAAI,CAACsB,wBAAwB,IAAI,IAAI;UACnDC,KAAK,EAAEvB,IAAI,CAACwB,qBAAqB,IAAI,GAAG;UACxCC,YAAY,EAAEzB,IAAI,CAAC0B,wBAAwB,IAAI,IAAI;UACnDC,QAAQ,EAAE3B,IAAI,CAACa,eAAe,IAAI,GAAG;UACrCC,eAAe,EAAEd,IAAI,CAACe,mBAAmB,IAAI,IAAI;UACjDP,OAAO,EAAEA,OAAO;UAChBoB,QAAQ,EAAE,CAACpB,OAAO,IAAI,CAACR,IAAI,CAACS,YAAY,CAACC,WAAW,CAAC,CAAC,CAACrB,QAAQ,CAAC,OAAO,CAAC;UACxE2B,UAAU,EAAEA,UAAU;UACtBC,UAAU,EAAEA;QACd,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO;MACLzC,SAAS;MACTC,SAAS,EAAE;QACTC,OAAO,EAAEG,YAAY;QACrBF,QAAQ,EAAEG;MACZ;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAM;IAAEN,SAAS;IAAEC;EAAU,CAAC,GAAGJ,gBAAgB,CAACH,UAAU,CAAC;;EAE7D;EACA,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACK,sBAAsB,IAAIC,SAAS,CAACO,MAAM,KAAK,CAAC,EAAE;IAC/E,oBACElB,OAAA;MAAKgE,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAC3DjE,OAAA;QAAKgE,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBAGvFjE,OAAA;UAAKgE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BjE,OAAA;YAAIkE,KAAK,EAAE;cAAE,GAAG/D,mBAAmB;cAAEgE,UAAU,EAAE,SAAS;cAAE5D,KAAK,EAAE;YAAQ,CAAE;YAAA0D,QAAA,EAAC;UAE9E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvE,OAAA;YAAKkE,KAAK,EAAE;cAAE,GAAG9D,gBAAgB;cAAEoE,QAAQ,EAAE;YAAO,CAAE;YAAAP,QAAA,EAAC;UAEvD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNvE,OAAA;YAAGkE,KAAK,EAAE;cAAE,GAAG9D,gBAAgB;cAAEoE,QAAQ,EAAE,MAAM;cAAEL,UAAU,EAAE;YAAM,CAAE;YAAAF,QAAA,EAAC;UAExE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENvE,OAAA;UAAKgE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpDjE,OAAA;YAAKgE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjE,OAAA;cAAKgE,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DvE,OAAA;cAAKgE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAA6B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,cAAc,GAAGA,CAACtC,IAAI,EAAEuC,KAAK,KAAK;IACtC,IAAIvC,IAAI,CAACM,QAAQ,EAAE;MACjB,oBACEzC,OAAA;QAAgBgE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACjDjE,OAAA;UAAIgE,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE9D,gBAAiB;UAAA6D,QAAA,eACjEjE,OAAA;YAAAiE,QAAA,EAAS9B,IAAI,CAACO;UAAQ;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACLvE,OAAA;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvE,OAAA;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvE,OAAA;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvE,OAAA;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvE,OAAA;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvE,OAAA;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,GATFG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUV,CAAC;IAET;IAEA,IAAIpC,IAAI,CAACQ,OAAO,EAAE;MAChB,oBACE3C,OAAA;QAAgBgE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAChDjE,OAAA;UAAIgE,SAAS,EAAC,4BAA4B;UAACE,KAAK,EAAE9D,gBAAiB;UAAA6D,QAAA,eACjEjE,OAAA;YAAAiE,QAAA,EAAS9B,IAAI,CAACkB;UAAK;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLvE,OAAA;UAAIgE,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE9D,gBAAiB;UAAA6D,QAAA,eAC3DjE,OAAA;YAAAiE,QAAA,EAAS9B,IAAI,CAACmB;UAAK;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLvE,OAAA;UAAIgE,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE9D,gBAAiB;UAAA6D,QAAA,eACjDjE,OAAA;YAAAiE,QAAA,EAAS9B,IAAI,CAACqB;UAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACLvE,OAAA;UAAIgE,SAAS,EAAC,sBAAsB;UAACE,KAAK,EAAE9D,gBAAiB;UAAA6D,QAAA,eAC3DjE,OAAA;YAAAiE,QAAA,EAAS9B,IAAI,CAACuB;UAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACLvE,OAAA;UAAIgE,SAAS,EAAC,YAAY;UAACE,KAAK,EAAE9D,gBAAiB;UAAA6D,QAAA,eACjDjE,OAAA;YAAAiE,QAAA,EAAS9B,IAAI,CAACyB;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACLvE,OAAA;UACEgE,SAAS,EAAE,wBAAwB7B,IAAI,CAACgB,UAAU,GAAG,gBAAgB,GAAGhB,IAAI,CAACiB,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;UAChHc,KAAK,EAAE9D,gBAAiB;UAAA6D,QAAA,eAExBjE,OAAA;YAAAiE,QAAA,EAAS9B,IAAI,CAAC2B;UAAQ;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACLvE,OAAA;UACEgE,SAAS,EAAE,cAAc7B,IAAI,CAACgB,UAAU,GAAG,gBAAgB,GAAGhB,IAAI,CAACiB,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;UACtGc,KAAK,EAAE9D,gBAAiB;UAAA6D,QAAA,eAExBjE,OAAA;YAAAiE,QAAA,EAAS9B,IAAI,CAACc;UAAe;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA,GA3BEG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4BV,CAAC;IAET;IAEA,oBACEvE,OAAA;MAAAiE,QAAA,gBACEjE,OAAA;QACEgE,SAAS,EAAE,yBAAyB7B,IAAI,CAAC4B,QAAQ,GAAG,MAAM,GAAG,MAAM,EAAG;QACtEG,KAAK,EAAE9D,gBAAiB;QAAA6D,QAAA,EAEvB9B,IAAI,CAACkB;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACLvE,OAAA;QAAIgE,SAAS,EAAC,sBAAsB;QAACE,KAAK,EAAE9D,gBAAiB;QAAA6D,QAAA,EAC1D9B,IAAI,CAACmB;MAAK;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACLvE,OAAA;QAAIgE,SAAS,EAAC,YAAY;QAACE,KAAK,EAAE9D,gBAAiB;QAAA6D,QAAA,EAChD9B,IAAI,CAACqB;MAAY;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACLvE,OAAA;QAAIgE,SAAS,EAAC,sBAAsB;QAACE,KAAK,EAAE9D,gBAAiB;QAAA6D,QAAA,EAC1D9B,IAAI,CAACuB;MAAK;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACLvE,OAAA;QAAIgE,SAAS,EAAC,YAAY;QAACE,KAAK,EAAE9D,gBAAiB;QAAA6D,QAAA,EAChD9B,IAAI,CAACyB;MAAY;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACLvE,OAAA;QACEgE,SAAS,EAAE,wBAAwB7B,IAAI,CAACgB,UAAU,GAAG,gBAAgB,GAAGhB,IAAI,CAACiB,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;QAChHc,KAAK,EAAE9D,gBAAiB;QAAA6D,QAAA,EAEvB9B,IAAI,CAAC2B;MAAQ;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACLvE,OAAA;QACEgE,SAAS,EAAE,cAAc7B,IAAI,CAACgB,UAAU,GAAG,gBAAgB,GAAGhB,IAAI,CAACiB,UAAU,GAAG,cAAc,GAAG,EAAE,EAAG;QACtGc,KAAK,EAAE9D,gBAAiB;QAAA6D,QAAA,EAEvB9B,IAAI,CAACc;MAAe;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA,GA9BEG,KAAK;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA+BV,CAAC;EAET,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI/D,SAAS,CAACC,OAAO,EAAE;MACrB,MAAM,CAACe,KAAK,EAAEC,IAAI,CAAC,GAAGjB,SAAS,CAACC,OAAO,CAACiB,KAAK,CAAC,GAAG,CAAC;MAClD,MAAM8C,UAAU,GAAG;QACjB,KAAK,EAAE,SAAS;QAAE,KAAK,EAAE,UAAU;QAAE,KAAK,EAAE,OAAO;QAAE,KAAK,EAAE,OAAO;QACnE,KAAK,EAAE,KAAK;QAAE,KAAK,EAAE,MAAM;QAAE,KAAK,EAAE,MAAM;QAAE,KAAK,EAAE,QAAQ;QAC3D,KAAK,EAAE,WAAW;QAAE,KAAK,EAAE,SAAS;QAAE,KAAK,EAAE,UAAU;QAAE,KAAK,EAAE;MAClE,CAAC;MACD,MAAMC,SAAS,GAAGD,UAAU,CAAChD,KAAK,CAAC,IAAIA,KAAK;MAC5C,OAAO,sBAAsBiD,SAAS,YAAYhD,IAAI,EAAE;IAC1D;IACA,OAAO,uCAAuC;EAChD,CAAC;EAED,oBACE7B,OAAA;IAAKgE,SAAS,EAAC,8CAA8C;IAAAC,QAAA,eAE3DjE,OAAA;MAAKgE,SAAS,EAAC,0EAA0E;MAAAC,QAAA,gBAEvFjE,OAAA;QAAKgE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjE,OAAA;UACEgE,SAAS,EAAC,uCAAuC;UAClDE,KAAK,EAAE;YAAC,GAAG/D,mBAAmB;YAAGgE,UAAU,EAAE,SAAS;YAAE5D,KAAK,EAAG;UAAO,CAAE;UAAA0D,QAAA,EACzE;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvE,OAAA;UACEgE,SAAS,EAAC,uBAAuB;UACjCE,KAAK,EAAE;YAAC,GAAG9D,gBAAgB;YAAEoE,QAAQ,EAAG;UAAM,CAAE;UAAAP,QAAA,EAE/CU,iBAAiB,CAAC;QAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eACNvE,OAAA;UACEkE,KAAK,EAAE;YAAC,GAAG9D,gBAAgB;YAAEoE,QAAQ,EAAG,MAAM;YAAEL,UAAU,EAAG;UAAK,CAAE;UAAAF,QAAA,EACrE;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvE,OAAA;QAAKgE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BjE,OAAA;UAAOgE,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACrEjE,OAAA;YAAOgE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAEpCjE,OAAA;cAAIgE,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC7CjE,OAAA;gBACEgE,SAAS,EAAC,6BAA6B;gBACvCE,KAAK,EAAE;kBAAE,GAAG9D,gBAAgB;kBAAEG,KAAK,EAAE;gBAAQ;cAAE;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACNvE,OAAA;gBACEgE,SAAS,EAAC,mCAAmC;gBAC7CE,KAAK,EAAE;kBAAE,GAAG9D,gBAAgB;kBAAEG,KAAK,EAAE;gBAAQ,CAAE;gBAC/CuE,OAAO,EAAC,GAAG;gBAAAb,QAAA,EAEVrD,SAAS,CAACC;cAAO;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACLvE,OAAA;gBACEgE,SAAS,EAAC,mCAAmC;gBAC7CE,KAAK,EAAE;kBAAE,GAAG9D,gBAAgB;kBAAEG,KAAK,EAAE;gBAAQ,CAAE;gBAC/CuE,OAAO,EAAC,GAAG;gBAAAb,QAAA,EAEVrD,SAAS,CAACE;cAAQ;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACLvE,OAAA;gBACEgE,SAAS,EAAC,6BAA6B;gBACvCE,KAAK,EAAE;kBAAE,GAAG9D,gBAAgB;kBAAEG,KAAK,EAAE;gBAAQ,CAAE;gBAC/CuE,OAAO,EAAC;cAAG;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAELvE,OAAA;cAAAiE,QAAA,gBACEjE,OAAA;gBACEgE,SAAS,EAAC,6CAA6C;gBACvDE,KAAK,EAAE;kBACL,GAAG9D,gBAAgB;kBACnBG,KAAK,EAAE;gBACT;cAAE;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENvE,OAAA;gBACEgE,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLa,eAAe,EAAEzE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBG,KAAK,EAAE;gBACT,CAAE;gBAAA0D,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvE,OAAA;gBACEgE,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLa,eAAe,EAAEzE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBG,KAAK,EAAE,OAAO;kBACdyE,WAAW,EAAE;gBACf,CAAE;gBAAAf,QAAA,eAEFjE,OAAA;kBAAAiE,QAAA,EAAK;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGLvE,OAAA;gBACEgE,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLa,eAAe,EAAEzE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBG,KAAK,EAAE;gBACT,CAAE;gBAAA0D,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvE,OAAA;gBACEgE,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLa,eAAe,EAAEzE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBG,KAAK,EAAE,OAAO;kBACdyE,WAAW,EAAE;gBACf,CAAE;gBAAAf,QAAA,eAEFjE,OAAA;kBAAAiE,QAAA,EAAK;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGLvE,OAAA;gBACEgE,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLa,eAAe,EAAEzE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBG,KAAK,EAAE;gBACT,CAAE;gBAAA0D,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLvE,OAAA;gBACEgE,SAAS,EAAC,8CAA8C;gBACxDE,KAAK,EAAE;kBACLa,eAAe,EAAEzE,aAAa;kBAC9B,GAAGF,gBAAgB;kBACnBG,KAAK,EAAE;gBACT,CAAE;gBAAA0D,QAAA,EACH;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRvE,OAAA;YAAOgE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC/BtD,SAAS,CAACsE,GAAG,CAAC,CAAC9C,IAAI,EAAEuC,KAAK,KAAKD,cAAc,CAACtC,IAAI,EAAEuC,KAAK,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNvE,OAAA;QAAKgE,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjFjE,OAAA;UAAAiE,QAAA,EAAG;QAKH;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACW,EAAA,GAvXIjF,sBAAsB;AAyX5B,eAAeA,sBAAsB;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}