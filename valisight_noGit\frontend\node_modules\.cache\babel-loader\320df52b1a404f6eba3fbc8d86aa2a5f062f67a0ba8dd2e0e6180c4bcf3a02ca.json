{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\ExpenseSummary.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef } from \"react\";\nimport ApexCharts from \"apexcharts\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ExpenseSummaryDashboard = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  reportData = null,\n  contentSettings = null // Add contentSettings prop\n}) => {\n  _s();\n  const roaRoeRef = useRef(null);\n  const expensesPieRef = useRef(null);\n  const expensesMonthlyRef = useRef(null);\n  const wagesRevenueRef = useRef(null);\n\n  // Helper function to sort data with \"Other\" last\n  const sortDataWithOtherLast = data => {\n    return data.sort((a, b) => {\n      const aIsOther = (a.account_name || '').toLowerCase().includes('other');\n      const bIsOther = (b.account_name || '').toLowerCase().includes('other');\n      if (aIsOther && !bIsOther) return 1; // a (Other) goes after b\n      if (!aIsOther && bIsOther) return -1; // b (Other) goes after a\n      return 0; // maintain original order for non-Other items\n    });\n  };\n\n  // Function to check if a chart should be displayed based on content settings\n  const shouldDisplayChart = chartKey => {\n    if (!(contentSettings !== null && contentSettings !== void 0 && contentSettings.chartSettings)) return true; // Default to true if no settings\n    return contentSettings.chartSettings[chartKey] === true;\n  };\n\n  // Enhanced data validation function\n  const isDataLoaded = () => {\n    var _reportData$roeRoa, _reportData$expensesT, _reportData$expensesT2, _reportData$monthlyPe;\n    if (!reportData) {\n      console.log(\"ExpenseSummary - No reportData provided\");\n      return false;\n    }\n    console.log(\"ExpenseSummary - reportData keys:\", Object.keys(reportData));\n\n    // Check if at least some required data exists - make it more flexible\n    const hasRoeRoaData = reportData.roeRoa && Array.isArray(reportData.roeRoa) && reportData.roeRoa.length > 0;\n    const hasExpensesData = reportData.expensesTopAccounts && Array.isArray(reportData.expensesTopAccounts) && reportData.expensesTopAccounts.length > 0;\n\n    // For monthly expenses, check for new detailed breakdown data first, then fallback to performance data\n    const hasMonthlyExpensesData = reportData.expensesTopAccountsMonthly && Array.isArray(reportData.expensesTopAccountsMonthly) && reportData.expensesTopAccountsMonthly.length > 0;\n    const hasMonthlyData = hasMonthlyExpensesData || reportData.monthlyPerformanceBreakDown && Array.isArray(reportData.monthlyPerformanceBreakDown) && reportData.monthlyPerformanceBreakDown.length > 0;\n\n    // For wages vs revenue, we'll derive from monthly performance data\n    const hasPerformanceData = hasMonthlyData;\n    console.log(\"ExpenseSummary - Data validation:\", {\n      hasRoeRoaData,\n      hasExpensesData,\n      hasMonthlyExpensesData,\n      hasMonthlyData,\n      hasPerformanceData,\n      roeRoaLength: ((_reportData$roeRoa = reportData.roeRoa) === null || _reportData$roeRoa === void 0 ? void 0 : _reportData$roeRoa.length) || 0,\n      expensesLength: ((_reportData$expensesT = reportData.expensesTopAccounts) === null || _reportData$expensesT === void 0 ? void 0 : _reportData$expensesT.length) || 0,\n      monthlyExpensesLength: ((_reportData$expensesT2 = reportData.expensesTopAccountsMonthly) === null || _reportData$expensesT2 === void 0 ? void 0 : _reportData$expensesT2.length) || 0,\n      monthlyDataLength: ((_reportData$monthlyPe = reportData.monthlyPerformanceBreakDown) === null || _reportData$monthlyPe === void 0 ? void 0 : _reportData$monthlyPe.length) || 0\n    });\n\n    // Return true if we have at least some data to work with\n    return hasRoeRoaData || hasExpensesData || hasMonthlyData;\n  };\n  useEffect(() => {\n    if (isDataLoaded()) {\n      initializeCharts();\n    }\n  }, [reportData, contentSettings]); // Add contentSettings to dependency array\n\n  // Helper function to format month-year\n  const formatMonthYear = (year, month) => {\n    const monthNames = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n\n  // Helper function to format currency values with appropriate units\n  const formatCurrency = (val, options = {}) => {\n    if (val === null || val === undefined || isNaN(val) || val === 0) {\n      return options.showZero ? '$0' : '';\n    }\n    const absVal = Math.abs(val);\n    if (absVal >= 1000000) {\n      // Trillions\n      return '$' + (val / 1000000).toFixed(1) + 't';\n    } else if (absVal >= 1000) {\n      // Millions\n      return '$' + (val / 1000).toFixed(1) + 'm';\n    } else {\n      // Thousands (since our data is already in thousands)\n      return '$' + val.toFixed(1) + 'k';\n    }\n  };\n\n  // Process ROE/ROA data from API\n  const processRoeRoaData = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.roeRoa)) return {\n      roaData: [],\n      roeData: [],\n      categories: []\n    };\n    const roaData = reportData.roeRoa.map(item => {\n      const value = parseFloat(item.roa || 0);\n      return isNaN(value) ? 0 : value;\n    });\n    const roeData = reportData.roeRoa.map(item => {\n      const value = parseFloat(item.roe || 0);\n      return isNaN(value) ? 0 : value;\n    });\n\n    // Create categories from the data if available\n    const categories = reportData.roeRoa.map(item => {\n      if (item.year && item.month) {\n        return formatMonthYear(item.year, item.month);\n      }\n      return item.period || `Period ${reportData.roeRoa.indexOf(item) + 1}`;\n    });\n    return {\n      roaData,\n      roeData,\n      categories\n    };\n  };\n\n  // Process expenses pie chart data from API\n  const processExpensesPieData = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.expensesTopAccounts)) return {\n      data: [],\n      labels: []\n    };\n\n    // Sort data with \"Other\" last\n    const sortedExpenses = sortDataWithOtherLast([...reportData.expensesTopAccounts]);\n    const data = sortedExpenses.map(item => {\n      const value = parseFloat(item.total_expense || 0);\n      return isNaN(value) ? 0 : value / 1000; // Convert to thousands\n    });\n    const labels = sortedExpenses.map(item => {\n      const expense = parseFloat(item.total_expense || 0);\n      const percentage = parseFloat(item.percentage_of_total || 0);\n      const expenseDisplay = isNaN(expense) ? \"0\" : expense / 1000;\n      const percentageDisplay = isNaN(percentage) ? \"0\" : percentage;\n      return `${item.account_name || \"Unknown\"} ${expenseDisplay}k (${percentageDisplay}%)`;\n    });\n    return {\n      data,\n      labels\n    };\n  };\n\n  // Process monthly expenses data from API - use expensesTopAccountsMonthly for detailed account breakdown\n  const processMonthlyExpensesData = () => {\n    // Check if we have the new monthly expenses breakdown data\n    if (reportData !== null && reportData !== void 0 && reportData.expensesTopAccountsMonthly && Array.isArray(reportData.expensesTopAccountsMonthly)) {\n      return processDetailedMonthlyExpenses();\n    }\n\n    // Fallback to old method if new data is not available\n    if (!(reportData !== null && reportData !== void 0 && reportData.monthlyPerformanceBreakDown)) return {\n      series: [],\n      categories: []\n    };\n\n    // Get monthly data and create categories\n    const monthlyData = reportData.monthlyPerformanceBreakDown;\n    const categories = monthlyData.map(item => formatMonthYear(item.year, item.month));\n\n    // Since we don't have individual account breakdowns by month,\n    // we'll create a single series for total expenses\n    const totalExpensesData = monthlyData.map(item => {\n      const expense = parseFloat(item.totalExpenses || 0) / 1000; // Convert to thousands\n      return isNaN(expense) ? 0 : expense;\n    });\n    const series = [{\n      name: 'Total Expenses',\n      data: totalExpensesData\n    }];\n    return {\n      series,\n      categories\n    };\n  };\n\n  // Process detailed monthly expenses breakdown by account\n  const processDetailedMonthlyExpenses = () => {\n    // Sort data with \"Other\" last\n    const sortedExpensesData = sortDataWithOtherLast([...reportData.expensesTopAccountsMonthly]);\n\n    // Define the month columns in order\n    const monthColumns = ['apr_24', 'may_24', 'jun_24', 'jul_24', 'aug_24', 'sep_24', 'oct_24', 'nov_24', 'dec_24', 'jan_25', 'feb_25', 'mar_25'];\n\n    // Create categories (month labels)\n    const categories = ['Apr 24', 'May 24', 'Jun 24', 'Jul 24', 'Aug 24', 'Sep 24', 'Oct 24', 'Nov 24', 'Dec 24', 'Jan 25', 'Feb 25', 'Mar 25'];\n\n    // Create series for each account (now sorted with Other last)\n    const series = sortedExpensesData.map(account => {\n      const data = monthColumns.map(month => {\n        // Handle string values from API by parsing them first\n        const rawValue = account[month] || '0';\n        const value = parseFloat(rawValue) / 1000; // Convert to thousands\n        return isNaN(value) ? 0 : value;\n      });\n      return {\n        name: account.account_name || 'Unknown',\n        data: data\n      };\n    });\n    return {\n      series,\n      categories\n    };\n  };\n\n  // Process wages vs revenue data from monthly performance data\n  const processWagesRevenueData = () => {\n    if (!(reportData !== null && reportData !== void 0 && reportData.monthlyPerformanceBreakDown)) return {\n      income: [],\n      salariesGA: [],\n      salariesSales: [],\n      categories: []\n    };\n    const monthlyData = reportData.monthlyPerformanceBreakDown;\n    const income = monthlyData.map(item => {\n      const value = parseFloat(item.totalIncome || 0) / 1000000; // Convert to millions\n      return isNaN(value) ? 0 : value;\n    });\n\n    // Since we don't have salary breakdown in the data, we'll estimate based on expenses\n    // This is a placeholder - you may want to add actual salary data to your API\n    const salariesGA = monthlyData.map(item => {\n      const expenses = parseFloat(item.totalExpenses || 0);\n      // Estimate G&A salaries as 30% of total expenses\n      const value = expenses * 0.3 / 1000000; // Convert to millions\n      return isNaN(value) ? 0 : value;\n    });\n    const salariesSales = monthlyData.map(item => {\n      const expenses = parseFloat(item.totalExpenses || 0);\n      // Estimate Sales salaries as 20% of total expenses\n      const value = expenses * 0.2 / 1000000; // Convert to millions\n      return isNaN(value) ? 0 : value;\n    });\n    const categories = monthlyData.map(item => formatMonthYear(item.year, item.month));\n    return {\n      income,\n      salariesGA,\n      salariesSales,\n      categories\n    };\n  };\n  const initializeCharts = () => {\n    console.log(\"ExpenseSummary - Initializing charts with data:\", reportData);\n    const {\n      roaData,\n      roeData,\n      categories: roaRoeCategories\n    } = processRoeRoaData();\n    const {\n      data: pieData,\n      labels: pieLabels\n    } = processExpensesPieData();\n    const {\n      series: monthlyExpensesSeries,\n      categories: monthlyCategories\n    } = processMonthlyExpensesData();\n    const {\n      income,\n      salariesGA,\n      salariesSales,\n      categories: wagesCategories\n    } = processWagesRevenueData();\n    console.log(\"ExpenseSummary - Processed data:\", {\n      roaDataLength: roaData.length,\n      pieDataLength: pieData.length,\n      monthlySeriesLength: monthlyExpensesSeries.length,\n      incomeDataLength: income.length\n    });\n\n    // Chart colors\n    const colors = {\n      roaRoe: [\"#4a90e2\", \"#ff6b47\"],\n      expensesPie: [\"#1f4e79\", \"#20b2aa\", \"#ff7f50\", \"#4db6ac\", \"#95a5a6\", \"#5d6d7e\", \"#bdc3c7\", \"#ffab91\", \"#9575cd\", \"#ba68c8\", \"#90a4ae\"],\n      monthlyExpenses: [\"#1f4e79\", \"#20b2aa\", \"#ff7f50\", \"#4db6ac\", \"#95a5a6\", \"#5d6d7e\", \"#bdc3c7\", \"#ffab91\", \"#9575cd\", \"#ba68c8\", \"#90a4ae\", \"#4a6fa5\", \"#2d5f5f\", \"#5f9ea0\", \"#8b7d82\", \"#4682b4\", \"#b0c4de\", \"#dda0dd\", \"#87ceeb\", \"#f0e68c\", \"#d3d3d3\"],\n      wagesRevenue: [\"#20b2aa\", \"#4a4a9a\", \"#ff7f50\"]\n    };\n\n    // 1. ROA and ROE Line Chart\n    const roaRoeOptions = {\n      series: [{\n        name: \"ROA\",\n        data: roaData\n      }, {\n        name: \"ROE\",\n        data: roeData\n      }],\n      chart: {\n        type: \"line\",\n        height: 300,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          if (val === null || val === undefined || isNaN(val)) return \"0\";\n          return val + \"%\";\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#333\"],\n          fontWeight: \"500\"\n        },\n        background: {\n          enabled: false\n        },\n        offsetY: -5\n      },\n      stroke: {\n        curve: \"smooth\",\n        width: 2\n      },\n      xaxis: {\n        categories: roaRoeCategories,\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: colors.roaRoe,\n      markers: {\n        size: 4,\n        strokeColors: \"#fff\",\n        strokeWidth: 1,\n        hover: {\n          size: 6\n        }\n      },\n      legend: {\n        position: \"bottom\",\n        horizontalAlign: \"center\",\n        offsetY: 0,\n        markers: {\n          width: 8,\n          height: 8,\n          radius: 4\n        },\n        labels: {\n          colors: [\"#333\"],\n          useSeriesColors: false,\n          fontSize: \"13px\"\n        },\n        itemMargin: {\n          horizontal: 20,\n          vertical: 0\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"0\";\n            return val + \"%\";\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      annotations: {\n        yaxis: [{\n          y: 0,\n          borderColor: \"#999\",\n          borderWidth: 1,\n          strokeDashArray: 0,\n          opacity: 1\n        }]\n      }\n    };\n\n    // 2. Expenses Pie Chart\n    const expensesPieOptions = {\n      series: pieData,\n      chart: {\n        type: \"pie\",\n        height: 400,\n        toolbar: {\n          show: false\n        }\n      },\n      labels: pieLabels,\n      colors: colors.expensesPie,\n      dataLabels: {\n        enabled: true,\n        formatter: function (val, opts) {\n          if (!opts || !opts.w || !opts.w.globals || !opts.w.globals.series) return \"\";\n          const value = opts.w.globals.series[opts.seriesIndex];\n          return formatCurrency(value);\n        },\n        style: {\n          fontSize: \"11px\",\n          colors: [\"#fff\"],\n          fontWeight: \"500\"\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      legend: {\n        position: \"right\",\n        fontSize: \"12px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 10,\n          height: 10,\n          radius: 5\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: false,\n          fontFamily: \"Calibri\"\n        },\n        itemMargin: {\n          horizontal: 5,\n          vertical: 3\n        },\n        offsetX: 0\n      },\n      plotOptions: {\n        pie: {\n          dataLabels: {\n            offset: 0\n          }\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return formatCurrency(val, {\n              showZero: true\n            });\n          }\n        }\n      },\n      stroke: {\n        show: false\n      },\n      responsive: [{\n        breakpoint: 768,\n        options: {\n          legend: {\n            position: \"bottom\"\n          }\n        }\n      }]\n    };\n\n    // 3. Monthly Expenses Stacked Chart\n    const expensesMonthlyOptions = {\n      series: monthlyExpensesSeries,\n      chart: {\n        type: \"bar\",\n        height: 450,\n        stacked: true,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      plotOptions: {\n        bar: {\n          horizontal: false,\n          columnWidth: \"60%\",\n          dataLabels: {\n            total: {\n              enabled: true,\n              offsetY: -25,\n              style: {\n                fontSize: \"12px\",\n                fontWeight: \"600\",\n                color: \"#333\"\n              },\n              formatter: function (val) {\n                return formatCurrency(val);\n              }\n            }\n          }\n        }\n      },\n      dataLabels: {\n        enabled: false,\n        formatter: function (val) {\n          return formatCurrency(val);\n        },\n        style: {\n          fontSize: \"11px\",\n          fontWeight: \"500\",\n          colors: [\"#333\"]\n        },\n        offsetY: -5,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      xaxis: {\n        categories: monthlyCategories,\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"14px\"\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: colors.monthlyExpenses,\n      legend: {\n        position: \"bottom\",\n        fontSize: \"11px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 8,\n          height: 8,\n          radius: 4\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: true\n        },\n        itemMargin: {\n          horizontal: 8,\n          vertical: 3\n        },\n        offsetY: 10,\n        onItemClick: {\n          toggleDataSeries: true\n        },\n        onItemHover: {\n          highlightDataSeries: true\n        }\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return formatCurrency(val, {\n              showZero: true\n            });\n          }\n        }\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      stroke: {\n        show: false\n      }\n    };\n\n    // 4. Wages vs Revenue Chart (using real data from API)\n    const wagesRevenueOptions = {\n      series: [{\n        name: \"Income\",\n        type: \"line\",\n        data: income\n      }, {\n        name: \"Salaries - G&A\",\n        type: \"column\",\n        data: salariesGA\n      }, {\n        name: \"Salaries - Sales\",\n        type: \"column\",\n        data: salariesSales\n      }],\n      chart: {\n        height: 400,\n        type: \"line\",\n        stacked: true,\n        toolbar: {\n          show: false\n        },\n        background: \"transparent\"\n      },\n      dataLabels: {\n        enabled: true,\n        enabledOnSeries: [0],\n        formatter: function (val, opts) {\n          if (opts.seriesIndex === 0) {\n            if (val === null || val === undefined || isNaN(val)) return \"\";\n            return \"$\" + val + \"m\";\n          }\n          return \"\";\n        },\n        style: {\n          fontSize: \"14px\",\n          colors: [\"#20b2aa\"],\n          fontWeight: \"500\"\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        width: [2, 0, 0],\n        curve: \"smooth\"\n      },\n      plotOptions: {\n        bar: {\n          columnWidth: \"40%\",\n          height: 1900,\n          dataLabels: {\n            total: {\n              enabled: true,\n              offsetY: -20,\n              style: {\n                fontSize: \"14px\",\n                fontWeight: \"500\",\n                color: \"#333\"\n              },\n              formatter: function (val) {\n                if (val === null || val === undefined || isNaN(val)) return \"$0\";\n                if (val >= 1) {\n                  return \"$\" + val + \"m\";\n                } else {\n                  return \"$\" + val * 1000 + \"k\";\n                }\n              }\n            }\n          }\n        }\n      },\n      fill: {\n        opacity: [1, 1, 1]\n      },\n      labels: wagesCategories,\n      markers: {\n        size: [5, 0, 0],\n        fontSize: \"14px\",\n        strokeColors: \"#fff\",\n        strokeWidth: 2,\n        fillOpacity: 1,\n        hover: {\n          size: 7\n        }\n      },\n      xaxis: {\n        labels: {\n          style: {\n            colors: \"#666\",\n            fontSize: \"12px\"\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: colors.wagesRevenue,\n      legend: {\n        position: \"bottom\",\n        horizontalAlign: \"center\",\n        fontSize: \"14px\",\n        fontWeight: \"400\",\n        markers: {\n          width: 8,\n          height: 8,\n          radius: 4\n        },\n        labels: {\n          colors: \"#333\",\n          useSeriesColors: false\n        },\n        itemMargin: {\n          horizontal: 15,\n          vertical: 4\n        },\n        offsetY: 10,\n        onItemClick: {\n          toggleDataSeries: false\n        },\n        onItemHover: {\n          highlightDataSeries: false\n        }\n      },\n      tooltip: {\n        shared: true,\n        intersect: false,\n        y: [{\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"$0\";\n            return \"$\" + val + \" million\";\n          }\n        }, {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"$0\";\n            return \"$\" + val + \" million\";\n          }\n        }, {\n          formatter: function (val) {\n            if (val === null || val === undefined || isNaN(val)) return \"$0\";\n            return \"$\" + val + \" million\";\n          }\n        }]\n      },\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      }\n    };\n\n    // Clear existing charts before rendering new ones\n    const clearAndRenderChart = (ref, options, chartName) => {\n      if (ref.current) {\n        // Clear any existing chart\n        ref.current.innerHTML = \"\";\n\n        // Wait a tick before rendering to ensure DOM is cleared\n        setTimeout(() => {\n          if (ref.current) {\n            try {\n              console.log(`ExpenseSummary - Rendering ${chartName} chart`);\n              const chart = new ApexCharts(ref.current, options);\n              chart.render();\n            } catch (error) {\n              console.error(`ExpenseSummary - Error rendering ${chartName} chart:`, error);\n            }\n          }\n        }, 10);\n      }\n    };\n\n    // Only render charts if we have data for them AND they are enabled in content settings\n    if ((roaData.length > 0 || roeData.length > 0) && shouldDisplayChart('roaAndRoe')) {\n      clearAndRenderChart(roaRoeRef, roaRoeOptions, \"ROA/ROE\");\n    }\n\n    // Note: There's no direct mapping for expenses pie chart in the settings, so we'll assume it's always shown\n    // You might want to add a specific setting for this\n    if (pieData.length > 0) {\n      clearAndRenderChart(expensesPieRef, expensesPieOptions, \"Expenses Pie\");\n    }\n\n    // Note: There's no direct mapping for monthly expenses in the settings, so we'll assume it's always shown\n    // You might want to add a specific setting for this\n    if (monthlyExpensesSeries.length > 0) {\n      clearAndRenderChart(expensesMonthlyRef, expensesMonthlyOptions, \"Monthly Expenses\");\n    }\n\n    // Note: There's no direct mapping for wages vs revenue in the settings, so we'll assume it's always shown\n    // You might want to add a specific setting for this\n    if (income.length > 0) {\n      clearAndRenderChart(wagesRevenueRef, wagesRevenueOptions, \"Wages vs Revenue\");\n    }\n  };\n\n  // Enhanced loading component\n  const LoadingComponent = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl text-gray-600 mb-2\",\n            children: \"Loading expense data...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Please wait while we fetch your expense information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 879,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 878,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 877,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 876,\n    columnNumber: 5\n  }, this);\n\n  // Show loading if data is not properly loaded\n  if (!isDataLoaded()) {\n    return /*#__PURE__*/_jsxDEV(LoadingComponent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 896,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Add a fallback if reportData exists but has no usable data\n  const hasAnyUsableData = () => {\n    const {\n      roaData,\n      roeData\n    } = processRoeRoaData();\n    const {\n      data: pieData\n    } = processExpensesPieData();\n    const {\n      series: monthlyExpensesSeries\n    } = processMonthlyExpensesData();\n    const {\n      income\n    } = processWagesRevenueData();\n    return roaData.length > 0 || roeData.length > 0 || pieData.length > 0 || monthlyExpensesSeries.length > 0 || income.length > 0;\n  };\n  if (!hasAnyUsableData()) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen p-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl text-gray-600 mb-2\",\n              children: \"No expense data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"The data structure is available but charts cannot be rendered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400 mt-2\",\n              children: [\"Available data: \", Object.keys(reportData || {}).join(\", \")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 918,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 917,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col gap-10 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Expense Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: \"January 2025 | Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 9\n      }, this), shouldDisplayChart('roaAndRoe') && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Return on Assets and Equity\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 957,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: roaRoeRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 963,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-teal-600 text-xl font-semibold\",\n              style: {\n                ...subHeadingTextStyle,\n                fontWeight: \"lighter\"\n              },\n              children: \"Return on Assets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Indicates how well Acme Print is using capital invested in Assets to generate Total Income. The higher the return, the more productive and efficient management is in utilizing economic resources the business has.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-teal-600 text-xl font-semibold\",\n              style: {\n                ...subHeadingTextStyle,\n                fontWeight: \"lighter\"\n              },\n              children: \"Return on Equity\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 leading-relaxed\",\n              style: contentTextStyle,\n              children: \"Indicates how efficient company management is at generating growth from its Equity financing. Because Equity is equal to a company's Assets minus Liabilities, ROE is also considered the Return on Net Assets.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 966,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-24\",\n          style: subHeadingTextStyle,\n          children: \"Expenses: Top Accounts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1008,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: expensesPieRef,\n          className: \"mb-12\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1014,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1007,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 940,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col gap-10 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 pt-10 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Expense Summary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: \"January 2025 | Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1029,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1022,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900 expenses-monthly-apex\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Expenses: Top Accounts Monthly\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: expensesMonthlyRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1042,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1035,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Expenses: Wages Vs Revenue Monthly\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1047,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: wagesRevenueRef,\n          className: \"mb-32\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1053,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1046,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1019,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 938,\n    columnNumber: 5\n  }, this);\n};\n_s(ExpenseSummaryDashboard, \"Sy5DlCxTGvmFm/0nV8tkAkcKqls=\");\n_c = ExpenseSummaryDashboard;\nexport default ExpenseSummaryDashboard;\nvar _c;\n$RefreshReg$(_c, \"ExpenseSummaryDashboard\");", "map": {"version": 3, "names": ["useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "ExpenseSummaryDashboard", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "contentTextStyle", "reportData", "contentSettings", "_s", "roaRoeRef", "expensesPieRef", "expensesMonthlyRef", "wagesRevenueRef", "sortDataWithOtherLast", "data", "sort", "a", "b", "aIsOther", "account_name", "toLowerCase", "includes", "bIs<PERSON>ther", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chart<PERSON>ey", "chartSettings", "isDataLoaded", "_reportData$roeRoa", "_reportData$expensesT", "_reportData$expensesT2", "_reportData$monthlyPe", "console", "log", "Object", "keys", "hasRoeRoaData", "roe<PERSON><PERSON>", "Array", "isArray", "length", "hasExpensesData", "expensesTopAccounts", "hasMonthlyExpensesData", "expensesTopAccountsMonthly", "hasMonthlyData", "monthlyPerformanceBreakDown", "hasPerformanceData", "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expensesLength", "monthlyExpensesLength", "monthlyDataLength", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "formatCurrency", "val", "options", "undefined", "isNaN", "showZero", "absVal", "Math", "abs", "toFixed", "processRoeRoaData", "roa<PERSON><PERSON>", "roeData", "categories", "map", "item", "value", "parseFloat", "roa", "roe", "period", "indexOf", "processExpensesPieData", "labels", "sortedExpenses", "total_expense", "expense", "percentage", "percentage_of_total", "expenseDisplay", "percentageDisplay", "processMonthlyExpensesData", "processDetailedMonthlyExpenses", "series", "monthlyData", "totalExpensesData", "totalExpenses", "name", "sortedExpensesData", "monthColumns", "account", "rawValue", "processWagesRevenueData", "income", "salariesGA", "salariesSales", "totalIncome", "expenses", "roaRoeCategories", "pieData", "<PERSON><PERSON><PERSON><PERSON>", "monthlyExpensesSeries", "monthlyCategories", "wagesCategories", "roa<PERSON><PERSON><PERSON><PERSON><PERSON>", "pieData<PERSON>ength", "monthlySeriesLength", "incomeDataLength", "colors", "roa<PERSON>oe", "expensesPie", "monthlyExpenses", "wagesRevenue", "roaRoeOptions", "chart", "type", "height", "toolbar", "show", "background", "dataLabels", "enabled", "formatter", "style", "fontSize", "fontWeight", "offsetY", "stroke", "curve", "width", "xaxis", "axisBorder", "axisTicks", "yaxis", "markers", "size", "strokeColors", "strokeWidth", "hover", "legend", "position", "horizontalAlign", "radius", "useSeriesColors", "itemMargin", "horizontal", "vertical", "tooltip", "y", "grid", "padding", "left", "right", "top", "bottom", "annotations", "borderColor", "borderWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity", "expensesPieOptions", "opts", "w", "globals", "seriesIndex", "dropShadow", "fontFamily", "offsetX", "plotOptions", "pie", "offset", "responsive", "breakpoint", "expensesMonthlyOptions", "stacked", "bar", "columnWidth", "total", "color", "onItemClick", "toggleDataSeries", "onItemHover", "highlightDataSeries", "wagesRevenueOptions", "enabledOnSeries", "fill", "fillOpacity", "shared", "intersect", "clearAndRender<PERSON>hart", "ref", "chartName", "current", "innerHTML", "setTimeout", "render", "error", "LoadingComponent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hasAnyUsableData", "join", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/reports/ReportPages/ExpenseSummary.jsx"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\r\nimport ApexCharts from \"apexcharts\";\r\n\r\nconst ExpenseSummaryDashboard = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  reportData = null,\r\n  contentSettings = null, // Add contentSettings prop\r\n}) => {\r\n  const roaRoeRef = useRef(null);\r\n  const expensesPieRef = useRef(null);\r\n  const expensesMonthlyRef = useRef(null);\r\n  const wagesRevenueRef = useRef(null);\r\n\r\n  // Helper function to sort data with \"Other\" last\r\n  const sortDataWithOtherLast = (data) => {\r\n    return data.sort((a, b) => {\r\n      const aIsOther = (a.account_name || '').toLowerCase().includes('other');\r\n      const bIsOther = (b.account_name || '').toLowerCase().includes('other');\r\n\r\n      if (aIsOther && !bIsOther) return 1;  // a (Other) goes after b\r\n      if (!aIsOther && bIsOther) return -1; // b (Other) goes after a\r\n      return 0; // maintain original order for non-Other items\r\n    });\r\n  };\r\n\r\n  // Function to check if a chart should be displayed based on content settings\r\n  const shouldDisplayChart = (chartKey) => {\r\n    if (!contentSettings?.chartSettings) return true; // Default to true if no settings\r\n    return contentSettings.chartSettings[chartKey] === true;\r\n  };\r\n\r\n  // Enhanced data validation function\r\n  const isDataLoaded = () => {\r\n    if (!reportData) {\r\n      console.log(\"ExpenseSummary - No reportData provided\");\r\n      return false;\r\n    }\r\n\r\n    console.log(\"ExpenseSummary - reportData keys:\", Object.keys(reportData));\r\n\r\n    // Check if at least some required data exists - make it more flexible\r\n    const hasRoeRoaData = reportData.roeRoa &&\r\n      Array.isArray(reportData.roeRoa) &&\r\n      reportData.roeRoa.length > 0;\r\n\r\n    const hasExpensesData = reportData.expensesTopAccounts &&\r\n      Array.isArray(reportData.expensesTopAccounts) &&\r\n      reportData.expensesTopAccounts.length > 0;\r\n\r\n    // For monthly expenses, check for new detailed breakdown data first, then fallback to performance data\r\n    const hasMonthlyExpensesData = reportData.expensesTopAccountsMonthly &&\r\n      Array.isArray(reportData.expensesTopAccountsMonthly) &&\r\n      reportData.expensesTopAccountsMonthly.length > 0;\r\n\r\n    const hasMonthlyData = hasMonthlyExpensesData ||\r\n      (reportData.monthlyPerformanceBreakDown &&\r\n        Array.isArray(reportData.monthlyPerformanceBreakDown) &&\r\n        reportData.monthlyPerformanceBreakDown.length > 0);\r\n\r\n    // For wages vs revenue, we'll derive from monthly performance data\r\n    const hasPerformanceData = hasMonthlyData;\r\n\r\n    console.log(\"ExpenseSummary - Data validation:\", {\r\n      hasRoeRoaData,\r\n      hasExpensesData,\r\n      hasMonthlyExpensesData,\r\n      hasMonthlyData,\r\n      hasPerformanceData,\r\n      roeRoaLength: reportData.roeRoa?.length || 0,\r\n      expensesLength: reportData.expensesTopAccounts?.length || 0,\r\n      monthlyExpensesLength: reportData.expensesTopAccountsMonthly?.length || 0,\r\n      monthlyDataLength: reportData.monthlyPerformanceBreakDown?.length || 0\r\n    });\r\n\r\n    // Return true if we have at least some data to work with\r\n    return hasRoeRoaData || hasExpensesData || hasMonthlyData;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      initializeCharts();\r\n    }\r\n  }, [reportData, contentSettings]); // Add contentSettings to dependency array\r\n\r\n  // Helper function to format month-year\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = [\r\n      \"Jan\",\r\n      \"Feb\",\r\n      \"Mar\",\r\n      \"Apr\",\r\n      \"May\",\r\n      \"Jun\",\r\n      \"Jul\",\r\n      \"Aug\",\r\n      \"Sep\",\r\n      \"Oct\",\r\n      \"Nov\",\r\n      \"Dec\",\r\n    ];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  // Helper function to format currency values with appropriate units\r\n  const formatCurrency = (val, options = {}) => {\r\n    if (val === null || val === undefined || isNaN(val) || val === 0) {\r\n      return options.showZero ? '$0' : '';\r\n    }\r\n\r\n    const absVal = Math.abs(val);\r\n\r\n    if (absVal >= 1000000) {\r\n      // Trillions\r\n      return '$' + (val / 1000000).toFixed(1) + 't';\r\n    } else if (absVal >= 1000) {\r\n      // Millions\r\n      return '$' + (val / 1000).toFixed(1) + 'm';\r\n    } else {\r\n      // Thousands (since our data is already in thousands)\r\n      return '$' + val.toFixed(1) + 'k';\r\n    }\r\n  };\r\n\r\n  // Process ROE/ROA data from API\r\n  const processRoeRoaData = () => {\r\n    if (!reportData?.roeRoa)\r\n      return { roaData: [], roeData: [], categories: [] };\r\n\r\n    const roaData = reportData.roeRoa.map((item) => {\r\n      const value = parseFloat(item.roa || 0);\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    const roeData = reportData.roeRoa.map((item) => {\r\n      const value = parseFloat(item.roe || 0);\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    // Create categories from the data if available\r\n    const categories = reportData.roeRoa.map((item) => {\r\n      if (item.year && item.month) {\r\n        return formatMonthYear(item.year, item.month);\r\n      }\r\n      return item.period || `Period ${reportData.roeRoa.indexOf(item) + 1}`;\r\n    });\r\n\r\n    return { roaData, roeData, categories };\r\n  };\r\n\r\n  // Process expenses pie chart data from API\r\n  const processExpensesPieData = () => {\r\n    if (!reportData?.expensesTopAccounts) return { data: [], labels: [] };\r\n\r\n    // Sort data with \"Other\" last\r\n    const sortedExpenses = sortDataWithOtherLast([...reportData.expensesTopAccounts]);\r\n\r\n    const data = sortedExpenses.map((item) => {\r\n      const value = parseFloat(item.total_expense || 0);\r\n      return isNaN(value) ? 0 : value / 1000; // Convert to thousands\r\n    });\r\n\r\n    const labels = sortedExpenses.map((item) => {\r\n      const expense = parseFloat(item.total_expense || 0);\r\n      const percentage = parseFloat(item.percentage_of_total || 0);\r\n      const expenseDisplay = isNaN(expense) ? \"0\" : (expense / 1000);\r\n      const percentageDisplay = isNaN(percentage) ? \"0\" : percentage;\r\n\r\n      return `${item.account_name || \"Unknown\"\r\n        } ${expenseDisplay}k (${percentageDisplay}%)`;\r\n    });\r\n\r\n    return { data, labels };\r\n  };\r\n\r\n  // Process monthly expenses data from API - use expensesTopAccountsMonthly for detailed account breakdown\r\n  const processMonthlyExpensesData = () => {\r\n    // Check if we have the new monthly expenses breakdown data\r\n    if (reportData?.expensesTopAccountsMonthly && Array.isArray(reportData.expensesTopAccountsMonthly)) {\r\n      return processDetailedMonthlyExpenses();\r\n    }\r\n\r\n    // Fallback to old method if new data is not available\r\n    if (!reportData?.monthlyPerformanceBreakDown) return { series: [], categories: [] };\r\n\r\n    // Get monthly data and create categories\r\n    const monthlyData = reportData.monthlyPerformanceBreakDown;\r\n    const categories = monthlyData.map(item =>\r\n      formatMonthYear(item.year, item.month)\r\n    );\r\n\r\n    // Since we don't have individual account breakdowns by month,\r\n    // we'll create a single series for total expenses\r\n    const totalExpensesData = monthlyData.map(item => {\r\n      const expense = parseFloat(item.totalExpenses || 0) / 1000; // Convert to thousands\r\n      return isNaN(expense) ? 0 : expense;\r\n    });\r\n\r\n    const series = [{\r\n      name: 'Total Expenses',\r\n      data: totalExpensesData\r\n    }];\r\n\r\n    return { series, categories };\r\n  };\r\n\r\n  // Process detailed monthly expenses breakdown by account\r\n  const processDetailedMonthlyExpenses = () => {\r\n    // Sort data with \"Other\" last\r\n    const sortedExpensesData = sortDataWithOtherLast([...reportData.expensesTopAccountsMonthly]);\r\n\r\n    // Define the month columns in order\r\n    const monthColumns = [\r\n      'apr_24', 'may_24', 'jun_24', 'jul_24', 'aug_24', 'sep_24',\r\n      'oct_24', 'nov_24', 'dec_24', 'jan_25', 'feb_25', 'mar_25'\r\n    ];\r\n\r\n    // Create categories (month labels)\r\n    const categories = [\r\n      'Apr 24', 'May 24', 'Jun 24', 'Jul 24', 'Aug 24', 'Sep 24',\r\n      'Oct 24', 'Nov 24', 'Dec 24', 'Jan 25', 'Feb 25', 'Mar 25'\r\n    ];\r\n\r\n    // Create series for each account (now sorted with Other last)\r\n    const series = sortedExpensesData.map(account => {\r\n      const data = monthColumns.map(month => {\r\n        // Handle string values from API by parsing them first\r\n        const rawValue = account[month] || '0';\r\n        const value = parseFloat(rawValue) / 1000; // Convert to thousands\r\n        return isNaN(value) ? 0 : value;\r\n      });\r\n\r\n      return {\r\n        name: account.account_name || 'Unknown',\r\n        data: data\r\n      };\r\n    });\r\n\r\n    return { series, categories };\r\n  };\r\n\r\n  // Process wages vs revenue data from monthly performance data\r\n  const processWagesRevenueData = () => {\r\n    if (!reportData?.monthlyPerformanceBreakDown)\r\n      return { income: [], salariesGA: [], salariesSales: [], categories: [] };\r\n\r\n    const monthlyData = reportData.monthlyPerformanceBreakDown;\r\n\r\n    const income = monthlyData.map((item) => {\r\n      const value = parseFloat(item.totalIncome || 0) / 1000000; // Convert to millions\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    // Since we don't have salary breakdown in the data, we'll estimate based on expenses\r\n    // This is a placeholder - you may want to add actual salary data to your API\r\n    const salariesGA = monthlyData.map((item) => {\r\n      const expenses = parseFloat(item.totalExpenses || 0);\r\n      // Estimate G&A salaries as 30% of total expenses\r\n      const value = (expenses * 0.3) / 1000000; // Convert to millions\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    const salariesSales = monthlyData.map((item) => {\r\n      const expenses = parseFloat(item.totalExpenses || 0);\r\n      // Estimate Sales salaries as 20% of total expenses\r\n      const value = (expenses * 0.2) / 1000000; // Convert to millions\r\n      return isNaN(value) ? 0 : value;\r\n    });\r\n\r\n    const categories = monthlyData.map((item) =>\r\n      formatMonthYear(item.year, item.month)\r\n    );\r\n\r\n    return { income, salariesGA, salariesSales, categories };\r\n  };\r\n\r\n  const initializeCharts = () => {\r\n    console.log(\"ExpenseSummary - Initializing charts with data:\", reportData);\r\n\r\n    const {\r\n      roaData,\r\n      roeData,\r\n      categories: roaRoeCategories,\r\n    } = processRoeRoaData();\r\n    const { data: pieData, labels: pieLabels } = processExpensesPieData();\r\n    const { series: monthlyExpensesSeries, categories: monthlyCategories } =\r\n      processMonthlyExpensesData();\r\n    const {\r\n      income,\r\n      salariesGA,\r\n      salariesSales,\r\n      categories: wagesCategories,\r\n    } = processWagesRevenueData();\r\n\r\n    console.log(\"ExpenseSummary - Processed data:\", {\r\n      roaDataLength: roaData.length,\r\n      pieDataLength: pieData.length,\r\n      monthlySeriesLength: monthlyExpensesSeries.length,\r\n      incomeDataLength: income.length,\r\n    });\r\n\r\n    // Chart colors\r\n    const colors = {\r\n      roaRoe: [\"#4a90e2\", \"#ff6b47\"],\r\n      expensesPie: [\r\n        \"#1f4e79\",\r\n        \"#20b2aa\",\r\n        \"#ff7f50\",\r\n        \"#4db6ac\",\r\n        \"#95a5a6\",\r\n        \"#5d6d7e\",\r\n        \"#bdc3c7\",\r\n        \"#ffab91\",\r\n        \"#9575cd\",\r\n        \"#ba68c8\",\r\n        \"#90a4ae\",\r\n      ],\r\n      monthlyExpenses: [\r\n        \"#1f4e79\",\r\n        \"#20b2aa\",\r\n        \"#ff7f50\",\r\n        \"#4db6ac\",\r\n        \"#95a5a6\",\r\n        \"#5d6d7e\",\r\n        \"#bdc3c7\",\r\n        \"#ffab91\",\r\n        \"#9575cd\",\r\n        \"#ba68c8\",\r\n        \"#90a4ae\",\r\n        \"#4a6fa5\",\r\n        \"#2d5f5f\",\r\n        \"#5f9ea0\",\r\n        \"#8b7d82\",\r\n        \"#4682b4\",\r\n        \"#b0c4de\",\r\n        \"#dda0dd\",\r\n        \"#87ceeb\",\r\n        \"#f0e68c\",\r\n        \"#d3d3d3\",\r\n      ],\r\n      wagesRevenue: [\"#20b2aa\", \"#4a4a9a\", \"#ff7f50\"],\r\n    };\r\n\r\n    // 1. ROA and ROE Line Chart\r\n    const roaRoeOptions = {\r\n      series: [\r\n        {\r\n          name: \"ROA\",\r\n          data: roaData,\r\n        },\r\n        {\r\n          name: \"ROE\",\r\n          data: roeData,\r\n        },\r\n      ],\r\n      chart: {\r\n        type: \"line\",\r\n        height: 300,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          if (val === null || val === undefined || isNaN(val)) return \"0\";\r\n          return val + \"%\";\r\n        },\r\n        style: {\r\n          fontSize: \"14px\",\r\n          colors: [\"#333\"],\r\n          fontWeight: \"500\",\r\n        },\r\n        background: {\r\n          enabled: false,\r\n        },\r\n        offsetY: -5,\r\n      },\r\n      stroke: {\r\n        curve: \"smooth\",\r\n        width: 2,\r\n      },\r\n      xaxis: {\r\n        categories: roaRoeCategories,\r\n        labels: {\r\n          style: {\r\n            colors: \"#666\",\r\n            fontSize: \"14px\",\r\n          },\r\n        },\r\n        axisBorder: {\r\n          show: false,\r\n        },\r\n        axisTicks: {\r\n          show: false,\r\n        },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n      },\r\n      colors: colors.roaRoe,\r\n      markers: {\r\n        size: 4,\r\n        strokeColors: \"#fff\",\r\n        strokeWidth: 1,\r\n        hover: { size: 6 },\r\n      },\r\n      legend: {\r\n        position: \"bottom\",\r\n        horizontalAlign: \"center\",\r\n        offsetY: 0,\r\n        markers: {\r\n          width: 8,\r\n          height: 8,\r\n          radius: 4,\r\n        },\r\n        labels: {\r\n          colors: [\"#333\"],\r\n          useSeriesColors: false,\r\n          fontSize: \"13px\",\r\n        },\r\n        itemMargin: {\r\n          horizontal: 20,\r\n          vertical: 0,\r\n        },\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            if (val === null || val === undefined || isNaN(val)) return \"0\";\r\n            return val + \"%\";\r\n          },\r\n        },\r\n      },\r\n\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0,\r\n        },\r\n      },\r\n      annotations: {\r\n        yaxis: [\r\n          {\r\n            y: 0,\r\n            borderColor: \"#999\",\r\n            borderWidth: 1,\r\n            strokeDashArray: 0,\r\n            opacity: 1,\r\n          },\r\n        ],\r\n      },\r\n    };\r\n\r\n    // 2. Expenses Pie Chart\r\n    const expensesPieOptions = {\r\n      series: pieData,\r\n      chart: {\r\n        type: \"pie\",\r\n        height: 400,\r\n        toolbar: { show: false },\r\n      },\r\n      labels: pieLabels,\r\n      colors: colors.expensesPie,\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val, opts) {\r\n          if (!opts || !opts.w || !opts.w.globals || !opts.w.globals.series)\r\n            return \"\";\r\n          const value = opts.w.globals.series[opts.seriesIndex];\r\n          return formatCurrency(value);\r\n        },\r\n        style: {\r\n          fontSize: \"11px\",\r\n          colors: [\"#fff\"],\r\n          fontWeight: \"500\",\r\n        },\r\n        dropShadow: {\r\n          enabled: false,\r\n        },\r\n      },\r\n      legend: {\r\n        position: \"right\",\r\n        fontSize: \"12px\",\r\n        fontWeight: \"400\",\r\n        markers: {\r\n          width: 10,\r\n          height: 10,\r\n          radius: 5,\r\n        },\r\n        labels: {\r\n          colors: \"#333\",\r\n          useSeriesColors: false,\r\n          fontFamily: \"Calibri\",\r\n        },\r\n        itemMargin: {\r\n          horizontal: 5,\r\n          vertical: 3,\r\n        },\r\n        offsetX: 0,\r\n      },\r\n      plotOptions: {\r\n        pie: {\r\n          dataLabels: {\r\n            offset: 0,\r\n          },\r\n        },\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return formatCurrency(val, { showZero: true });\r\n          },\r\n        },\r\n      },\r\n      stroke: {\r\n        show: false,\r\n      },\r\n      responsive: [\r\n        {\r\n          breakpoint: 768,\r\n          options: {\r\n            legend: { position: \"bottom\" },\r\n          },\r\n        },\r\n      ],\r\n    };\r\n\r\n    // 3. Monthly Expenses Stacked Chart\r\n    const expensesMonthlyOptions = {\r\n      series: monthlyExpensesSeries,\r\n      chart: {\r\n        type: \"bar\",\r\n        height: 450,\r\n        stacked: true,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          horizontal: false,\r\n          columnWidth: \"60%\",\r\n          dataLabels: {\r\n            total: {\r\n              enabled: true,\r\n              offsetY: -25,\r\n              style: {\r\n                fontSize: \"12px\",\r\n                fontWeight: \"600\",\r\n                color: \"#333\",\r\n              },\r\n              formatter: function (val) {\r\n                return formatCurrency(val);\r\n              },\r\n            },\r\n          },\r\n        },\r\n      },\r\n      dataLabels: {\r\n        enabled: false,\r\n        formatter: function (val) {\r\n          return formatCurrency(val);\r\n        },\r\n        style: {\r\n          fontSize: \"11px\",\r\n          fontWeight: \"500\",\r\n          colors: [\"#333\"],\r\n        },\r\n        offsetY: -5,\r\n        background: {\r\n          enabled: false,\r\n        },\r\n        dropShadow: {\r\n          enabled: false,\r\n        },\r\n      },\r\n      xaxis: {\r\n        categories: monthlyCategories,\r\n        labels: {\r\n          style: {\r\n            colors: \"#666\",\r\n            fontSize: \"14px\",\r\n          },\r\n        },\r\n        axisBorder: {\r\n          show: false,\r\n        },\r\n        axisTicks: {\r\n          show: false,\r\n        },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n      },\r\n      colors: colors.monthlyExpenses,\r\n      legend: {\r\n        position: \"bottom\",\r\n        fontSize: \"11px\",\r\n        fontWeight: \"400\",\r\n        markers: {\r\n          width: 8,\r\n          height: 8,\r\n          radius: 4,\r\n        },\r\n        labels: {\r\n          colors: \"#333\",\r\n          useSeriesColors: true,\r\n        },\r\n        itemMargin: {\r\n          horizontal: 8,\r\n          vertical: 3,\r\n        },\r\n        offsetY: 10,\r\n        onItemClick: {\r\n          toggleDataSeries: true,\r\n        },\r\n        onItemHover: {\r\n          highlightDataSeries: true,\r\n        },\r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function (val) {\r\n            return formatCurrency(val, { showZero: true });\r\n          },\r\n        },\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0,\r\n        },\r\n      },\r\n      stroke: {\r\n        show: false,\r\n      },\r\n    };\r\n\r\n    // 4. Wages vs Revenue Chart (using real data from API)\r\n    const wagesRevenueOptions = {\r\n      series: [\r\n        {\r\n          name: \"Income\",\r\n          type: \"line\",\r\n          data: income,\r\n        },\r\n        {\r\n          name: \"Salaries - G&A\",\r\n          type: \"column\",\r\n          data: salariesGA,\r\n        },\r\n        {\r\n          name: \"Salaries - Sales\",\r\n          type: \"column\",\r\n          data: salariesSales,\r\n        },\r\n      ],\r\n      chart: {\r\n        height: 400,\r\n        type: \"line\",\r\n        stacked: true,\r\n        toolbar: { show: false },\r\n        background: \"transparent\",\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        enabledOnSeries: [0],\r\n        formatter: function (val, opts) {\r\n          if (opts.seriesIndex === 0) {\r\n            if (val === null || val === undefined || isNaN(val)) return \"\";\r\n            return \"$\" + val + \"m\";\r\n          }\r\n          return \"\";\r\n        },\r\n        style: {\r\n          fontSize: \"14px\",\r\n          colors: [\"#20b2aa\"],\r\n          fontWeight: \"500\",\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false,\r\n        },\r\n        dropShadow: {\r\n          enabled: false,\r\n        },\r\n      },\r\n      stroke: {\r\n        width: [2, 0, 0],\r\n        curve: \"smooth\",\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          columnWidth: \"40%\",\r\n          height: 1900,\r\n          dataLabels: {\r\n            total: {\r\n              enabled: true,\r\n              offsetY: -20,\r\n              style: {\r\n                fontSize: \"14px\",\r\n                fontWeight: \"500\",\r\n                color: \"#333\",\r\n              },\r\n              formatter: function (val) {\r\n                if (val === null || val === undefined || isNaN(val))\r\n                  return \"$0\";\r\n                if (val >= 1) {\r\n                  return \"$\" + val + \"m\";\r\n                } else {\r\n                  return \"$\" + (val * 1000) + \"k\";\r\n                }\r\n              },\r\n            },\r\n          },\r\n        },\r\n      },\r\n      fill: {\r\n        opacity: [1, 1, 1],\r\n      },\r\n      labels: wagesCategories,\r\n      markers: {\r\n        size: [5, 0, 0],\r\n        fontSize: \"14px\",\r\n        strokeColors: \"#fff\",\r\n        strokeWidth: 2,\r\n        fillOpacity: 1,\r\n        hover: {\r\n          size: 7,\r\n        },\r\n      },\r\n      xaxis: {\r\n        labels: {\r\n          style: {\r\n            colors: \"#666\",\r\n            fontSize: \"12px\",\r\n          },\r\n        },\r\n        axisBorder: {\r\n          show: false,\r\n        },\r\n        axisTicks: {\r\n          show: false,\r\n        },\r\n      },\r\n      yaxis: {\r\n        show: false,\r\n      },\r\n      colors: colors.wagesRevenue,\r\n      legend: {\r\n        position: \"bottom\",\r\n        horizontalAlign: \"center\",\r\n        fontSize: \"14px\",\r\n        fontWeight: \"400\",\r\n        markers: {\r\n          width: 8,\r\n          height: 8,\r\n          radius: 4,\r\n        },\r\n        labels: {\r\n          colors: \"#333\",\r\n          useSeriesColors: false,\r\n        },\r\n        itemMargin: {\r\n          horizontal: 15,\r\n          vertical: 4,\r\n        },\r\n        offsetY: 10,\r\n        onItemClick: {\r\n          toggleDataSeries: false,\r\n        },\r\n        onItemHover: {\r\n          highlightDataSeries: false,\r\n        },\r\n      },\r\n      tooltip: {\r\n        shared: true,\r\n        intersect: false,\r\n        y: [\r\n          {\r\n            formatter: function (val) {\r\n              if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n              return \"$\" + val + \" million\";\r\n            },\r\n          },\r\n          {\r\n            formatter: function (val) {\r\n              if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n              return \"$\" + val + \" million\";\r\n            },\r\n          },\r\n          {\r\n            formatter: function (val) {\r\n              if (val === null || val === undefined || isNaN(val)) return \"$0\";\r\n              return \"$\" + val + \" million\";\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0,\r\n        },\r\n      },\r\n    };\r\n\r\n    // Clear existing charts before rendering new ones\r\n    const clearAndRenderChart = (ref, options, chartName) => {\r\n      if (ref.current) {\r\n        // Clear any existing chart\r\n        ref.current.innerHTML = \"\";\r\n\r\n        // Wait a tick before rendering to ensure DOM is cleared\r\n        setTimeout(() => {\r\n          if (ref.current) {\r\n            try {\r\n              console.log(`ExpenseSummary - Rendering ${chartName} chart`);\r\n              const chart = new ApexCharts(ref.current, options);\r\n              chart.render();\r\n            } catch (error) {\r\n              console.error(\r\n                `ExpenseSummary - Error rendering ${chartName} chart:`,\r\n                error\r\n              );\r\n            }\r\n          }\r\n        }, 10);\r\n      }\r\n    };\r\n\r\n    // Only render charts if we have data for them AND they are enabled in content settings\r\n    if ((roaData.length > 0 || roeData.length > 0) && shouldDisplayChart('roaAndRoe')) {\r\n      clearAndRenderChart(roaRoeRef, roaRoeOptions, \"ROA/ROE\");\r\n    }\r\n\r\n    // Note: There's no direct mapping for expenses pie chart in the settings, so we'll assume it's always shown\r\n    // You might want to add a specific setting for this\r\n    if (pieData.length > 0) {\r\n      clearAndRenderChart(expensesPieRef, expensesPieOptions, \"Expenses Pie\");\r\n    }\r\n\r\n    // Note: There's no direct mapping for monthly expenses in the settings, so we'll assume it's always shown\r\n    // You might want to add a specific setting for this\r\n    if (monthlyExpensesSeries.length > 0) {\r\n      clearAndRenderChart(\r\n        expensesMonthlyRef,\r\n        expensesMonthlyOptions,\r\n        \"Monthly Expenses\"\r\n      );\r\n    }\r\n\r\n    // Note: There's no direct mapping for wages vs revenue in the settings, so we'll assume it's always shown\r\n    // You might want to add a specific setting for this\r\n    if (income.length > 0) {\r\n      clearAndRenderChart(\r\n        wagesRevenueRef,\r\n        wagesRevenueOptions,\r\n        \"Wages vs Revenue\"\r\n      );\r\n    }\r\n  };\r\n\r\n  // Enhanced loading component\r\n  const LoadingComponent = () => (\r\n    <div className=\"min-h-screen p-5\">\r\n      <div className=\"max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8\">\r\n        <div className=\"flex items-center justify-center h-64\">\r\n          <div className=\"text-center\">\r\n            {/* Loading spinner */}\r\n            <div className=\"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4\"></div>\r\n            <div className=\"text-xl text-gray-600 mb-2\">\r\n              Loading expense data...\r\n            </div>\r\n            <div className=\"text-sm text-gray-500\">\r\n              Please wait while we fetch your expense information\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Show loading if data is not properly loaded\r\n  if (!isDataLoaded()) {\r\n    return <LoadingComponent />;\r\n  }\r\n\r\n  // Add a fallback if reportData exists but has no usable data\r\n  const hasAnyUsableData = () => {\r\n    const { roaData, roeData } = processRoeRoaData();\r\n    const { data: pieData } = processExpensesPieData();\r\n    const { series: monthlyExpensesSeries } = processMonthlyExpensesData();\r\n    const { income } = processWagesRevenueData();\r\n\r\n    return (\r\n      roaData.length > 0 ||\r\n      roeData.length > 0 ||\r\n      pieData.length > 0 ||\r\n      monthlyExpensesSeries.length > 0 ||\r\n      income.length > 0\r\n    );\r\n  };\r\n\r\n  if (!hasAnyUsableData()) {\r\n    return (\r\n      <div className=\"min-h-screen p-5\">\r\n        <div className=\"max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8\">\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-xl text-gray-600 mb-2\">\r\n                No expense data available\r\n              </div>\r\n              <div className=\"text-sm text-gray-500\">\r\n                The data structure is available but charts cannot be rendered\r\n              </div>\r\n              <div className=\"text-xs text-gray-400 mt-2\">\r\n                Available data: {Object.keys(reportData || {}).join(\", \")}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-5\">\r\n      {/* Main Container */}\r\n      <div className=\"max-w-6xl mx-auto bg-white flex flex-col gap-10 p-10 mb-8\">\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Expense Summary\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            January 2025 | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Conditionally render Return on Assets and Equity Chart */}\r\n        {shouldDisplayChart('roaAndRoe') && (\r\n          <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n            <div\r\n              className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n              style={subHeadingTextStyle}\r\n            >\r\n              Return on Assets and Equity\r\n            </div>\r\n            <div ref={roaRoeRef}></div>\r\n\r\n            {/* Metrics Explanations */}\r\n            <div className=\"mt-6\">\r\n              <div className=\"mb-4\">\r\n                <div\r\n                  className=\"text-teal-600 text-xl font-semibold\"\r\n                  style={{ ...subHeadingTextStyle, fontWeight: \"lighter\" }}\r\n                >\r\n                  Return on Assets\r\n                </div>\r\n                <p\r\n                  className=\"text-gray-700 leading-relaxed\"\r\n                  style={contentTextStyle}\r\n                >\r\n                  Indicates how well Acme Print is using capital invested in\r\n                  Assets to generate Total Income. The higher the return, the more\r\n                  productive and efficient management is in utilizing economic\r\n                  resources the business has.\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"mb-10\">\r\n                <div\r\n                  className=\"text-teal-600 text-xl font-semibold\"\r\n                  style={{ ...subHeadingTextStyle, fontWeight: \"lighter\" }}\r\n                >\r\n                  Return on Equity\r\n                </div>\r\n                <p\r\n                  className=\"text-gray-700 leading-relaxed\"\r\n                  style={contentTextStyle}\r\n                >\r\n                  Indicates how efficient company management is at generating\r\n                  growth from its Equity financing. Because Equity is equal to a\r\n                  company's Assets minus Liabilities, ROE is also considered the\r\n                  Return on Net Assets.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Expenses: Top Accounts Pie Chart - Always show for now */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-24\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Expenses: Top Accounts\r\n          </div>\r\n          <div ref={expensesPieRef} className=\"mb-12\"></div>\r\n        </div>\r\n\r\n      </div>\r\n\r\n      <div className=\"max-w-6xl mx-auto bg-white flex flex-col gap-10 p-10 mb-8\">\r\n\r\n\r\n        <div className=\"component-header flex items-center justify-between gap-4 pt-10 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n            Expense Summary\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            January 2025 | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Expenses: Top Accounts Monthly - Always show for now */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900 expenses-monthly-apex\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Expenses: Top Accounts Monthly\r\n          </div>\r\n          <div ref={expensesMonthlyRef}></div>\r\n        </div>\r\n\r\n        {/* Expenses: Wages Vs Revenue Monthly - Always show for now */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Expenses: Wages Vs Revenue Monthly\r\n          </div>\r\n          <div ref={wagesRevenueRef} className=\"mb-32\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExpenseSummaryDashboard;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,uBAAuB,GAAGA,CAAC;EAC/BC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,UAAU,GAAG,IAAI;EACjBC,eAAe,GAAG,IAAI,CAAE;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,SAAS,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMa,cAAc,GAAGb,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMc,kBAAkB,GAAGd,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMe,eAAe,GAAGf,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMgB,qBAAqB,GAAIC,IAAI,IAAK;IACtC,OAAOA,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACzB,MAAMC,QAAQ,GAAG,CAACF,CAAC,CAACG,YAAY,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC;MACvE,MAAMC,QAAQ,GAAG,CAACL,CAAC,CAACE,YAAY,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC;MAEvE,IAAIH,QAAQ,IAAI,CAACI,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAE;MACtC,IAAI,CAACJ,QAAQ,IAAII,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;MACtC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,QAAQ,IAAK;IACvC,IAAI,EAACjB,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEkB,aAAa,GAAE,OAAO,IAAI,CAAC,CAAC;IAClD,OAAOlB,eAAe,CAACkB,aAAa,CAACD,QAAQ,CAAC,KAAK,IAAI;EACzD,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IAAA,IAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;IACzB,IAAI,CAACxB,UAAU,EAAE;MACfyB,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,OAAO,KAAK;IACd;IAEAD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEC,MAAM,CAACC,IAAI,CAAC5B,UAAU,CAAC,CAAC;;IAEzE;IACA,MAAM6B,aAAa,GAAG7B,UAAU,CAAC8B,MAAM,IACrCC,KAAK,CAACC,OAAO,CAAChC,UAAU,CAAC8B,MAAM,CAAC,IAChC9B,UAAU,CAAC8B,MAAM,CAACG,MAAM,GAAG,CAAC;IAE9B,MAAMC,eAAe,GAAGlC,UAAU,CAACmC,mBAAmB,IACpDJ,KAAK,CAACC,OAAO,CAAChC,UAAU,CAACmC,mBAAmB,CAAC,IAC7CnC,UAAU,CAACmC,mBAAmB,CAACF,MAAM,GAAG,CAAC;;IAE3C;IACA,MAAMG,sBAAsB,GAAGpC,UAAU,CAACqC,0BAA0B,IAClEN,KAAK,CAACC,OAAO,CAAChC,UAAU,CAACqC,0BAA0B,CAAC,IACpDrC,UAAU,CAACqC,0BAA0B,CAACJ,MAAM,GAAG,CAAC;IAElD,MAAMK,cAAc,GAAGF,sBAAsB,IAC1CpC,UAAU,CAACuC,2BAA2B,IACrCR,KAAK,CAACC,OAAO,CAAChC,UAAU,CAACuC,2BAA2B,CAAC,IACrDvC,UAAU,CAACuC,2BAA2B,CAACN,MAAM,GAAG,CAAE;;IAEtD;IACA,MAAMO,kBAAkB,GAAGF,cAAc;IAEzCb,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAC/CG,aAAa;MACbK,eAAe;MACfE,sBAAsB;MACtBE,cAAc;MACdE,kBAAkB;MAClBC,YAAY,EAAE,EAAApB,kBAAA,GAAArB,UAAU,CAAC8B,MAAM,cAAAT,kBAAA,uBAAjBA,kBAAA,CAAmBY,MAAM,KAAI,CAAC;MAC5CS,cAAc,EAAE,EAAApB,qBAAA,GAAAtB,UAAU,CAACmC,mBAAmB,cAAAb,qBAAA,uBAA9BA,qBAAA,CAAgCW,MAAM,KAAI,CAAC;MAC3DU,qBAAqB,EAAE,EAAApB,sBAAA,GAAAvB,UAAU,CAACqC,0BAA0B,cAAAd,sBAAA,uBAArCA,sBAAA,CAAuCU,MAAM,KAAI,CAAC;MACzEW,iBAAiB,EAAE,EAAApB,qBAAA,GAAAxB,UAAU,CAACuC,2BAA2B,cAAAf,qBAAA,uBAAtCA,qBAAA,CAAwCS,MAAM,KAAI;IACvE,CAAC,CAAC;;IAEF;IACA,OAAOJ,aAAa,IAAIK,eAAe,IAAII,cAAc;EAC3D,CAAC;EAEDhD,SAAS,CAAC,MAAM;IACd,IAAI8B,YAAY,CAAC,CAAC,EAAE;MAClByB,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC7C,UAAU,EAAEC,eAAe,CAAC,CAAC,CAAC,CAAC;;EAEnC;EACA,MAAM6C,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CACjB,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;IACD,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAACC,GAAG,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5C,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,IAAIA,GAAG,KAAK,CAAC,EAAE;MAChE,OAAOC,OAAO,CAACG,QAAQ,GAAG,IAAI,GAAG,EAAE;IACrC;IAEA,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACP,GAAG,CAAC;IAE5B,IAAIK,MAAM,IAAI,OAAO,EAAE;MACrB;MACA,OAAO,GAAG,GAAG,CAACL,GAAG,GAAG,OAAO,EAAEQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC/C,CAAC,MAAM,IAAIH,MAAM,IAAI,IAAI,EAAE;MACzB;MACA,OAAO,GAAG,GAAG,CAACL,GAAG,GAAG,IAAI,EAAEQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC5C,CAAC,MAAM;MACL;MACA,OAAO,GAAG,GAAGR,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACnC;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,EAAC9D,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE8B,MAAM,GACrB,OAAO;MAAEiC,OAAO,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAG,CAAC;IAErD,MAAMF,OAAO,GAAG/D,UAAU,CAAC8B,MAAM,CAACoC,GAAG,CAAEC,IAAI,IAAK;MAC9C,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACG,GAAG,IAAI,CAAC,CAAC;MACvC,OAAOd,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;IAEF,MAAMJ,OAAO,GAAGhE,UAAU,CAAC8B,MAAM,CAACoC,GAAG,CAAEC,IAAI,IAAK;MAC9C,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACI,GAAG,IAAI,CAAC,CAAC;MACvC,OAAOf,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;;IAEF;IACA,MAAMH,UAAU,GAAGjE,UAAU,CAAC8B,MAAM,CAACoC,GAAG,CAAEC,IAAI,IAAK;MACjD,IAAIA,IAAI,CAACpB,IAAI,IAAIoB,IAAI,CAACnB,KAAK,EAAE;QAC3B,OAAOF,eAAe,CAACqB,IAAI,CAACpB,IAAI,EAAEoB,IAAI,CAACnB,KAAK,CAAC;MAC/C;MACA,OAAOmB,IAAI,CAACK,MAAM,IAAI,UAAUxE,UAAU,CAAC8B,MAAM,CAAC2C,OAAO,CAACN,IAAI,CAAC,GAAG,CAAC,EAAE;IACvE,CAAC,CAAC;IAEF,OAAO;MAAEJ,OAAO;MAAEC,OAAO;MAAEC;IAAW,CAAC;EACzC,CAAC;;EAED;EACA,MAAMS,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,EAAC1E,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEmC,mBAAmB,GAAE,OAAO;MAAE3B,IAAI,EAAE,EAAE;MAAEmE,MAAM,EAAE;IAAG,CAAC;;IAErE;IACA,MAAMC,cAAc,GAAGrE,qBAAqB,CAAC,CAAC,GAAGP,UAAU,CAACmC,mBAAmB,CAAC,CAAC;IAEjF,MAAM3B,IAAI,GAAGoE,cAAc,CAACV,GAAG,CAAEC,IAAI,IAAK;MACxC,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAACU,aAAa,IAAI,CAAC,CAAC;MACjD,OAAOrB,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,IAAI,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,MAAMO,MAAM,GAAGC,cAAc,CAACV,GAAG,CAAEC,IAAI,IAAK;MAC1C,MAAMW,OAAO,GAAGT,UAAU,CAACF,IAAI,CAACU,aAAa,IAAI,CAAC,CAAC;MACnD,MAAME,UAAU,GAAGV,UAAU,CAACF,IAAI,CAACa,mBAAmB,IAAI,CAAC,CAAC;MAC5D,MAAMC,cAAc,GAAGzB,KAAK,CAACsB,OAAO,CAAC,GAAG,GAAG,GAAIA,OAAO,GAAG,IAAK;MAC9D,MAAMI,iBAAiB,GAAG1B,KAAK,CAACuB,UAAU,CAAC,GAAG,GAAG,GAAGA,UAAU;MAE9D,OAAO,GAAGZ,IAAI,CAACtD,YAAY,IAAI,SAAS,IAClCoE,cAAc,MAAMC,iBAAiB,IAAI;IACjD,CAAC,CAAC;IAEF,OAAO;MAAE1E,IAAI;MAAEmE;IAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMQ,0BAA0B,GAAGA,CAAA,KAAM;IACvC;IACA,IAAInF,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEqC,0BAA0B,IAAIN,KAAK,CAACC,OAAO,CAAChC,UAAU,CAACqC,0BAA0B,CAAC,EAAE;MAClG,OAAO+C,8BAA8B,CAAC,CAAC;IACzC;;IAEA;IACA,IAAI,EAACpF,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEuC,2BAA2B,GAAE,OAAO;MAAE8C,MAAM,EAAE,EAAE;MAAEpB,UAAU,EAAE;IAAG,CAAC;;IAEnF;IACA,MAAMqB,WAAW,GAAGtF,UAAU,CAACuC,2BAA2B;IAC1D,MAAM0B,UAAU,GAAGqB,WAAW,CAACpB,GAAG,CAACC,IAAI,IACrCrB,eAAe,CAACqB,IAAI,CAACpB,IAAI,EAAEoB,IAAI,CAACnB,KAAK,CACvC,CAAC;;IAED;IACA;IACA,MAAMuC,iBAAiB,GAAGD,WAAW,CAACpB,GAAG,CAACC,IAAI,IAAI;MAChD,MAAMW,OAAO,GAAGT,UAAU,CAACF,IAAI,CAACqB,aAAa,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;MAC5D,OAAOhC,KAAK,CAACsB,OAAO,CAAC,GAAG,CAAC,GAAGA,OAAO;IACrC,CAAC,CAAC;IAEF,MAAMO,MAAM,GAAG,CAAC;MACdI,IAAI,EAAE,gBAAgB;MACtBjF,IAAI,EAAE+E;IACR,CAAC,CAAC;IAEF,OAAO;MAAEF,MAAM;MAAEpB;IAAW,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMmB,8BAA8B,GAAGA,CAAA,KAAM;IAC3C;IACA,MAAMM,kBAAkB,GAAGnF,qBAAqB,CAAC,CAAC,GAAGP,UAAU,CAACqC,0BAA0B,CAAC,CAAC;;IAE5F;IACA,MAAMsD,YAAY,GAAG,CACnB,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAC1D,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAC3D;;IAED;IACA,MAAM1B,UAAU,GAAG,CACjB,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAC1D,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAC3D;;IAED;IACA,MAAMoB,MAAM,GAAGK,kBAAkB,CAACxB,GAAG,CAAC0B,OAAO,IAAI;MAC/C,MAAMpF,IAAI,GAAGmF,YAAY,CAACzB,GAAG,CAAClB,KAAK,IAAI;QACrC;QACA,MAAM6C,QAAQ,GAAGD,OAAO,CAAC5C,KAAK,CAAC,IAAI,GAAG;QACtC,MAAMoB,KAAK,GAAGC,UAAU,CAACwB,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3C,OAAOrC,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;MACjC,CAAC,CAAC;MAEF,OAAO;QACLqB,IAAI,EAAEG,OAAO,CAAC/E,YAAY,IAAI,SAAS;QACvCL,IAAI,EAAEA;MACR,CAAC;IACH,CAAC,CAAC;IAEF,OAAO;MAAE6E,MAAM;MAAEpB;IAAW,CAAC;EAC/B,CAAC;;EAED;EACA,MAAM6B,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,EAAC9F,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEuC,2BAA2B,GAC1C,OAAO;MAAEwD,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE,EAAE;MAAEC,aAAa,EAAE,EAAE;MAAEhC,UAAU,EAAE;IAAG,CAAC;IAE1E,MAAMqB,WAAW,GAAGtF,UAAU,CAACuC,2BAA2B;IAE1D,MAAMwD,MAAM,GAAGT,WAAW,CAACpB,GAAG,CAAEC,IAAI,IAAK;MACvC,MAAMC,KAAK,GAAGC,UAAU,CAACF,IAAI,CAAC+B,WAAW,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;MAC3D,OAAO1C,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;;IAEF;IACA;IACA,MAAM4B,UAAU,GAAGV,WAAW,CAACpB,GAAG,CAAEC,IAAI,IAAK;MAC3C,MAAMgC,QAAQ,GAAG9B,UAAU,CAACF,IAAI,CAACqB,aAAa,IAAI,CAAC,CAAC;MACpD;MACA,MAAMpB,KAAK,GAAI+B,QAAQ,GAAG,GAAG,GAAI,OAAO,CAAC,CAAC;MAC1C,OAAO3C,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;IAEF,MAAM6B,aAAa,GAAGX,WAAW,CAACpB,GAAG,CAAEC,IAAI,IAAK;MAC9C,MAAMgC,QAAQ,GAAG9B,UAAU,CAACF,IAAI,CAACqB,aAAa,IAAI,CAAC,CAAC;MACpD;MACA,MAAMpB,KAAK,GAAI+B,QAAQ,GAAG,GAAG,GAAI,OAAO,CAAC,CAAC;MAC1C,OAAO3C,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IACjC,CAAC,CAAC;IAEF,MAAMH,UAAU,GAAGqB,WAAW,CAACpB,GAAG,CAAEC,IAAI,IACtCrB,eAAe,CAACqB,IAAI,CAACpB,IAAI,EAAEoB,IAAI,CAACnB,KAAK,CACvC,CAAC;IAED,OAAO;MAAE+C,MAAM;MAAEC,UAAU;MAAEC,aAAa;MAAEhC;IAAW,CAAC;EAC1D,CAAC;EAED,MAAMpB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE1B,UAAU,CAAC;IAE1E,MAAM;MACJ+D,OAAO;MACPC,OAAO;MACPC,UAAU,EAAEmC;IACd,CAAC,GAAGtC,iBAAiB,CAAC,CAAC;IACvB,MAAM;MAAEtD,IAAI,EAAE6F,OAAO;MAAE1B,MAAM,EAAE2B;IAAU,CAAC,GAAG5B,sBAAsB,CAAC,CAAC;IACrE,MAAM;MAAEW,MAAM,EAAEkB,qBAAqB;MAAEtC,UAAU,EAAEuC;IAAkB,CAAC,GACpErB,0BAA0B,CAAC,CAAC;IAC9B,MAAM;MACJY,MAAM;MACNC,UAAU;MACVC,aAAa;MACbhC,UAAU,EAAEwC;IACd,CAAC,GAAGX,uBAAuB,CAAC,CAAC;IAE7BrE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;MAC9CgF,aAAa,EAAE3C,OAAO,CAAC9B,MAAM;MAC7B0E,aAAa,EAAEN,OAAO,CAACpE,MAAM;MAC7B2E,mBAAmB,EAAEL,qBAAqB,CAACtE,MAAM;MACjD4E,gBAAgB,EAAEd,MAAM,CAAC9D;IAC3B,CAAC,CAAC;;IAEF;IACA,MAAM6E,MAAM,GAAG;MACbC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAC9BC,WAAW,EAAE,CACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,eAAe,EAAE,CACf,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;MACDC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;IAChD,CAAC;;IAED;IACA,MAAMC,aAAa,GAAG;MACpB9B,MAAM,EAAE,CACN;QACEI,IAAI,EAAE,KAAK;QACXjF,IAAI,EAAEuD;MACR,CAAC,EACD;QACE0B,IAAI,EAAE,KAAK;QACXjF,IAAI,EAAEwD;MACR,CAAC,CACF;MACDoD,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;UACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,GAAG;UAC/D,OAAOA,GAAG,GAAG,GAAG;QAClB,CAAC;QACDwE,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBhB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBiB,UAAU,EAAE;QACd,CAAC;QACDN,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDK,OAAO,EAAE,CAAC;MACZ,CAAC;MACDC,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE;MACT,CAAC;MACDC,KAAK,EAAE;QACLnE,UAAU,EAAEmC,gBAAgB;QAC5BzB,MAAM,EAAE;UACNkD,KAAK,EAAE;YACLf,MAAM,EAAE,MAAM;YACdgB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDO,UAAU,EAAE;UACVb,IAAI,EAAE;QACR,CAAC;QACDc,SAAS,EAAE;UACTd,IAAI,EAAE;QACR;MACF,CAAC;MACDe,KAAK,EAAE;QACLf,IAAI,EAAE;MACR,CAAC;MACDV,MAAM,EAAEA,MAAM,CAACC,MAAM;MACrByB,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UAAEH,IAAI,EAAE;QAAE;MACnB,CAAC;MACDI,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,QAAQ;QACzBf,OAAO,EAAE,CAAC;QACVQ,OAAO,EAAE;UACPL,KAAK,EAAE,CAAC;UACRb,MAAM,EAAE,CAAC;UACT0B,MAAM,EAAE;QACV,CAAC;QACDrE,MAAM,EAAE;UACNmC,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBmC,eAAe,EAAE,KAAK;UACtBnB,QAAQ,EAAE;QACZ,CAAC;QACDoB,UAAU,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,QAAQ,EAAE;QACZ;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,CAAC,EAAE;UACD1B,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,GAAG;YAC/D,OAAOA,GAAG,GAAG,GAAG;UAClB;QACF;MACF,CAAC;MAEDkG,IAAI,EAAE;QACJ/B,IAAI,EAAE,KAAK;QACXgC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,WAAW,EAAE;QACXtB,KAAK,EAAE,CACL;UACEe,CAAC,EAAE,CAAC;UACJQ,WAAW,EAAE,MAAM;UACnBC,WAAW,EAAE,CAAC;UACdC,eAAe,EAAE,CAAC;UAClBC,OAAO,EAAE;QACX,CAAC;MAEL;IACF,CAAC;;IAED;IACA,MAAMC,kBAAkB,GAAG;MACzB7E,MAAM,EAAEgB,OAAO;MACfe,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM;MACzB,CAAC;MACD7C,MAAM,EAAE2B,SAAS;MACjBQ,MAAM,EAAEA,MAAM,CAACE,WAAW;MAC1BU,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE8G,IAAI,EAAE;UAC9B,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,CAAC,IAAI,CAACD,IAAI,CAACC,CAAC,CAACC,OAAO,IAAI,CAACF,IAAI,CAACC,CAAC,CAACC,OAAO,CAAChF,MAAM,EAC/D,OAAO,EAAE;UACX,MAAMjB,KAAK,GAAG+F,IAAI,CAACC,CAAC,CAACC,OAAO,CAAChF,MAAM,CAAC8E,IAAI,CAACG,WAAW,CAAC;UACrD,OAAOlH,cAAc,CAACgB,KAAK,CAAC;QAC9B,CAAC;QACDyD,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBhB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBiB,UAAU,EAAE;QACd,CAAC;QACDwC,UAAU,EAAE;UACV5C,OAAO,EAAE;QACX;MACF,CAAC;MACDkB,MAAM,EAAE;QACNC,QAAQ,EAAE,OAAO;QACjBhB,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBS,OAAO,EAAE;UACPL,KAAK,EAAE,EAAE;UACTb,MAAM,EAAE,EAAE;UACV0B,MAAM,EAAE;QACV,CAAC;QACDrE,MAAM,EAAE;UACNmC,MAAM,EAAE,MAAM;UACdmC,eAAe,EAAE,KAAK;UACtBuB,UAAU,EAAE;QACd,CAAC;QACDtB,UAAU,EAAE;UACVC,UAAU,EAAE,CAAC;UACbC,QAAQ,EAAE;QACZ,CAAC;QACDqB,OAAO,EAAE;MACX,CAAC;MACDC,WAAW,EAAE;QACXC,GAAG,EAAE;UACHjD,UAAU,EAAE;YACVkD,MAAM,EAAE;UACV;QACF;MACF,CAAC;MACDvB,OAAO,EAAE;QACPC,CAAC,EAAE;UACD1B,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;YACxB,OAAOD,cAAc,CAACC,GAAG,EAAE;cAAEI,QAAQ,EAAE;YAAK,CAAC,CAAC;UAChD;QACF;MACF,CAAC;MACDwE,MAAM,EAAE;QACNT,IAAI,EAAE;MACR,CAAC;MACDqD,UAAU,EAAE,CACV;QACEC,UAAU,EAAE,GAAG;QACfxH,OAAO,EAAE;UACPuF,MAAM,EAAE;YAAEC,QAAQ,EAAE;UAAS;QAC/B;MACF,CAAC;IAEL,CAAC;;IAED;IACA,MAAMiC,sBAAsB,GAAG;MAC7B1F,MAAM,EAAEkB,qBAAqB;MAC7Ba,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,GAAG;QACX0D,OAAO,EAAE,IAAI;QACbzD,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDiD,WAAW,EAAE;QACXO,GAAG,EAAE;UACH9B,UAAU,EAAE,KAAK;UACjB+B,WAAW,EAAE,KAAK;UAClBxD,UAAU,EAAE;YACVyD,KAAK,EAAE;cACLxD,OAAO,EAAE,IAAI;cACbK,OAAO,EAAE,CAAC,EAAE;cACZH,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBqD,KAAK,EAAE;cACT,CAAC;cACDxD,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;gBACxB,OAAOD,cAAc,CAACC,GAAG,CAAC;cAC5B;YACF;UACF;QACF;MACF,CAAC;MACDqE,UAAU,EAAE;QACVC,OAAO,EAAE,KAAK;QACdC,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;UACxB,OAAOD,cAAc,CAACC,GAAG,CAAC;QAC5B,CAAC;QACDwE,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBjB,MAAM,EAAE,CAAC,MAAM;QACjB,CAAC;QACDkB,OAAO,EAAE,CAAC,CAAC;QACXP,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACD4C,UAAU,EAAE;UACV5C,OAAO,EAAE;QACX;MACF,CAAC;MACDS,KAAK,EAAE;QACLnE,UAAU,EAAEuC,iBAAiB;QAC7B7B,MAAM,EAAE;UACNkD,KAAK,EAAE;YACLf,MAAM,EAAE,MAAM;YACdgB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDO,UAAU,EAAE;UACVb,IAAI,EAAE;QACR,CAAC;QACDc,SAAS,EAAE;UACTd,IAAI,EAAE;QACR;MACF,CAAC;MACDe,KAAK,EAAE;QACLf,IAAI,EAAE;MACR,CAAC;MACDV,MAAM,EAAEA,MAAM,CAACG,eAAe;MAC9B4B,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBhB,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBS,OAAO,EAAE;UACPL,KAAK,EAAE,CAAC;UACRb,MAAM,EAAE,CAAC;UACT0B,MAAM,EAAE;QACV,CAAC;QACDrE,MAAM,EAAE;UACNmC,MAAM,EAAE,MAAM;UACdmC,eAAe,EAAE;QACnB,CAAC;QACDC,UAAU,EAAE;UACVC,UAAU,EAAE,CAAC;UACbC,QAAQ,EAAE;QACZ,CAAC;QACDpB,OAAO,EAAE,EAAE;QACXqD,WAAW,EAAE;UACXC,gBAAgB,EAAE;QACpB,CAAC;QACDC,WAAW,EAAE;UACXC,mBAAmB,EAAE;QACvB;MACF,CAAC;MACDnC,OAAO,EAAE;QACPC,CAAC,EAAE;UACD1B,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;YACxB,OAAOD,cAAc,CAACC,GAAG,EAAE;cAAEI,QAAQ,EAAE;YAAK,CAAC,CAAC;UAChD;QACF;MACF,CAAC;MACD8F,IAAI,EAAE;QACJ/B,IAAI,EAAE,KAAK;QACXgC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACD3B,MAAM,EAAE;QACNT,IAAI,EAAE;MACR;IACF,CAAC;;IAED;IACA,MAAMiE,mBAAmB,GAAG;MAC1BpG,MAAM,EAAE,CACN;QACEI,IAAI,EAAE,QAAQ;QACd4B,IAAI,EAAE,MAAM;QACZ7G,IAAI,EAAEuF;MACR,CAAC,EACD;QACEN,IAAI,EAAE,gBAAgB;QACtB4B,IAAI,EAAE,QAAQ;QACd7G,IAAI,EAAEwF;MACR,CAAC,EACD;QACEP,IAAI,EAAE,kBAAkB;QACxB4B,IAAI,EAAE,QAAQ;QACd7G,IAAI,EAAEyF;MACR,CAAC,CACF;MACDmB,KAAK,EAAE;QACLE,MAAM,EAAE,GAAG;QACXD,IAAI,EAAE,MAAM;QACZ2D,OAAO,EAAE,IAAI;QACbzD,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACb+D,eAAe,EAAE,CAAC,CAAC,CAAC;QACpB9D,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE8G,IAAI,EAAE;UAC9B,IAAIA,IAAI,CAACG,WAAW,KAAK,CAAC,EAAE;YAC1B,IAAIjH,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,EAAE;YAC9D,OAAO,GAAG,GAAGA,GAAG,GAAG,GAAG;UACxB;UACA,OAAO,EAAE;QACX,CAAC;QACDwE,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBhB,MAAM,EAAE,CAAC,SAAS,CAAC;UACnBiB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZP,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACD4C,UAAU,EAAE;UACV5C,OAAO,EAAE;QACX;MACF,CAAC;MACDM,MAAM,EAAE;QACNE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBD,KAAK,EAAE;MACT,CAAC;MACDwC,WAAW,EAAE;QACXO,GAAG,EAAE;UACHC,WAAW,EAAE,KAAK;UAClB5D,MAAM,EAAE,IAAI;UACZI,UAAU,EAAE;YACVyD,KAAK,EAAE;cACLxD,OAAO,EAAE,IAAI;cACbK,OAAO,EAAE,CAAC,EAAE;cACZH,KAAK,EAAE;gBACLC,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBqD,KAAK,EAAE;cACT,CAAC;cACDxD,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;gBACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EACjD,OAAO,IAAI;gBACb,IAAIA,GAAG,IAAI,CAAC,EAAE;kBACZ,OAAO,GAAG,GAAGA,GAAG,GAAG,GAAG;gBACxB,CAAC,MAAM;kBACL,OAAO,GAAG,GAAIA,GAAG,GAAG,IAAK,GAAG,GAAG;gBACjC;cACF;YACF;UACF;QACF;MACF,CAAC;MACDsI,IAAI,EAAE;QACJ1B,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACnB,CAAC;MACDtF,MAAM,EAAE8B,eAAe;MACvB+B,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACfX,QAAQ,EAAE,MAAM;QAChBY,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdiD,WAAW,EAAE,CAAC;QACdhD,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDL,KAAK,EAAE;QACLzD,MAAM,EAAE;UACNkD,KAAK,EAAE;YACLf,MAAM,EAAE,MAAM;YACdgB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDO,UAAU,EAAE;UACVb,IAAI,EAAE;QACR,CAAC;QACDc,SAAS,EAAE;UACTd,IAAI,EAAE;QACR;MACF,CAAC;MACDe,KAAK,EAAE;QACLf,IAAI,EAAE;MACR,CAAC;MACDV,MAAM,EAAEA,MAAM,CAACI,YAAY;MAC3B2B,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBC,eAAe,EAAE,QAAQ;QACzBjB,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE,KAAK;QACjBS,OAAO,EAAE;UACPL,KAAK,EAAE,CAAC;UACRb,MAAM,EAAE,CAAC;UACT0B,MAAM,EAAE;QACV,CAAC;QACDrE,MAAM,EAAE;UACNmC,MAAM,EAAE,MAAM;UACdmC,eAAe,EAAE;QACnB,CAAC;QACDC,UAAU,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,QAAQ,EAAE;QACZ,CAAC;QACDpB,OAAO,EAAE,EAAE;QACXqD,WAAW,EAAE;UACXC,gBAAgB,EAAE;QACpB,CAAC;QACDC,WAAW,EAAE;UACXC,mBAAmB,EAAE;QACvB;MACF,CAAC;MACDnC,OAAO,EAAE;QACPwC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,KAAK;QAChBxC,CAAC,EAAE,CACD;UACE1B,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;YAChE,OAAO,GAAG,GAAGA,GAAG,GAAG,UAAU;UAC/B;QACF,CAAC,EACD;UACEuE,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;YAChE,OAAO,GAAG,GAAGA,GAAG,GAAG,UAAU;UAC/B;QACF,CAAC,EACD;UACEuE,SAAS,EAAE,SAAAA,CAAUvE,GAAG,EAAE;YACxB,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,IAAIC,KAAK,CAACH,GAAG,CAAC,EAAE,OAAO,IAAI;YAChE,OAAO,GAAG,GAAGA,GAAG,GAAG,UAAU;UAC/B;QACF,CAAC;MAEL,CAAC;MACDkG,IAAI,EAAE;QACJ/B,IAAI,EAAE,KAAK;QACXgC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF;IACF,CAAC;;IAED;IACA,MAAMmC,mBAAmB,GAAGA,CAACC,GAAG,EAAE1I,OAAO,EAAE2I,SAAS,KAAK;MACvD,IAAID,GAAG,CAACE,OAAO,EAAE;QACf;QACAF,GAAG,CAACE,OAAO,CAACC,SAAS,GAAG,EAAE;;QAE1B;QACAC,UAAU,CAAC,MAAM;UACf,IAAIJ,GAAG,CAACE,OAAO,EAAE;YACf,IAAI;cACFzK,OAAO,CAACC,GAAG,CAAC,8BAA8BuK,SAAS,QAAQ,CAAC;cAC5D,MAAM7E,KAAK,GAAG,IAAI5H,UAAU,CAACwM,GAAG,CAACE,OAAO,EAAE5I,OAAO,CAAC;cAClD8D,KAAK,CAACiF,MAAM,CAAC,CAAC;YAChB,CAAC,CAAC,OAAOC,KAAK,EAAE;cACd7K,OAAO,CAAC6K,KAAK,CACX,oCAAoCL,SAAS,SAAS,EACtDK,KACF,CAAC;YACH;UACF;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC;;IAED;IACA,IAAI,CAACvI,OAAO,CAAC9B,MAAM,GAAG,CAAC,IAAI+B,OAAO,CAAC/B,MAAM,GAAG,CAAC,KAAKhB,kBAAkB,CAAC,WAAW,CAAC,EAAE;MACjF8K,mBAAmB,CAAC5L,SAAS,EAAEgH,aAAa,EAAE,SAAS,CAAC;IAC1D;;IAEA;IACA;IACA,IAAId,OAAO,CAACpE,MAAM,GAAG,CAAC,EAAE;MACtB8J,mBAAmB,CAAC3L,cAAc,EAAE8J,kBAAkB,EAAE,cAAc,CAAC;IACzE;;IAEA;IACA;IACA,IAAI3D,qBAAqB,CAACtE,MAAM,GAAG,CAAC,EAAE;MACpC8J,mBAAmB,CACjB1L,kBAAkB,EAClB0K,sBAAsB,EACtB,kBACF,CAAC;IACH;;IAEA;IACA;IACA,IAAIhF,MAAM,CAAC9D,MAAM,GAAG,CAAC,EAAE;MACrB8J,mBAAmB,CACjBzL,eAAe,EACfmL,mBAAmB,EACnB,kBACF,CAAC;IACH;EACF,CAAC;;EAED;EACA,MAAMc,gBAAgB,GAAGA,CAAA,kBACvB7M,OAAA;IAAK8M,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/B/M,OAAA;MAAK8M,SAAS,EAAC,2DAA2D;MAAAC,QAAA,eACxE/M,OAAA;QAAK8M,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpD/M,OAAA;UAAK8M,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAE1B/M,OAAA;YAAK8M,SAAS,EAAC;UAAkF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxGnN,OAAA;YAAK8M,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNnN,OAAA;YAAK8M,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,IAAI,CAACzL,YAAY,CAAC,CAAC,EAAE;IACnB,oBAAO1B,OAAA,CAAC6M,gBAAgB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;;EAEA;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAM;MAAE/I,OAAO;MAAEC;IAAQ,CAAC,GAAGF,iBAAiB,CAAC,CAAC;IAChD,MAAM;MAAEtD,IAAI,EAAE6F;IAAQ,CAAC,GAAG3B,sBAAsB,CAAC,CAAC;IAClD,MAAM;MAAEW,MAAM,EAAEkB;IAAsB,CAAC,GAAGpB,0BAA0B,CAAC,CAAC;IACtE,MAAM;MAAEY;IAAO,CAAC,GAAGD,uBAAuB,CAAC,CAAC;IAE5C,OACE/B,OAAO,CAAC9B,MAAM,GAAG,CAAC,IAClB+B,OAAO,CAAC/B,MAAM,GAAG,CAAC,IAClBoE,OAAO,CAACpE,MAAM,GAAG,CAAC,IAClBsE,qBAAqB,CAACtE,MAAM,GAAG,CAAC,IAChC8D,MAAM,CAAC9D,MAAM,GAAG,CAAC;EAErB,CAAC;EAED,IAAI,CAAC6K,gBAAgB,CAAC,CAAC,EAAE;IACvB,oBACEpN,OAAA;MAAK8M,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B/M,OAAA;QAAK8M,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxE/M,OAAA;UAAK8M,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD/M,OAAA;YAAK8M,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B/M,OAAA;cAAK8M,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNnN,OAAA;cAAK8M,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNnN,OAAA;cAAK8M,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,kBAC1B,EAAC9K,MAAM,CAACC,IAAI,CAAC5B,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC+M,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnN,OAAA;IAAK8M,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/B/M,OAAA;MAAK8M,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAExE/M,OAAA;QAAK8M,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvG/M,OAAA;UACE8M,SAAS,EAAC,sCAAsC;UAChD3E,KAAK,EAAEjI,eAAgB;UAAA6M,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnN,OAAA;UAAG8M,SAAS,EAAC,2BAA2B;UAAC3E,KAAK,EAAE/H,mBAAoB;UAAA2M,QAAA,EAAC;QAErE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGL5L,kBAAkB,CAAC,WAAW,CAAC,iBAC9BvB,OAAA;QAAK8M,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD/M,OAAA;UACE8M,SAAS,EAAC,2CAA2C;UACrD3E,KAAK,EAAE/H,mBAAoB;UAAA2M,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnN,OAAA;UAAKsM,GAAG,EAAE7L;QAAU;UAAAuM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAG3BnN,OAAA;UAAK8M,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB/M,OAAA;YAAK8M,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB/M,OAAA;cACE8M,SAAS,EAAC,qCAAqC;cAC/C3E,KAAK,EAAE;gBAAE,GAAG/H,mBAAmB;gBAAEiI,UAAU,EAAE;cAAU,CAAE;cAAA0E,QAAA,EAC1D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNnN,OAAA;cACE8M,SAAS,EAAC,+BAA+B;cACzC3E,KAAK,EAAE9H,gBAAiB;cAAA0M,QAAA,EACzB;YAKD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENnN,OAAA;YAAK8M,SAAS,EAAC,OAAO;YAAAC,QAAA,gBACpB/M,OAAA;cACE8M,SAAS,EAAC,qCAAqC;cAC/C3E,KAAK,EAAE;gBAAE,GAAG/H,mBAAmB;gBAAEiI,UAAU,EAAE;cAAU,CAAE;cAAA0E,QAAA,EAC1D;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNnN,OAAA;cACE8M,SAAS,EAAC,+BAA+B;cACzC3E,KAAK,EAAE9H,gBAAiB;cAAA0M,QAAA,EACzB;YAKD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDnN,OAAA;QAAK8M,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD/M,OAAA;UACE8M,SAAS,EAAC,4CAA4C;UACtD3E,KAAK,EAAE/H,mBAAoB;UAAA2M,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnN,OAAA;UAAKsM,GAAG,EAAE5L,cAAe;UAACoM,SAAS,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,eAENnN,OAAA;MAAK8M,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAGxE/M,OAAA;QAAK8M,SAAS,EAAC,gGAAgG;QAAAC,QAAA,gBAC7G/M,OAAA;UACE8M,SAAS,EAAC,sCAAsC;UAChD3E,KAAK,EAAEjI,eAAgB;UAAA6M,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLnN,OAAA;UAAG8M,SAAS,EAAC,2BAA2B;UAAC3E,KAAK,EAAE/H,mBAAoB;UAAA2M,QAAA,EAAC;QAErE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNnN,OAAA;QAAK8M,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAC5E/M,OAAA;UACE8M,SAAS,EAAC,2CAA2C;UACrD3E,KAAK,EAAE/H,mBAAoB;UAAA2M,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnN,OAAA;UAAKsM,GAAG,EAAE3L;QAAmB;UAAAqM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAGNnN,OAAA;QAAK8M,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD/M,OAAA;UACE8M,SAAS,EAAC,2CAA2C;UACrD3E,KAAK,EAAE/H,mBAAoB;UAAA2M,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNnN,OAAA;UAAKsM,GAAG,EAAE1L,eAAgB;UAACkM,SAAS,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3M,EAAA,CA9hCIP,uBAAuB;AAAAqN,EAAA,GAAvBrN,uBAAuB;AAgiC7B,eAAeA,uBAAuB;AAAC,IAAAqN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}