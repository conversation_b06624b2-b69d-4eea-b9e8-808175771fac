{"ast": null, "code": "\"use strict\";\n\nexports.isLeapYear = isLeapYear;\nvar _index = require(\"./toDate.js\");\n\n/**\n * @name isLeapYear\n * @category Year Helpers\n * @summary Is the given date in the leap year?\n *\n * @description\n * Is the given date in the leap year?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in the leap year\n *\n * @example\n * // Is 1 September 2012 in the leap year?\n * const result = isLeapYear(new Date(2012, 8, 1))\n * //=> true\n */\nfunction isLeapYear(date) {\n  const _date = (0, _index.toDate)(date);\n  const year = _date.getFullYear();\n  return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n}", "map": {"version": 3, "names": ["exports", "isLeapYear", "_index", "require", "date", "_date", "toDate", "year", "getFullYear"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/isLeapYear.js"], "sourcesContent": ["\"use strict\";\nexports.isLeapYear = isLeapYear;\nvar _index = require(\"./toDate.js\");\n\n/**\n * @name isLeapYear\n * @category Year Helpers\n * @summary Is the given date in the leap year?\n *\n * @description\n * Is the given date in the leap year?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in the leap year\n *\n * @example\n * // Is 1 September 2012 in the leap year?\n * const result = isLeapYear(new Date(2012, 8, 1))\n * //=> true\n */\nfunction isLeapYear(date) {\n  const _date = (0, _index.toDate)(date);\n  const year = _date.getFullYear();\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n"], "mappings": "AAAA,YAAY;;AACZA,OAAO,CAACC,UAAU,GAAGA,UAAU;AAC/B,IAAIC,MAAM,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,UAAUA,CAACG,IAAI,EAAE;EACxB,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAEH,MAAM,CAACI,MAAM,EAAEF,IAAI,CAAC;EACtC,MAAMG,IAAI,GAAGF,KAAK,CAACG,WAAW,CAAC,CAAC;EAChC,OAAOD,IAAI,GAAG,GAAG,KAAK,CAAC,IAAKA,IAAI,GAAG,CAAC,KAAK,CAAC,IAAIA,IAAI,GAAG,GAAG,KAAK,CAAE;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}