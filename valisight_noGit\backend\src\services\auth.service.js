import bcrypt from 'bcryptjs';
import {
  generateAuthTokens,
  generateResetToken,
} from '../middleware/auth.middleware.js';
import { prisma } from '../db/prisma.js';
import jwt from 'jsonwebtoken';
import { sendEmail, sendEmailWithTemplate } from '../utils/sendEmail.js';
import { populateEmailTemplate } from '../utils/populateEmailTemplate.js';

export const login = async (email, password) => {
  try {
    // fetch user
    const user = await prisma.user.findFirst({
      where: {
        email,
      },
      select: {
        id: true,
        username: true,
        isPasswordReset: true,
        email: true,
        createdAt: true,
        updatedAt: true,
        password: true,
      },
    });

    if (!user) {
      return {
        success: false,
        statusCode: 401,
        message: 'User does not exist ',
      };
    }

    // validate user password
    const isValidPassword = await bcrypt.compare(password, user.password);

    if (!isValidPassword) {
      return {
        success: false,
        statusCode: 404,
        message: 'Password is incorrect.',
      };
    }


    // Generate a JWT token
    const token = await generateAuthTokens(user.id);

    return {
      success: true,
      message: 'User is login Successfully',
      user,
      token,
    };
  } catch (error) {
    throw {
      status: error.status,
      success: false,
      statusCode: error.statusCode,
      message: error.message || 'Internal Server Error',
    };
  }
};

export const register = async (username, password, email,isAdmin) => {
  
  try {
    // hash the password
    const hashedPassword = await bcrypt.hash(password, parseInt(10));
    const newUser = await prisma.user.create({
      data: {
        email,
        username: username?.trim(),
        password: hashedPassword,
        isAdmin
      },
      select: {
        id: true,
        username: true,
        isAdmin:true,
        createdAt: true,
      },
    });

    return {
      success: true,
      statusCode: 201,
      user: newUser,
      message: 'User is created successfully',
    };
  } catch (error) {
    console.log({ error });
    if (error.code === 'P2002') {
      throw {
        success: false,
        statusCode: 400,
        status: 200,
        message: 'User is already exist',
      };
    }
  }
};

export const forgetPassword = async (email) => {
  try {
    // check user
    const existingUser = await prisma.user.findFirst({
      where: {
        email,
      },
    });

    if (!existingUser) {
      return {
        success: true,
        statusCode: 404,
        message: 'User does not exist.',
      };
    }

    // generate reset token
    const resetToken = await generateResetToken(existingUser.id);
    let resetLink = `${process.env.REACT_APP_FRONTEND_URL}/reset-password?token=${encodeURIComponent(resetToken)}`;
    console.log({ resetLink });

    // update user
    await prisma.user.update({
      where: {
        id: existingUser.id,
      },
      data: {
        resetToken: resetToken,
        resetTokenExpiry: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes expiry,
      },
    });

    // send email to user
    let replaceValues = [
      {
        original: 'fullName',
        newVal: existingUser.username,
      },
      {
        original: 'resetLink',
        newVal: resetLink,
      },
    ];
    let body = await populateEmailTemplate('resetPassword.html', replaceValues);
    let emailResponse = await sendEmailWithTemplate(email, 'Forgot Password', body)
    console.log({ emailResponse });

    return {
      success: true,
      statusCode: 200,
      message: 'Password forget successfully',
    };
  } catch (error) {
    console.log({ error });

    console.log(error.message);
  }
};

export const sendResetPasswordEmail = async (email) => {
  try {
    // check user
    const existingUser = await prisma.user.findFirst({ where: { email }});

    if (!existingUser) {
      return {
        success: true,
        statusCode: 404,
        message: 'User does not exist.',
      };
    }

    // generate reset token
    const resetToken = await generateResetToken(existingUser.id);
    let resetLink = `${process.env.REACT_APP_FRONTEND_URL}/reset-password?token=${encodeURIComponent(resetToken)}`;
    console.log({ resetLink });

    // update user
    await prisma.user.update({
      where: {
        id: existingUser.id,
      },
      data: {
        resetToken: resetToken,
        resetTokenExpiry: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes expiry,
      },
    });

    // send email to user
    let replaceValues = [
      {
        original: 'fullName',
        newVal: existingUser.username,
      },
      {
        original: 'resetLink',
        newVal: resetLink,
      },
    ];
    let body = await populateEmailTemplate('resetPassword.html', replaceValues);
    let emailResponse = await sendEmailWithTemplate(email, 'Reset Password', body)
    console.log({ emailResponse });

    return {
      success: true,
      statusCode: 200,
      message: 'Password reset successfully',
    };
  } catch (error) {
    console.log({ error });

    console.log(error.message);
  }
}

export const resetPassword = async (newPassword, token, isPasswordReset) => {
  try {
    const decoded = jwt.verify(token, process.env.SECRET_KEY);
    const user = await prisma.user.findUnique({ where: { id: decoded.id } });

    if (!user) {
      throw {
        statusCode: 400,
        message: 'User not found',
      };
    }

    if (user.resetToken !== token) {
      throw {
        status: 200,
        statusCode: 400,
        message: 'Toke does not match',
      };
    }

    console.log("user", user);

    if (new Date() > user.resetTokenExpiry) {
      throw {
        statusCode: 400,
        message: 'Token has expired',
      };
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await prisma.user.update({
      where: { id: user.id },
      data: {
        isPasswordReset,
        password: hashedPassword,
        resetToken: null,
        resetTokenExpiry: null,
      },
    });
    return {
      statusCode: 201,
      success: true,
      message: 'Password reset successful',
    };
  } catch (error) {
    return {
      success: false,
      statusCode: 400,
      status: 200,
      message: error,
    };
  }
};
