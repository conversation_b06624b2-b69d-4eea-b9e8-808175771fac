FROM node:20

# Set working directory inside the container
WORKDIR /app

# Install required dependencies for bcrypt
# RUN apk add --no-cache python3 make g++

# Copy only package.json and package-lock.json to leverage Docker layer caching
COPY package*.json ./

# Install dependencies and rebuild bcrypt
RUN npm ci

# Copy the rest of the application code
COPY . .

# Optional: Add build step here if necessary
RUN npm run build

# Generate Prisma Client
RUN npx prisma generate

# Expose the port your app listens on
EXPOSE 8000

# Use the start script from package.json to start the app
CMD ["npm", "start"]
