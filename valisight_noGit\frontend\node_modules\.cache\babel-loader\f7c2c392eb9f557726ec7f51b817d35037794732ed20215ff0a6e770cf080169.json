{"ast": null, "code": "export { default } from \"./style.js\";\nexport * from \"./style.js\";", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/@mui/material/node_modules/@mui/system/esm/style/index.js"], "sourcesContent": ["export { default } from \"./style.js\";\nexport * from \"./style.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,cAAc,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}