import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
import { getFaqsId } from "../helpers/faqsHelper.js";
const baseUrl = `/api/v1/faqs`;


describe('Update Faqs', () => {
    let faqId;

    before(async () => {
       faqId = await getFaqsId()
       faqId = faqId[0].id
    });

    it('should update a FAQ successfully', async () => {
        const updatedData = { question: 'What is Docker?', answer: 'Docker is a containerization platform.' };

        const res = await request(app)
            .put(`${baseUrl}/${faqId}`)
            .send(updatedData)
            .expect(200);        

        expect(res._body.success).to.be.true;
        expect(res._body.message).to.equal('FAQ updated successfully');
        expect(res._body.faq.answer).to.equal(updatedData.answer);
    });

    it('should return an error if FAQ not found', async () => {
        const res = await request(app)
            .put(`${baseUrl}/9999`)
            .send({ question: 'Invalid', answer: 'Does not exist' })        

        expect(res._body.success).to.be.false;
        expect(res._body.statusCode).to.equal(500);

    });
});
