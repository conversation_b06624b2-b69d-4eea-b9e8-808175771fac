{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\reports\\\\ReportPages\\\\OperationalEfficiency.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport ApexCharts from 'apexcharts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OperationalEfficiencyDashboard = ({\n  headerTextStyle = {},\n  headingTextStyle = {},\n  subHeadingTextStyle = {},\n  contentTextStyle = {},\n  operationalData = null\n}) => {\n  _s();\n  console.log('OperationalEfficiency - operationalData:', operationalData);\n  const salesOutstandingRef = useRef(null);\n  const payablesOutstandingRef = useRef(null);\n  const inventoryOutstandingRef = useRef(null);\n  const cashConversionRef = useRef(null);\n  const fixedAssetTurnoverRef = useRef(null);\n\n  // Enhanced data validation function\n  const isDataLoaded = () => {\n    var _operationalData$days, _operationalData$days2, _operationalData$days3, _operationalData$cash, _operationalData$fixe;\n    if (!operationalData) {\n      console.log('OperationalEfficiency - No operationalData provided');\n      return false;\n    }\n    console.log('OperationalEfficiency - operationalData keys:', Object.keys(operationalData));\n    console.log('OperationalEfficiency - Full operationalData:', operationalData);\n\n    // Check if data might be nested under a different structure\n    const possibleDataPaths = [operationalData, operationalData.operationalEfficiency, operationalData.operational, operationalData.data, operationalData.reportData];\n    console.log('OperationalEfficiency - Checking possible data paths:', possibleDataPaths.map(path => path ? Object.keys(path) : 'null'));\n\n    // Check if at least some required data exists - using actual property names from your data\n    const hasSalesOutstandingData = operationalData.daysSalesAROutstanding && Array.isArray(operationalData.daysSalesAROutstanding) && operationalData.daysSalesAROutstanding.length > 0;\n    const hasPayablesOutstandingData = operationalData.daysSalesAPOutstanding && Array.isArray(operationalData.daysSalesAPOutstanding) && operationalData.daysSalesAPOutstanding.length > 0;\n    const hasInventoryOutstandingData = operationalData.daysInventoryOutstanding && Array.isArray(operationalData.daysInventoryOutstanding) && operationalData.daysInventoryOutstanding.length > 0;\n    const hasCashConversionData = operationalData.cashConversionCycle && Array.isArray(operationalData.cashConversionCycle) && operationalData.cashConversionCycle.length > 0;\n    const hasFixedAssetTurnoverData = operationalData.fixedAssetTurnover && Array.isArray(operationalData.fixedAssetTurnover) && operationalData.fixedAssetTurnover.length > 0;\n    console.log('OperationalEfficiency - Data validation:', {\n      hasSalesOutstandingData,\n      hasPayablesOutstandingData,\n      hasInventoryOutstandingData,\n      hasCashConversionData,\n      hasFixedAssetTurnoverData,\n      salesOutstandingLength: ((_operationalData$days = operationalData.daysSalesAROutstanding) === null || _operationalData$days === void 0 ? void 0 : _operationalData$days.length) || 0,\n      payablesOutstandingLength: ((_operationalData$days2 = operationalData.daysSalesAPOutstanding) === null || _operationalData$days2 === void 0 ? void 0 : _operationalData$days2.length) || 0,\n      inventoryOutstandingLength: ((_operationalData$days3 = operationalData.daysInventoryOutstanding) === null || _operationalData$days3 === void 0 ? void 0 : _operationalData$days3.length) || 0,\n      cashConversionLength: ((_operationalData$cash = operationalData.cashConversionCycle) === null || _operationalData$cash === void 0 ? void 0 : _operationalData$cash.length) || 0,\n      fixedAssetTurnoverLength: ((_operationalData$fixe = operationalData.fixedAssetTurnover) === null || _operationalData$fixe === void 0 ? void 0 : _operationalData$fixe.length) || 0\n    });\n\n    // Log sample data for debugging\n    if (operationalData.daysSalesAROutstanding && operationalData.daysSalesAROutstanding.length > 0) {\n      console.log('OperationalEfficiency - Sample sales outstanding data:', operationalData.daysSalesAROutstanding[0]);\n    }\n    if (operationalData.daysSalesAPOutstanding && operationalData.daysSalesAPOutstanding.length > 0) {\n      console.log('OperationalEfficiency - Sample payables outstanding data:', operationalData.daysSalesAPOutstanding[0]);\n    }\n\n    // Return true if we have at least some data to work with\n    return hasSalesOutstandingData || hasPayablesOutstandingData || hasInventoryOutstandingData || hasCashConversionData || hasFixedAssetTurnoverData;\n  };\n  useEffect(() => {\n    if (isDataLoaded()) {\n      initializeCharts();\n    }\n  }, [operationalData]);\n  const formatMonthYear = (year, month) => {\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\n  };\n  const initializeCharts = () => {\n    var _dataToUse$daysSalesA, _dataToUse$daysSalesA2, _dataToUse$daysSalesA3, _dataToUse$daysInvent, _dataToUse$cashConver, _dataToUse$fixedAsset;\n    if (!operationalData) return;\n    console.log('OperationalEfficiency - Initializing charts with data:', operationalData);\n\n    // Try to find the correct data structure\n    let dataToUse = operationalData;\n\n    // Check if data is nested under different possible paths\n    if (operationalData.operationalEfficiency) {\n      dataToUse = operationalData.operationalEfficiency;\n      console.log('OperationalEfficiency - Using nested operationalEfficiency data');\n    } else if (operationalData.operational) {\n      dataToUse = operationalData.operational;\n      console.log('OperationalEfficiency - Using nested operational data');\n    } else if (operationalData.data) {\n      dataToUse = operationalData.data;\n      console.log('OperationalEfficiency - Using nested data');\n    } else if (operationalData.reportData) {\n      dataToUse = operationalData.reportData;\n      console.log('OperationalEfficiency - Using nested reportData');\n    }\n    console.log('OperationalEfficiency - Final data structure to use:', dataToUse);\n\n    // Prepare data from API response - using correct property names from your JSON\n    const categories = ((_dataToUse$daysSalesA = dataToUse.daysSalesAROutstanding) === null || _dataToUse$daysSalesA === void 0 ? void 0 : _dataToUse$daysSalesA.map(item => formatMonthYear(item.year, item.month))) || [];\n    const daysSalesOutstandingData = ((_dataToUse$daysSalesA2 = dataToUse.daysSalesAROutstanding) === null || _dataToUse$daysSalesA2 === void 0 ? void 0 : _dataToUse$daysSalesA2.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    console.log('OperationalEfficiency - Categories:', daysSalesOutstandingData);\n    const daysPayablesOutstandingData = ((_dataToUse$daysSalesA3 = dataToUse.daysSalesAPOutstanding) === null || _dataToUse$daysSalesA3 === void 0 ? void 0 : _dataToUse$daysSalesA3.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    const daysInventoryOutstandingData = ((_dataToUse$daysInvent = dataToUse.daysInventoryOutstanding) === null || _dataToUse$daysInvent === void 0 ? void 0 : _dataToUse$daysInvent.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    const cashConversionCycleData = ((_dataToUse$cashConver = dataToUse.cashConversionCycle) === null || _dataToUse$cashConver === void 0 ? void 0 : _dataToUse$cashConver.map(item => {\n      const value = parseFloat(item.CCC || item.ccc);\n      return isNaN(value) || value === null ? 0 : value;\n    })) || [];\n    const fixedAssetTurnoverData = ((_dataToUse$fixedAsset = dataToUse.fixedAssetTurnover) === null || _dataToUse$fixedAsset === void 0 ? void 0 : _dataToUse$fixedAsset.map(item => parseFloat(item.fat) || 0)) || [];\n    console.log('OperationalEfficiency - Processed data:', {\n      categoriesLength: categories.length,\n      salesOutstandingLength: daysSalesOutstandingData.length,\n      salesOutstandingData: daysSalesOutstandingData,\n      payablesOutstandingLength: daysPayablesOutstandingData.length,\n      payablesOutstandingData: daysPayablesOutstandingData,\n      inventoryOutstandingLength: daysInventoryOutstandingData.length,\n      inventoryOutstandingData: daysInventoryOutstandingData,\n      cashConversionLength: cashConversionCycleData.length,\n      cashConversionData: cashConversionCycleData,\n      fixedAssetTurnoverLength: fixedAssetTurnoverData.length,\n      fixedAssetTurnoverData: fixedAssetTurnoverData\n    });\n\n    // Color scheme\n    const colors = {\n      salesOutstanding: '#2d6a9b',\n      payablesOutstanding: '#565aa4',\n      inventoryOutstanding: '#2a689a',\n      cashConversion: '#ff6b47',\n      fixedAssetTurnover: '#2d6a9b'\n    };\n    console.log('OperationalEfficiency - Days Sales Outstanding Data:', daysSalesOutstandingData);\n    // 1. Days Sales (A/R) Outstanding Chart\n    const salesOutstandingOptions = {\n      series: [{\n        name: 'Days Sales Outstanding',\n        data: daysSalesOutstandingData\n      }],\n      chart: {\n        type: 'area',\n        height: 200,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent'\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return Math.round(val);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: [colors.salesOutstanding]\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: colors.salesOutstanding,\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: colors.salesOutstanding,\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: [colors.salesOutstanding],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.salesOutstanding],\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return Math.round(val) + ' days';\n          }\n        }\n      }\n    };\n\n    // 2. Days Payables (AP) Outstanding Chart\n    const payablesOutstandingOptions = {\n      series: [{\n        name: 'Days Payables Outstanding',\n        data: daysPayablesOutstandingData\n      }],\n      chart: {\n        type: 'area',\n        height: 200,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent',\n        parentHeightOffset: 0,\n        sparkline: {\n          enabled: false\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return Math.round(val);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: [colors.payablesOutstanding]\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: colors.payablesOutstanding,\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: colors.payablesOutstanding,\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: [colors.payablesOutstanding],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.payablesOutstanding],\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return Math.round(val) + ' days';\n          }\n        }\n      }\n    };\n\n    // 3. Days Inventory Outstanding Chart (Bar Chart)\n    const inventoryOutstandingOptions = {\n      series: [{\n        name: 'Days Inventory Outstanding',\n        data: daysInventoryOutstandingData\n      }],\n      chart: {\n        type: 'bar',\n        height: 350,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent'\n      },\n      plotOptions: {\n        bar: {\n          columnWidth: '40%',\n          dataLabels: {\n            position: 'top'\n          }\n        }\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return Math.round(val);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -20,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.inventoryOutstanding],\n      grid: {\n        show: false,\n        padding: {\n          left: 10,\n          right: 10,\n          top: 25,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return Math.round(val) + ' days';\n          }\n        }\n      }\n    };\n\n    // 4. Cash Conversion Cycle Chart\n    const cashConversionOptions = {\n      series: [{\n        name: 'Cash Conversion Cycle',\n        data: cashConversionCycleData\n      }],\n      chart: {\n        type: 'line',\n        height: 200,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent'\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return Math.round(val);\n        },\n        style: {\n          fontSize: '14px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        width: 2,\n        colors: [colors.cashConversion]\n      },\n      markers: {\n        size: 4,\n        colors: [colors.cashConversion],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '14px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.cashConversion],\n      grid: {\n        show: false,\n        padding: {\n          left: 25,\n          right: 25,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return Math.round(val) + ' days';\n          }\n        }\n      }\n    };\n\n    // 5. Fixed Asset Turnover Chart\n    const fixedAssetTurnoverOptions = {\n      series: [{\n        name: 'Fixed Asset Turnover',\n        data: fixedAssetTurnoverData\n      }],\n      chart: {\n        type: 'area',\n        height: 200,\n        toolbar: {\n          show: false\n        },\n        background: 'transparent'\n      },\n      dataLabels: {\n        enabled: true,\n        formatter: function (val) {\n          return val.toFixed(2);\n        },\n        style: {\n          fontSize: '12px',\n          colors: ['#333'],\n          fontWeight: '500'\n        },\n        offsetY: -10,\n        background: {\n          enabled: false\n        },\n        dropShadow: {\n          enabled: false\n        }\n      },\n      stroke: {\n        curve: 'smooth',\n        width: 2,\n        colors: [colors.fixedAssetTurnover]\n      },\n      fill: {\n        type: 'gradient',\n        gradient: {\n          shadeIntensity: 1,\n          type: 'vertical',\n          colorStops: [{\n            offset: 0,\n            color: colors.fixedAssetTurnover,\n            opacity: 0.4\n          }, {\n            offset: 100,\n            color: colors.fixedAssetTurnover,\n            opacity: 0.1\n          }]\n        }\n      },\n      markers: {\n        size: 4,\n        colors: [colors.fixedAssetTurnover],\n        strokeColors: '#fff',\n        strokeWidth: 2,\n        hover: {\n          size: 6\n        }\n      },\n      xaxis: {\n        categories: categories,\n        labels: {\n          style: {\n            colors: '#666',\n            fontSize: '11px'\n          }\n        },\n        axisBorder: {\n          show: false\n        },\n        axisTicks: {\n          show: false\n        }\n      },\n      yaxis: {\n        show: false\n      },\n      colors: [colors.fixedAssetTurnover],\n      grid: {\n        show: false,\n        padding: {\n          left: 15,\n          right: 15,\n          top: 20,\n          bottom: 0\n        }\n      },\n      legend: {\n        show: false\n      },\n      tooltip: {\n        y: {\n          formatter: function (val) {\n            return val.toFixed(2);\n          }\n        }\n      }\n    };\n\n    // Clear existing charts before rendering new ones\n    const clearAndRenderChart = (ref, options, chartName) => {\n      if (ref.current) {\n        // Clear any existing chart\n        ref.current.innerHTML = '';\n\n        // Wait a tick before rendering to ensure DOM is cleared\n        setTimeout(() => {\n          if (ref.current) {\n            try {\n              console.log(`OperationalEfficiency - Rendering ${chartName} chart`);\n              const chart = new ApexCharts(ref.current, options);\n              chart.render();\n            } catch (error) {\n              console.error(`OperationalEfficiency - Error rendering ${chartName} chart:`, error);\n            }\n          }\n        }, 10);\n      }\n    };\n\n    // Only render charts if we have data for them and the data has meaningful values\n    console.log('OperationalEfficiency - Chart rendering check:', {\n      salesDataLength: daysSalesOutstandingData.length,\n      salesDataHasValues: daysSalesOutstandingData.some(val => val > 0),\n      salesData: daysSalesOutstandingData,\n      payablesDataLength: daysPayablesOutstandingData.length,\n      payablesDataHasValues: daysPayablesOutstandingData.some(val => val > 0),\n      payablesData: daysPayablesOutstandingData\n    });\n    if (daysSalesOutstandingData.length > 0 && daysSalesOutstandingData.some(val => val > 0)) {\n      clearAndRenderChart(salesOutstandingRef, salesOutstandingOptions, 'Sales Outstanding');\n    } else if (salesOutstandingRef.current) {\n      salesOutstandingRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful sales outstanding data available</div>';\n    }\n    if (daysPayablesOutstandingData.length > 0 && daysPayablesOutstandingData.some(val => val > 0)) {\n      clearAndRenderChart(payablesOutstandingRef, payablesOutstandingOptions, 'Payables Outstanding');\n    } else if (payablesOutstandingRef.current) {\n      payablesOutstandingRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful payables outstanding data available</div>';\n    }\n    if (daysInventoryOutstandingData.length > 0 && daysInventoryOutstandingData.some(val => val > 0)) {\n      clearAndRenderChart(inventoryOutstandingRef, inventoryOutstandingOptions, 'Inventory Outstanding');\n    } else if (inventoryOutstandingRef.current) {\n      inventoryOutstandingRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful inventory outstanding data available</div>';\n    }\n\n    // Only render cash conversion cycle chart if there's meaningful data\n    if (cashConversionCycleData.length > 0 && cashConversionCycleData.some(val => val != null && val > 0)) {\n      clearAndRenderChart(cashConversionRef, cashConversionOptions, 'Cash Conversion');\n    } else if (cashConversionRef.current) {\n      // Show \"No data available\" message\n      cashConversionRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No cash conversion cycle data available</div>';\n    }\n    if (fixedAssetTurnoverData.length > 0 && fixedAssetTurnoverData.some(val => val > 0)) {\n      clearAndRenderChart(fixedAssetTurnoverRef, fixedAssetTurnoverOptions, 'Fixed Asset Turnover');\n    } else if (fixedAssetTurnoverRef.current) {\n      fixedAssetTurnoverRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful fixed asset turnover data available</div>';\n    }\n  };\n\n  // Enhanced loading component\n  const LoadingComponent = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col shadow gap-1 p-10 mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-64\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xl text-gray-600 mb-2\",\n            children: \"Loading operational efficiency data...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Please wait while we fetch your operational data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 678,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 677,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 676,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 675,\n    columnNumber: 5\n  }, this);\n\n  // Show loading if data is not properly loaded\n  if (!isDataLoaded()) {\n    return /*#__PURE__*/_jsxDEV(LoadingComponent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Add a fallback if operationalData exists but has no usable data\n  const hasAnyUsableData = () => {\n    var _operationalData$days4, _operationalData$days5, _operationalData$days6, _operationalData$days7, _operationalData$fixe2;\n    const categories = ((_operationalData$days4 = operationalData.daysSalesAROutstanding) === null || _operationalData$days4 === void 0 ? void 0 : _operationalData$days4.map(item => formatMonthYear(item.year, item.month))) || [];\n    const daysSalesOutstandingData = ((_operationalData$days5 = operationalData.daysSalesAROutstanding) === null || _operationalData$days5 === void 0 ? void 0 : _operationalData$days5.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    const daysPayablesOutstandingData = ((_operationalData$days6 = operationalData.daysSalesAPOutstanding) === null || _operationalData$days6 === void 0 ? void 0 : _operationalData$days6.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    const daysInventoryOutstandingData = ((_operationalData$days7 = operationalData.daysInventoryOutstanding) === null || _operationalData$days7 === void 0 ? void 0 : _operationalData$days7.map(item => {\n      // Try multiple possible property names for robustness\n      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;\n      return parseFloat(value) || 0;\n    })) || [];\n    const fixedAssetTurnoverData = ((_operationalData$fixe2 = operationalData.fixedAssetTurnover) === null || _operationalData$fixe2 === void 0 ? void 0 : _operationalData$fixe2.map(item => parseFloat(item.fat) || 0)) || [];\n\n    // Check if we have meaningful data (not just zeros)\n    const hasMeaningfulSalesData = daysSalesOutstandingData.some(val => val > 0);\n    const hasMeaningfulPayablesData = daysPayablesOutstandingData.some(val => val > 0);\n    const hasMeaningfulInventoryData = daysInventoryOutstandingData.some(val => val > 0);\n    const hasMeaningfulFixedAssetData = fixedAssetTurnoverData.some(val => val > 0);\n    return categories.length > 0 && (hasMeaningfulSalesData || hasMeaningfulPayablesData || hasMeaningfulInventoryData || hasMeaningfulFixedAssetData);\n  };\n  if (!hasAnyUsableData()) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen p-5\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xl text-gray-600 mb-2\",\n              children: \"No operational efficiency data available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: \"The data structure is available but charts cannot be rendered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400 mt-2\",\n              children: [\"Available data: \", Object.keys(operationalData || {}).join(', ')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 741,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 738,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen p-5\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col gap-14 p-10 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Operational Efficiency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: \"January 2025 | Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 771,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 764,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Days Sales (A/R) Outstanding\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: salesOutstandingRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Days Payables (AP) Outstanding\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: payablesOutstandingRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 795,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-teal-600 text-2xl mb-6\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter'\n            },\n            children: \"Days AR Outstanding & Days AP Outstanding\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 797,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: contentTextStyle,\n            children: \"Average number of days it takes customers to pay for invoices/ average number of days it takes a company to pay its suppliers.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 mb-24 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Days Inventory Outstanding\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: inventoryOutstandingRef,\n          className: \"mb-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 810,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 761,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-6xl mx-auto bg-white flex flex-col gap-10 p-10 mt-4 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"component-header flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-bold text-gray-800 m-0\",\n          style: headerTextStyle,\n          children: \"Operational Efficiency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 827,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 m-0\",\n          style: subHeadingTextStyle,\n          children: \"January 2025 | Acme Print\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibent text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Cash Conversion Cycle\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: cashConversionRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-teal-600 text-2xl\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter'\n            },\n            children: \"Cash Conversion Cycle (CCC)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: contentTextStyle,\n            children: \"The time it takes a company to convert the money spent on inventory or production back into cash by selling its goods or services. A shorter CCC is better because it means less time that money is tied up in inventory or accounts receivable.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 border-b-4 border-blue-900\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-semibold text-teal-600 mb-5\",\n          style: subHeadingTextStyle,\n          children: \"Fixed Asset Turnover\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: fixedAssetTurnoverRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-teal-600 text-2xl\",\n            style: {\n              ...subHeadingTextStyle,\n              fontWeight: 'lighter'\n            },\n            children: \"Fixed Asset Turnover (FAT)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: contentTextStyle,\n            children: \"The ratio of a company's net sales to its average fixed assets over a specific period, usually a year. A higher ratio indicates that a company is using its fixed assets more efficiently, while a lower ratio suggests underutilization.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 863,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 824,\n      columnNumber: 10\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 759,\n    columnNumber: 5\n  }, this);\n};\n_s(OperationalEfficiencyDashboard, \"O0Le7Wl/rSJonuZpsA+RcAzhNOs=\");\n_c = OperationalEfficiencyDashboard;\nexport default OperationalEfficiencyDashboard;\nvar _c;\n$RefreshReg$(_c, \"OperationalEfficiencyDashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Apex<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "OperationalEfficiencyDashboard", "headerTextStyle", "headingTextStyle", "subHeadingTextStyle", "contentTextStyle", "operationalData", "_s", "console", "log", "salesOutstandingRef", "payablesOutstandingRef", "inventoryOutstandingRef", "cashConversionRef", "fixedAssetTurnoverRef", "isDataLoaded", "_operationalData$days", "_operationalData$days2", "_operationalData$days3", "_operationalData$cash", "_operationalData$fixe", "Object", "keys", "possibleDataPaths", "operationalEfficiency", "operational", "data", "reportData", "map", "path", "hasSalesOutstandingData", "daysSalesAROutstanding", "Array", "isArray", "length", "hasPayablesOutstandingData", "daysSalesAPOutstanding", "hasInventoryOutstandingData", "daysInventoryOutstanding", "hasCashConversionData", "cashConversionCycle", "hasFixedAssetTurnoverData", "fixedAssetTurnover", "salesOutstandingLength", "payablesOutstandingLength", "inventoryOutstandingLength", "cashConversionLength", "fixedAssetTurnoverLength", "initializeCharts", "formatMonthYear", "year", "month", "monthNames", "String", "slice", "_dataToUse$daysSalesA", "_dataToUse$daysSalesA2", "_dataToUse$daysSalesA3", "_dataToUse$daysInvent", "_dataToUse$cashConver", "_dataToUse$fixedAsset", "dataToUse", "categories", "item", "daysSalesOutstandingData", "value", "dso", "days_sales_outstanding", "daysSalesOutstanding", "parseFloat", "daysPayablesOutstandingData", "dpo", "days_payables_outstanding", "daysPayablesOutstanding", "daysInventoryOutstandingData", "dio", "days_inventory_outstanding", "cashConversionCycleData", "CCC", "ccc", "isNaN", "fixedAssetTurnoverData", "fat", "categoriesLength", "salesOutstandingData", "payablesOutstandingData", "inventoryOutstandingData", "cashConversionData", "colors", "salesOutstanding", "payablesOutstanding", "inventoryOutstanding", "cashConversion", "salesOutstandingOptions", "series", "name", "chart", "type", "height", "toolbar", "show", "background", "dataLabels", "enabled", "formatter", "val", "Math", "round", "style", "fontSize", "fontWeight", "offsetY", "dropShadow", "stroke", "curve", "width", "fill", "gradient", "shadeIntensity", "colorStops", "offset", "color", "opacity", "markers", "size", "strokeColors", "strokeWidth", "hover", "xaxis", "labels", "axisBorder", "axisTicks", "yaxis", "grid", "padding", "left", "right", "top", "bottom", "legend", "tooltip", "y", "payablesOutstandingOptions", "parentHeightOffset", "sparkline", "inventoryOutstandingOptions", "plotOptions", "bar", "columnWidth", "position", "cashConversionOptions", "fixedAssetTurnoverOptions", "toFixed", "clearAndRender<PERSON>hart", "ref", "options", "chartName", "current", "innerHTML", "setTimeout", "render", "error", "salesDataLength", "salesDataHasValues", "some", "salesData", "payablesDataLength", "payablesDataHasValues", "payablesData", "LoadingComponent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "hasAnyUsableData", "_operationalData$days4", "_operationalData$days5", "_operationalData$days6", "_operationalData$days7", "_operationalData$fixe2", "hasMeaningfulSalesData", "hasMeaningfulPayablesData", "hasMeaningfulInventoryData", "hasMeaningfulFixedAssetData", "join", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/reports/ReportPages/OperationalEfficiency.jsx"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport ApexCharts from 'apexcharts';\r\n\r\nconst OperationalEfficiencyDashboard = ({\r\n  headerTextStyle = {},\r\n  headingTextStyle = {},\r\n  subHeadingTextStyle = {},\r\n  contentTextStyle = {},\r\n  operationalData = null\r\n}) => {\r\n  console.log('OperationalEfficiency - operationalData:', operationalData);\r\n  const salesOutstandingRef = useRef(null);\r\n  const payablesOutstandingRef = useRef(null);\r\n  const inventoryOutstandingRef = useRef(null);\r\n  const cashConversionRef = useRef(null);\r\n  const fixedAssetTurnoverRef = useRef(null);\r\n\r\n  // Enhanced data validation function\r\n  const isDataLoaded = () => {\r\n    if (!operationalData) {\r\n      console.log('OperationalEfficiency - No operationalData provided');\r\n      return false;\r\n    }\r\n    \r\n    console.log('OperationalEfficiency - operationalData keys:', Object.keys(operationalData));\r\n    console.log('OperationalEfficiency - Full operationalData:', operationalData);\r\n    \r\n    // Check if data might be nested under a different structure\r\n    const possibleDataPaths = [\r\n      operationalData,\r\n      operationalData.operationalEfficiency,\r\n      operationalData.operational,\r\n      operationalData.data,\r\n      operationalData.reportData\r\n    ];\r\n    \r\n    console.log('OperationalEfficiency - Checking possible data paths:', possibleDataPaths.map(path => path ? Object.keys(path) : 'null'));\r\n    \r\n    // Check if at least some required data exists - using actual property names from your data\r\n    const hasSalesOutstandingData = operationalData.daysSalesAROutstanding && \r\n                                   Array.isArray(operationalData.daysSalesAROutstanding) && \r\n                                   operationalData.daysSalesAROutstanding.length > 0;\r\n    \r\n    const hasPayablesOutstandingData = operationalData.daysSalesAPOutstanding && \r\n                                      Array.isArray(operationalData.daysSalesAPOutstanding) && \r\n                                      operationalData.daysSalesAPOutstanding.length > 0;\r\n    \r\n    const hasInventoryOutstandingData = operationalData.daysInventoryOutstanding && \r\n                                       Array.isArray(operationalData.daysInventoryOutstanding) && \r\n                                       operationalData.daysInventoryOutstanding.length > 0;\r\n    \r\n    const hasCashConversionData = operationalData.cashConversionCycle && \r\n                                 Array.isArray(operationalData.cashConversionCycle) && \r\n                                 operationalData.cashConversionCycle.length > 0;\r\n    \r\n    const hasFixedAssetTurnoverData = operationalData.fixedAssetTurnover && \r\n                                     Array.isArray(operationalData.fixedAssetTurnover) && \r\n                                     operationalData.fixedAssetTurnover.length > 0;\r\n\r\n    console.log('OperationalEfficiency - Data validation:', {\r\n      hasSalesOutstandingData,\r\n      hasPayablesOutstandingData,\r\n      hasInventoryOutstandingData,\r\n      hasCashConversionData,\r\n      hasFixedAssetTurnoverData,\r\n      salesOutstandingLength: operationalData.daysSalesAROutstanding?.length || 0,\r\n      payablesOutstandingLength: operationalData.daysSalesAPOutstanding?.length || 0,\r\n      inventoryOutstandingLength: operationalData.daysInventoryOutstanding?.length || 0,\r\n      cashConversionLength: operationalData.cashConversionCycle?.length || 0,\r\n      fixedAssetTurnoverLength: operationalData.fixedAssetTurnover?.length || 0\r\n    });\r\n\r\n    // Log sample data for debugging\r\n    if (operationalData.daysSalesAROutstanding && operationalData.daysSalesAROutstanding.length > 0) {\r\n      console.log('OperationalEfficiency - Sample sales outstanding data:', operationalData.daysSalesAROutstanding[0]);\r\n    }\r\n    if (operationalData.daysSalesAPOutstanding && operationalData.daysSalesAPOutstanding.length > 0) {\r\n      console.log('OperationalEfficiency - Sample payables outstanding data:', operationalData.daysSalesAPOutstanding[0]);\r\n    }\r\n\r\n    // Return true if we have at least some data to work with\r\n    return hasSalesOutstandingData || hasPayablesOutstandingData || hasInventoryOutstandingData || \r\n           hasCashConversionData || hasFixedAssetTurnoverData;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isDataLoaded()) {\r\n      initializeCharts();\r\n    }\r\n  }, [operationalData]);\r\n\r\n  const formatMonthYear = (year, month) => {\r\n    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\r\n                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\r\n    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;\r\n  };\r\n\r\n  const initializeCharts = () => {\r\n    if (!operationalData) return;\r\n\r\n    console.log('OperationalEfficiency - Initializing charts with data:', operationalData);\r\n    \r\n    // Try to find the correct data structure\r\n    let dataToUse = operationalData;\r\n    \r\n    // Check if data is nested under different possible paths\r\n    if (operationalData.operationalEfficiency) {\r\n      dataToUse = operationalData.operationalEfficiency;\r\n      console.log('OperationalEfficiency - Using nested operationalEfficiency data');\r\n    } else if (operationalData.operational) {\r\n      dataToUse = operationalData.operational;\r\n      console.log('OperationalEfficiency - Using nested operational data');\r\n    } else if (operationalData.data) {\r\n      dataToUse = operationalData.data;\r\n      console.log('OperationalEfficiency - Using nested data');\r\n    } else if (operationalData.reportData) {\r\n      dataToUse = operationalData.reportData;\r\n      console.log('OperationalEfficiency - Using nested reportData');\r\n    }\r\n    \r\n    console.log('OperationalEfficiency - Final data structure to use:', dataToUse);\r\n\r\n    // Prepare data from API response - using correct property names from your JSON\r\n    const categories = dataToUse.daysSalesAROutstanding?.map(item => \r\n      formatMonthYear(item.year, item.month)\r\n    ) || [];\r\n\r\n    const daysSalesOutstandingData = dataToUse.daysSalesAROutstanding?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n    console.log('OperationalEfficiency - Categories:', daysSalesOutstandingData);\r\n\r\n    const daysPayablesOutstandingData = dataToUse.daysSalesAPOutstanding?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    const daysInventoryOutstandingData = dataToUse.daysInventoryOutstanding?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    const cashConversionCycleData = dataToUse.cashConversionCycle?.map(item => {\r\n      const value = parseFloat(item.CCC || item.ccc);\r\n      return isNaN(value) || value === null ? 0 : value;\r\n    }) || [];\r\n\r\n    const fixedAssetTurnoverData = dataToUse.fixedAssetTurnover?.map(item => \r\n      parseFloat(item.fat) || 0\r\n    ) || [];\r\n\r\n    console.log('OperationalEfficiency - Processed data:', {\r\n      categoriesLength: categories.length,\r\n      salesOutstandingLength: daysSalesOutstandingData.length,\r\n      salesOutstandingData: daysSalesOutstandingData,\r\n      payablesOutstandingLength: daysPayablesOutstandingData.length,\r\n      payablesOutstandingData: daysPayablesOutstandingData,\r\n      inventoryOutstandingLength: daysInventoryOutstandingData.length,\r\n      inventoryOutstandingData: daysInventoryOutstandingData,\r\n      cashConversionLength: cashConversionCycleData.length,\r\n      cashConversionData: cashConversionCycleData,\r\n      fixedAssetTurnoverLength: fixedAssetTurnoverData.length,\r\n      fixedAssetTurnoverData: fixedAssetTurnoverData\r\n    });\r\n\r\n    // Color scheme\r\n    const colors = {\r\n      salesOutstanding: '#2d6a9b',\r\n      payablesOutstanding: '#565aa4',\r\n      inventoryOutstanding: '#2a689a',\r\n      cashConversion: '#ff6b47',\r\n      fixedAssetTurnover: '#2d6a9b'\r\n    };\r\nconsole.log('OperationalEfficiency - Days Sales Outstanding Data:', daysSalesOutstandingData);\r\n    // 1. Days Sales (A/R) Outstanding Chart\r\n    const salesOutstandingOptions = {\r\n      series: [{\r\n        name: 'Days Sales Outstanding',\r\n        data: daysSalesOutstandingData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 200,\r\n        toolbar: { show: false },\r\n        background: 'transparent'\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return Math.round(val);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: [colors.salesOutstanding]\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: colors.salesOutstanding, opacity: 0.4 },\r\n            { offset: 100, color: colors.salesOutstanding, opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: [colors.salesOutstanding],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: { \r\n            colors: '#666', \r\n            fontSize: '14px' \r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.salesOutstanding],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: { \r\n        show: false \r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function(val) {\r\n            return Math.round(val) + ' days';\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // 2. Days Payables (AP) Outstanding Chart\r\n    const payablesOutstandingOptions = {\r\n      series: [{\r\n        name: 'Days Payables Outstanding',\r\n        data: daysPayablesOutstandingData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 200,\r\n        toolbar: { show: false },\r\n        background: 'transparent',\r\n        parentHeightOffset: 0,\r\n        sparkline: {\r\n          enabled: false\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return Math.round(val);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: [colors.payablesOutstanding]\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: colors.payablesOutstanding, opacity: 0.4 },\r\n            { offset: 100, color: colors.payablesOutstanding, opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: [colors.payablesOutstanding],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: { \r\n            colors: '#666', \r\n            fontSize: '14px' \r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.payablesOutstanding],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: { \r\n        show: false \r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function(val) {\r\n            return Math.round(val) + ' days';\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // 3. Days Inventory Outstanding Chart (Bar Chart)\r\n    const inventoryOutstandingOptions = {\r\n      series: [{\r\n        name: 'Days Inventory Outstanding',\r\n        data: daysInventoryOutstandingData\r\n      }],\r\n      chart: {\r\n        type: 'bar',\r\n        height: 350,\r\n        toolbar: { show: false },\r\n        background: 'transparent'\r\n      },\r\n      plotOptions: {\r\n        bar: {\r\n          columnWidth: '40%',\r\n          dataLabels: {\r\n            position: 'top'\r\n          }\r\n        }\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return Math.round(val);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -20,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: { \r\n            colors: '#666', \r\n            fontSize: '14px' \r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.inventoryOutstanding],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 10,\r\n          right: 10,\r\n          top: 25,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: { \r\n        show: false \r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function(val) {\r\n            return Math.round(val) + ' days';\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // 4. Cash Conversion Cycle Chart\r\n    const cashConversionOptions = {\r\n      series: [{\r\n        name: 'Cash Conversion Cycle',\r\n        data: cashConversionCycleData\r\n      }],\r\n      chart: {\r\n        type: 'line',\r\n        height: 200,\r\n        toolbar: { show: false },\r\n        background: 'transparent'\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return Math.round(val);\r\n        },\r\n        style: {\r\n          fontSize: '14px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        width: 2,\r\n        colors: [colors.cashConversion]\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: [colors.cashConversion],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: { \r\n            colors: '#666', \r\n            fontSize: '14px' \r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.cashConversion],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 25,\r\n          right: 25,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: { \r\n        show: false \r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function(val) {\r\n            return Math.round(val) + ' days';\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // 5. Fixed Asset Turnover Chart\r\n    const fixedAssetTurnoverOptions = {\r\n      series: [{\r\n        name: 'Fixed Asset Turnover',\r\n        data: fixedAssetTurnoverData\r\n      }],\r\n      chart: {\r\n        type: 'area',\r\n        height: 200,\r\n        toolbar: { show: false },\r\n        background: 'transparent'\r\n      },\r\n      dataLabels: {\r\n        enabled: true,\r\n        formatter: function (val) {\r\n          return val.toFixed(2);\r\n        },\r\n        style: {\r\n          fontSize: '12px',\r\n          colors: ['#333'],\r\n          fontWeight: '500'\r\n        },\r\n        offsetY: -10,\r\n        background: {\r\n          enabled: false\r\n        },\r\n        dropShadow: {\r\n          enabled: false\r\n        }\r\n      },\r\n      stroke: {\r\n        curve: 'smooth',\r\n        width: 2,\r\n        colors: [colors.fixedAssetTurnover]\r\n      },\r\n      fill: {\r\n        type: 'gradient',\r\n        gradient: {\r\n          shadeIntensity: 1,\r\n          type: 'vertical',\r\n          colorStops: [\r\n            { offset: 0, color: colors.fixedAssetTurnover, opacity: 0.4 },\r\n            { offset: 100, color: colors.fixedAssetTurnover, opacity: 0.1 }\r\n          ]\r\n        }\r\n      },\r\n      markers: {\r\n        size: 4,\r\n        colors: [colors.fixedAssetTurnover],\r\n        strokeColors: '#fff',\r\n        strokeWidth: 2,\r\n        hover: {\r\n          size: 6\r\n        }\r\n      },\r\n      xaxis: {\r\n        categories: categories,\r\n        labels: {\r\n          style: { \r\n            colors: '#666', \r\n            fontSize: '11px' \r\n          }\r\n        },\r\n        axisBorder: { show: false },\r\n        axisTicks: { show: false }\r\n      },\r\n      yaxis: {\r\n        show: false\r\n      },\r\n      colors: [colors.fixedAssetTurnover],\r\n      grid: {\r\n        show: false,\r\n        padding: {\r\n          left: 15,\r\n          right: 15,\r\n          top: 20,\r\n          bottom: 0\r\n        }\r\n      },\r\n      legend: { \r\n        show: false \r\n      },\r\n      tooltip: {\r\n        y: {\r\n          formatter: function(val) {\r\n            return val.toFixed(2);\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    // Clear existing charts before rendering new ones\r\n    const clearAndRenderChart = (ref, options, chartName) => {\r\n      if (ref.current) {\r\n        // Clear any existing chart\r\n        ref.current.innerHTML = '';\r\n        \r\n        // Wait a tick before rendering to ensure DOM is cleared\r\n        setTimeout(() => {\r\n          if (ref.current) {\r\n            try {\r\n              console.log(`OperationalEfficiency - Rendering ${chartName} chart`);\r\n              const chart = new ApexCharts(ref.current, options);\r\n              chart.render();\r\n            } catch (error) {\r\n              console.error(`OperationalEfficiency - Error rendering ${chartName} chart:`, error);\r\n            }\r\n          }\r\n        }, 10);\r\n      }\r\n    };\r\n\r\n    // Only render charts if we have data for them and the data has meaningful values\r\n    console.log('OperationalEfficiency - Chart rendering check:', {\r\n      salesDataLength: daysSalesOutstandingData.length,\r\n      salesDataHasValues: daysSalesOutstandingData.some(val => val > 0),\r\n      salesData: daysSalesOutstandingData,\r\n      payablesDataLength: daysPayablesOutstandingData.length,\r\n      payablesDataHasValues: daysPayablesOutstandingData.some(val => val > 0),\r\n      payablesData: daysPayablesOutstandingData\r\n    });\r\n\r\n    if (daysSalesOutstandingData.length > 0 && daysSalesOutstandingData.some(val => val > 0)) {\r\n      clearAndRenderChart(salesOutstandingRef, salesOutstandingOptions, 'Sales Outstanding');\r\n    } else if (salesOutstandingRef.current) {\r\n      salesOutstandingRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful sales outstanding data available</div>';\r\n    }\r\n    \r\n    if (daysPayablesOutstandingData.length > 0 && daysPayablesOutstandingData.some(val => val > 0)) {\r\n      clearAndRenderChart(payablesOutstandingRef, payablesOutstandingOptions, 'Payables Outstanding');\r\n    } else if (payablesOutstandingRef.current) {\r\n      payablesOutstandingRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful payables outstanding data available</div>';\r\n    }\r\n    \r\n    if (daysInventoryOutstandingData.length > 0 && daysInventoryOutstandingData.some(val => val > 0)) {\r\n      clearAndRenderChart(inventoryOutstandingRef, inventoryOutstandingOptions, 'Inventory Outstanding');\r\n    } else if (inventoryOutstandingRef.current) {\r\n      inventoryOutstandingRef.current.innerHTML = '<div class=\"flex items-center justify-center h-64 text-gray-500\">No meaningful inventory outstanding data available</div>';\r\n    }\r\n    \r\n    // Only render cash conversion cycle chart if there's meaningful data\r\n    if (cashConversionCycleData.length > 0 && cashConversionCycleData.some(val => val != null && val > 0)) {\r\n      clearAndRenderChart(cashConversionRef, cashConversionOptions, 'Cash Conversion');\r\n    } else if (cashConversionRef.current) {\r\n      // Show \"No data available\" message\r\n      cashConversionRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No cash conversion cycle data available</div>';\r\n    }\r\n    \r\n    if (fixedAssetTurnoverData.length > 0 && fixedAssetTurnoverData.some(val => val > 0)) {\r\n      clearAndRenderChart(fixedAssetTurnoverRef, fixedAssetTurnoverOptions, 'Fixed Asset Turnover');\r\n    } else if (fixedAssetTurnoverRef.current) {\r\n      fixedAssetTurnoverRef.current.innerHTML = '<div class=\"flex items-center justify-center h-48 text-gray-500\">No meaningful fixed asset turnover data available</div>';\r\n    }\r\n  };\r\n\r\n  // Enhanced loading component\r\n  const LoadingComponent = () => (\r\n    <div className=\"min-h-screen p-5\">\r\n      <div className=\"max-w-6xl mx-auto bg-white flex flex-col shadow gap-1 p-10 mb-8\">\r\n        <div className=\"flex items-center justify-center h-64\">\r\n          <div className=\"text-center\">\r\n            {/* Loading spinner */}\r\n            <div className=\"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mb-4\"></div>\r\n            <div className=\"text-xl text-gray-600 mb-2\">\r\n              Loading operational efficiency data...\r\n            </div>\r\n            <div className=\"text-sm text-gray-500\">\r\n              Please wait while we fetch your operational data\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Show loading if data is not properly loaded\r\n  if (!isDataLoaded()) {\r\n    return <LoadingComponent />;\r\n  }\r\n\r\n  // Add a fallback if operationalData exists but has no usable data\r\n  const hasAnyUsableData = () => {\r\n    const categories = operationalData.daysSalesAROutstanding?.map(item => \r\n      formatMonthYear(item.year, item.month)\r\n    ) || [];\r\n\r\n    const daysSalesOutstandingData = operationalData.daysSalesAROutstanding?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    const daysPayablesOutstandingData = operationalData.daysSalesAPOutstanding?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    const daysInventoryOutstandingData = operationalData.daysInventoryOutstanding?.map(item => {\r\n      // Try multiple possible property names for robustness\r\n      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;\r\n      return parseFloat(value) || 0;\r\n    }) || [];\r\n\r\n    const fixedAssetTurnoverData = operationalData.fixedAssetTurnover?.map(item => \r\n      parseFloat(item.fat) || 0\r\n    ) || [];\r\n\r\n    // Check if we have meaningful data (not just zeros)\r\n    const hasMeaningfulSalesData = daysSalesOutstandingData.some(val => val > 0);\r\n    const hasMeaningfulPayablesData = daysPayablesOutstandingData.some(val => val > 0);\r\n    const hasMeaningfulInventoryData = daysInventoryOutstandingData.some(val => val > 0);\r\n    const hasMeaningfulFixedAssetData = fixedAssetTurnoverData.some(val => val > 0);\r\n\r\n    return (categories.length > 0 && (hasMeaningfulSalesData || hasMeaningfulPayablesData || \r\n            hasMeaningfulInventoryData || hasMeaningfulFixedAssetData));\r\n  };\r\n\r\n  if (!hasAnyUsableData()) {\r\n    return (\r\n      <div className=\"min-h-screen p-5\">\r\n        <div className=\"max-w-6xl mx-auto bg-white flex flex-col shadow p-10 mb-8\">\r\n          <div className=\"flex items-center justify-center h-64\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-xl text-gray-600 mb-2\">\r\n                No operational efficiency data available\r\n              </div>\r\n              <div className=\"text-sm text-gray-500\">\r\n                The data structure is available but charts cannot be rendered\r\n              </div>\r\n              <div className=\"text-xs text-gray-400 mt-2\">\r\n                Available data: {Object.keys(operationalData || {}).join(', ')}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-5\">\r\n      {/* Single Main Container - Same structure as ExpenseSummary */}\r\n      <div className=\"max-w-6xl mx-auto bg-white flex flex-col gap-14 p-10 mb-8\">\r\n\r\n        {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n           Operational Efficiency\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            January 2025 | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Days Sales (A/R) Outstanding */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Days Sales (A/R) Outstanding\r\n          </div>\r\n          <div ref={salesOutstandingRef}></div>\r\n        </div>\r\n\r\n        {/* Days Payables (AP) Outstanding */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Days Payables (AP) Outstanding\r\n          </div>\r\n          <div ref={payablesOutstandingRef}></div>\r\n          <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n            <div\r\n                className=\"text-teal-600 text-2xl mb-6\"\r\n                style={{...subHeadingTextStyle, fontWeight: 'lighter'}}\r\n            >\r\n              Days AR Outstanding & Days AP Outstanding\r\n            </div>\r\n            <div style={contentTextStyle}>\r\n              Average number of days it takes customers to pay for invoices/ average number of days it takes a company to pay its suppliers.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Days Inventory Outstanding */}\r\n        <div className=\"bg-white p-6 border-b-4 mb-24 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Days Inventory Outstanding\r\n          </div>\r\n          <div ref={inventoryOutstandingRef} className=\"mb-5\"></div>\r\n        </div>\r\n\r\n\r\n         </div>\r\n\r\n\r\n         <div className=\"max-w-6xl mx-auto bg-white flex flex-col gap-10 p-10 mt-4 mb-8\">\r\n         {/* Header Section */}\r\n        <div className=\"component-header flex items-center justify-between gap-4 border-b-4 border-blue-900 pb-2\">\r\n          <h1\r\n            className=\"text-4xl font-bold text-gray-800 m-0\"\r\n            style={headerTextStyle}\r\n          >\r\n           Operational Efficiency\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 m-0\" style={subHeadingTextStyle}>\r\n            January 2025 | Acme Print\r\n          </p>\r\n        </div>\r\n\r\n        {/* Cash Conversion Cycle */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibent text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Cash Conversion Cycle\r\n          </div>\r\n          <div ref={cashConversionRef}></div>\r\n          <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n            <div\r\n              className=\"text-teal-600 text-2xl\"\r\n              style={{...subHeadingTextStyle, fontWeight: 'lighter'}}\r\n            >\r\n              Cash Conversion Cycle (CCC)\r\n            </div>\r\n            <div style={contentTextStyle}>\r\n              The time it takes a company to convert the money spent on inventory or production back into cash by selling its goods or services. A shorter CCC is better because it means less time that money is tied up in inventory or accounts receivable.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n       \r\n\r\n        {/* Fixed Asset Turnover */}\r\n        <div className=\"bg-white p-6 border-b-4 border-blue-900\">\r\n          <div\r\n            className=\"text-2xl font-semibold text-teal-600 mb-5\"\r\n            style={subHeadingTextStyle}\r\n          >\r\n            Fixed Asset Turnover\r\n          </div>\r\n          <div ref={fixedAssetTurnoverRef}></div>\r\n          <div className=\"mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5\">\r\n            <div\r\n                className=\"text-teal-600 text-2xl\"\r\n                style={{...subHeadingTextStyle, fontWeight: 'lighter'}}\r\n            >\r\n              Fixed Asset Turnover (FAT)\r\n            </div>\r\n            <div style={contentTextStyle}>\r\n              The ratio of a company's net sales to its average fixed assets over a specific period, usually a year. A higher ratio indicates that a company is using its fixed assets more efficiently, while a lower ratio suggests underutilization.\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OperationalEfficiencyDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,UAAU,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,8BAA8B,GAAGA,CAAC;EACtCC,eAAe,GAAG,CAAC,CAAC;EACpBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,mBAAmB,GAAG,CAAC,CAAC;EACxBC,gBAAgB,GAAG,CAAC,CAAC;EACrBC,eAAe,GAAG;AACpB,CAAC,KAAK;EAAAC,EAAA;EACJC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEH,eAAe,CAAC;EACxE,MAAMI,mBAAmB,GAAGb,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMc,sBAAsB,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC3C,MAAMe,uBAAuB,GAAGf,MAAM,CAAC,IAAI,CAAC;EAC5C,MAAMgB,iBAAiB,GAAGhB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMiB,qBAAqB,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAE1C;EACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACzB,IAAI,CAACd,eAAe,EAAE;MACpBE,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE,OAAO,KAAK;IACd;IAEAD,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEY,MAAM,CAACC,IAAI,CAAChB,eAAe,CAAC,CAAC;IAC1FE,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEH,eAAe,CAAC;;IAE7E;IACA,MAAMiB,iBAAiB,GAAG,CACxBjB,eAAe,EACfA,eAAe,CAACkB,qBAAqB,EACrClB,eAAe,CAACmB,WAAW,EAC3BnB,eAAe,CAACoB,IAAI,EACpBpB,eAAe,CAACqB,UAAU,CAC3B;IAEDnB,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEc,iBAAiB,CAACK,GAAG,CAACC,IAAI,IAAIA,IAAI,GAAGR,MAAM,CAACC,IAAI,CAACO,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;;IAEtI;IACA,MAAMC,uBAAuB,GAAGxB,eAAe,CAACyB,sBAAsB,IACvCC,KAAK,CAACC,OAAO,CAAC3B,eAAe,CAACyB,sBAAsB,CAAC,IACrDzB,eAAe,CAACyB,sBAAsB,CAACG,MAAM,GAAG,CAAC;IAEhF,MAAMC,0BAA0B,GAAG7B,eAAe,CAAC8B,sBAAsB,IACvCJ,KAAK,CAACC,OAAO,CAAC3B,eAAe,CAAC8B,sBAAsB,CAAC,IACrD9B,eAAe,CAAC8B,sBAAsB,CAACF,MAAM,GAAG,CAAC;IAEnF,MAAMG,2BAA2B,GAAG/B,eAAe,CAACgC,wBAAwB,IACzCN,KAAK,CAACC,OAAO,CAAC3B,eAAe,CAACgC,wBAAwB,CAAC,IACvDhC,eAAe,CAACgC,wBAAwB,CAACJ,MAAM,GAAG,CAAC;IAEtF,MAAMK,qBAAqB,GAAGjC,eAAe,CAACkC,mBAAmB,IACpCR,KAAK,CAACC,OAAO,CAAC3B,eAAe,CAACkC,mBAAmB,CAAC,IAClDlC,eAAe,CAACkC,mBAAmB,CAACN,MAAM,GAAG,CAAC;IAE3E,MAAMO,yBAAyB,GAAGnC,eAAe,CAACoC,kBAAkB,IACnCV,KAAK,CAACC,OAAO,CAAC3B,eAAe,CAACoC,kBAAkB,CAAC,IACjDpC,eAAe,CAACoC,kBAAkB,CAACR,MAAM,GAAG,CAAC;IAE9E1B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDqB,uBAAuB;MACvBK,0BAA0B;MAC1BE,2BAA2B;MAC3BE,qBAAqB;MACrBE,yBAAyB;MACzBE,sBAAsB,EAAE,EAAA3B,qBAAA,GAAAV,eAAe,CAACyB,sBAAsB,cAAAf,qBAAA,uBAAtCA,qBAAA,CAAwCkB,MAAM,KAAI,CAAC;MAC3EU,yBAAyB,EAAE,EAAA3B,sBAAA,GAAAX,eAAe,CAAC8B,sBAAsB,cAAAnB,sBAAA,uBAAtCA,sBAAA,CAAwCiB,MAAM,KAAI,CAAC;MAC9EW,0BAA0B,EAAE,EAAA3B,sBAAA,GAAAZ,eAAe,CAACgC,wBAAwB,cAAApB,sBAAA,uBAAxCA,sBAAA,CAA0CgB,MAAM,KAAI,CAAC;MACjFY,oBAAoB,EAAE,EAAA3B,qBAAA,GAAAb,eAAe,CAACkC,mBAAmB,cAAArB,qBAAA,uBAAnCA,qBAAA,CAAqCe,MAAM,KAAI,CAAC;MACtEa,wBAAwB,EAAE,EAAA3B,qBAAA,GAAAd,eAAe,CAACoC,kBAAkB,cAAAtB,qBAAA,uBAAlCA,qBAAA,CAAoCc,MAAM,KAAI;IAC1E,CAAC,CAAC;;IAEF;IACA,IAAI5B,eAAe,CAACyB,sBAAsB,IAAIzB,eAAe,CAACyB,sBAAsB,CAACG,MAAM,GAAG,CAAC,EAAE;MAC/F1B,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEH,eAAe,CAACyB,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAClH;IACA,IAAIzB,eAAe,CAAC8B,sBAAsB,IAAI9B,eAAe,CAAC8B,sBAAsB,CAACF,MAAM,GAAG,CAAC,EAAE;MAC/F1B,OAAO,CAACC,GAAG,CAAC,2DAA2D,EAAEH,eAAe,CAAC8B,sBAAsB,CAAC,CAAC,CAAC,CAAC;IACrH;;IAEA;IACA,OAAON,uBAAuB,IAAIK,0BAA0B,IAAIE,2BAA2B,IACpFE,qBAAqB,IAAIE,yBAAyB;EAC3D,CAAC;EAED7C,SAAS,CAAC,MAAM;IACd,IAAImB,YAAY,CAAC,CAAC,EAAE;MAClBiC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC1C,eAAe,CAAC,CAAC;EAErB,MAAM2C,eAAe,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;IACvC,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACzC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC5D,OAAO,GAAGA,UAAU,CAACD,KAAK,GAAG,CAAC,CAAC,IAAIE,MAAM,CAACH,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;EAC7D,CAAC;EAED,MAAMN,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAO,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAC7B,IAAI,CAACtD,eAAe,EAAE;IAEtBE,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAEH,eAAe,CAAC;;IAEtF;IACA,IAAIuD,SAAS,GAAGvD,eAAe;;IAE/B;IACA,IAAIA,eAAe,CAACkB,qBAAqB,EAAE;MACzCqC,SAAS,GAAGvD,eAAe,CAACkB,qBAAqB;MACjDhB,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;IAChF,CAAC,MAAM,IAAIH,eAAe,CAACmB,WAAW,EAAE;MACtCoC,SAAS,GAAGvD,eAAe,CAACmB,WAAW;MACvCjB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACtE,CAAC,MAAM,IAAIH,eAAe,CAACoB,IAAI,EAAE;MAC/BmC,SAAS,GAAGvD,eAAe,CAACoB,IAAI;MAChClB,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IAC1D,CAAC,MAAM,IAAIH,eAAe,CAACqB,UAAU,EAAE;MACrCkC,SAAS,GAAGvD,eAAe,CAACqB,UAAU;MACtCnB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAChE;IAEAD,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEoD,SAAS,CAAC;;IAE9E;IACA,MAAMC,UAAU,GAAG,EAAAP,qBAAA,GAAAM,SAAS,CAAC9B,sBAAsB,cAAAwB,qBAAA,uBAAhCA,qBAAA,CAAkC3B,GAAG,CAACmC,IAAI,IAC3Dd,eAAe,CAACc,IAAI,CAACb,IAAI,EAAEa,IAAI,CAACZ,KAAK,CACvC,CAAC,KAAI,EAAE;IAEP,MAAMa,wBAAwB,GAAG,EAAAR,sBAAA,GAAAK,SAAS,CAAC9B,sBAAsB,cAAAyB,sBAAA,uBAAhCA,sBAAA,CAAkC5B,GAAG,CAACmC,IAAI,IAAI;MAC7E;MACA,MAAME,KAAK,GAAGF,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACI,sBAAsB,IAAIJ,IAAI,CAACK,oBAAoB,IAAI,CAAC;MACvF,OAAOC,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IACRzD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEuD,wBAAwB,CAAC;IAE5E,MAAMM,2BAA2B,GAAG,EAAAb,sBAAA,GAAAI,SAAS,CAACzB,sBAAsB,cAAAqB,sBAAA,uBAAhCA,sBAAA,CAAkC7B,GAAG,CAACmC,IAAI,IAAI;MAChF;MACA,MAAME,KAAK,GAAGF,IAAI,CAACQ,GAAG,IAAIR,IAAI,CAACS,yBAAyB,IAAIT,IAAI,CAACU,uBAAuB,IAAI,CAAC;MAC7F,OAAOJ,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER,MAAMS,4BAA4B,GAAG,EAAAhB,qBAAA,GAAAG,SAAS,CAACvB,wBAAwB,cAAAoB,qBAAA,uBAAlCA,qBAAA,CAAoC9B,GAAG,CAACmC,IAAI,IAAI;MACnF;MACA,MAAME,KAAK,GAAGF,IAAI,CAACY,GAAG,IAAIZ,IAAI,CAACa,0BAA0B,IAAIb,IAAI,CAACzB,wBAAwB,IAAI,CAAC;MAC/F,OAAO+B,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER,MAAMY,uBAAuB,GAAG,EAAAlB,qBAAA,GAAAE,SAAS,CAACrB,mBAAmB,cAAAmB,qBAAA,uBAA7BA,qBAAA,CAA+B/B,GAAG,CAACmC,IAAI,IAAI;MACzE,MAAME,KAAK,GAAGI,UAAU,CAACN,IAAI,CAACe,GAAG,IAAIf,IAAI,CAACgB,GAAG,CAAC;MAC9C,OAAOC,KAAK,CAACf,KAAK,CAAC,IAAIA,KAAK,KAAK,IAAI,GAAG,CAAC,GAAGA,KAAK;IACnD,CAAC,CAAC,KAAI,EAAE;IAER,MAAMgB,sBAAsB,GAAG,EAAArB,qBAAA,GAAAC,SAAS,CAACnB,kBAAkB,cAAAkB,qBAAA,uBAA5BA,qBAAA,CAA8BhC,GAAG,CAACmC,IAAI,IACnEM,UAAU,CAACN,IAAI,CAACmB,GAAG,CAAC,IAAI,CAC1B,CAAC,KAAI,EAAE;IAEP1E,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrD0E,gBAAgB,EAAErB,UAAU,CAAC5B,MAAM;MACnCS,sBAAsB,EAAEqB,wBAAwB,CAAC9B,MAAM;MACvDkD,oBAAoB,EAAEpB,wBAAwB;MAC9CpB,yBAAyB,EAAE0B,2BAA2B,CAACpC,MAAM;MAC7DmD,uBAAuB,EAAEf,2BAA2B;MACpDzB,0BAA0B,EAAE6B,4BAA4B,CAACxC,MAAM;MAC/DoD,wBAAwB,EAAEZ,4BAA4B;MACtD5B,oBAAoB,EAAE+B,uBAAuB,CAAC3C,MAAM;MACpDqD,kBAAkB,EAAEV,uBAAuB;MAC3C9B,wBAAwB,EAAEkC,sBAAsB,CAAC/C,MAAM;MACvD+C,sBAAsB,EAAEA;IAC1B,CAAC,CAAC;;IAEF;IACA,MAAMO,MAAM,GAAG;MACbC,gBAAgB,EAAE,SAAS;MAC3BC,mBAAmB,EAAE,SAAS;MAC9BC,oBAAoB,EAAE,SAAS;MAC/BC,cAAc,EAAE,SAAS;MACzBlD,kBAAkB,EAAE;IACtB,CAAC;IACLlC,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEuD,wBAAwB,CAAC;IACzF;IACA,MAAM6B,uBAAuB,GAAG;MAC9BC,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,wBAAwB;QAC9BrE,IAAI,EAAEsC;MACR,CAAC,CAAC;MACFgC,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;QACxB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBrB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBsB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZV,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDS,UAAU,EAAE;UACVT,OAAO,EAAE;QACX;MACF,CAAC;MACDU,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACR3B,MAAM,EAAE,CAACA,MAAM,CAACC,gBAAgB;MAClC,CAAC;MACD2B,IAAI,EAAE;QACJnB,IAAI,EAAE,UAAU;QAChBoB,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjBrB,IAAI,EAAE,UAAU;UAChBsB,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAEjC,MAAM,CAACC,gBAAgB;YAAEiC,OAAO,EAAE;UAAI,CAAC,EAC3D;YAAEF,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAEjC,MAAM,CAACC,gBAAgB;YAAEiC,OAAO,EAAE;UAAI,CAAC;QAEjE;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPpC,MAAM,EAAE,CAACA,MAAM,CAACC,gBAAgB,CAAC;QACjCoC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDI,KAAK,EAAE;QACLlE,UAAU,EAAEA,UAAU;QACtBmE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLpB,MAAM,EAAE,MAAM;YACdqB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE9B,IAAI,EAAE;QAAM,CAAC;QAC3B+B,SAAS,EAAE;UAAE/B,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDgC,KAAK,EAAE;QACLhC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAACC,gBAAgB,CAAC;MACjC4C,IAAI,EAAE;QACJjC,IAAI,EAAE,KAAK;QACXkC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNvC,IAAI,EAAE;MACR,CAAC;MACDwC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAASC,GAAG,EAAE;YACvB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC,GAAG,OAAO;UAClC;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAMqC,0BAA0B,GAAG;MACjChD,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,2BAA2B;QACjCrE,IAAI,EAAE4C;MACR,CAAC,CAAC;MACF0B,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE,aAAa;QACzB0C,kBAAkB,EAAE,CAAC;QACrBC,SAAS,EAAE;UACTzC,OAAO,EAAE;QACX;MACF,CAAC;MACDD,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;QACxB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBrB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBsB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZV,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDS,UAAU,EAAE;UACVT,OAAO,EAAE;QACX;MACF,CAAC;MACDU,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACR3B,MAAM,EAAE,CAACA,MAAM,CAACE,mBAAmB;MACrC,CAAC;MACD0B,IAAI,EAAE;QACJnB,IAAI,EAAE,UAAU;QAChBoB,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjBrB,IAAI,EAAE,UAAU;UAChBsB,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAEjC,MAAM,CAACE,mBAAmB;YAAEgC,OAAO,EAAE;UAAI,CAAC,EAC9D;YAAEF,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAEjC,MAAM,CAACE,mBAAmB;YAAEgC,OAAO,EAAE;UAAI,CAAC;QAEpE;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPpC,MAAM,EAAE,CAACA,MAAM,CAACE,mBAAmB,CAAC;QACpCmC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDI,KAAK,EAAE;QACLlE,UAAU,EAAEA,UAAU;QACtBmE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLpB,MAAM,EAAE,MAAM;YACdqB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE9B,IAAI,EAAE;QAAM,CAAC;QAC3B+B,SAAS,EAAE;UAAE/B,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDgC,KAAK,EAAE;QACLhC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAACE,mBAAmB,CAAC;MACpC2C,IAAI,EAAE;QACJjC,IAAI,EAAE,KAAK;QACXkC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNvC,IAAI,EAAE;MACR,CAAC;MACDwC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAASC,GAAG,EAAE;YACvB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC,GAAG,OAAO;UAClC;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAMwC,2BAA2B,GAAG;MAClCnD,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,4BAA4B;QAClCrE,IAAI,EAAEgD;MACR,CAAC,CAAC;MACFsB,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACXC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACD6C,WAAW,EAAE;QACXC,GAAG,EAAE;UACHC,WAAW,EAAE,KAAK;UAClB9C,UAAU,EAAE;YACV+C,QAAQ,EAAE;UACZ;QACF;MACF,CAAC;MACD/C,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;QACxB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBrB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBsB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZV,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDS,UAAU,EAAE;UACVT,OAAO,EAAE;QACX;MACF,CAAC;MACDyB,KAAK,EAAE;QACLlE,UAAU,EAAEA,UAAU;QACtBmE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLpB,MAAM,EAAE,MAAM;YACdqB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE9B,IAAI,EAAE;QAAM,CAAC;QAC3B+B,SAAS,EAAE;UAAE/B,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDgC,KAAK,EAAE;QACLhC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAACG,oBAAoB,CAAC;MACrC0C,IAAI,EAAE;QACJjC,IAAI,EAAE,KAAK;QACXkC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNvC,IAAI,EAAE;MACR,CAAC;MACDwC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAASC,GAAG,EAAE;YACvB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC,GAAG,OAAO;UAClC;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAM6C,qBAAqB,GAAG;MAC5BxD,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,uBAAuB;QAC7BrE,IAAI,EAAEmD;MACR,CAAC,CAAC;MACFmB,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC;QACxB,CAAC;QACDG,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBrB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBsB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZV,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDS,UAAU,EAAE;UACVT,OAAO,EAAE;QACX;MACF,CAAC;MACDU,MAAM,EAAE;QACNE,KAAK,EAAE,CAAC;QACR3B,MAAM,EAAE,CAACA,MAAM,CAACI,cAAc;MAChC,CAAC;MACD+B,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPpC,MAAM,EAAE,CAACA,MAAM,CAACI,cAAc,CAAC;QAC/BiC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDI,KAAK,EAAE;QACLlE,UAAU,EAAEA,UAAU;QACtBmE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLpB,MAAM,EAAE,MAAM;YACdqB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE9B,IAAI,EAAE;QAAM,CAAC;QAC3B+B,SAAS,EAAE;UAAE/B,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDgC,KAAK,EAAE;QACLhC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAACI,cAAc,CAAC;MAC/ByC,IAAI,EAAE;QACJjC,IAAI,EAAE,KAAK;QACXkC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNvC,IAAI,EAAE;MACR,CAAC;MACDwC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAASC,GAAG,EAAE;YACvB,OAAOC,IAAI,CAACC,KAAK,CAACF,GAAG,CAAC,GAAG,OAAO;UAClC;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAM8C,yBAAyB,GAAG;MAChCzD,MAAM,EAAE,CAAC;QACPC,IAAI,EAAE,sBAAsB;QAC5BrE,IAAI,EAAEuD;MACR,CAAC,CAAC;MACFe,KAAK,EAAE;QACLC,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE;UAAEC,IAAI,EAAE;QAAM,CAAC;QACxBC,UAAU,EAAE;MACd,CAAC;MACDC,UAAU,EAAE;QACVC,OAAO,EAAE,IAAI;QACbC,SAAS,EAAE,SAAAA,CAAUC,GAAG,EAAE;UACxB,OAAOA,GAAG,CAAC+C,OAAO,CAAC,CAAC,CAAC;QACvB,CAAC;QACD5C,KAAK,EAAE;UACLC,QAAQ,EAAE,MAAM;UAChBrB,MAAM,EAAE,CAAC,MAAM,CAAC;UAChBsB,UAAU,EAAE;QACd,CAAC;QACDC,OAAO,EAAE,CAAC,EAAE;QACZV,UAAU,EAAE;UACVE,OAAO,EAAE;QACX,CAAC;QACDS,UAAU,EAAE;UACVT,OAAO,EAAE;QACX;MACF,CAAC;MACDU,MAAM,EAAE;QACNC,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,CAAC;QACR3B,MAAM,EAAE,CAACA,MAAM,CAAC9C,kBAAkB;MACpC,CAAC;MACD0E,IAAI,EAAE;QACJnB,IAAI,EAAE,UAAU;QAChBoB,QAAQ,EAAE;UACRC,cAAc,EAAE,CAAC;UACjBrB,IAAI,EAAE,UAAU;UAChBsB,UAAU,EAAE,CACV;YAAEC,MAAM,EAAE,CAAC;YAAEC,KAAK,EAAEjC,MAAM,CAAC9C,kBAAkB;YAAEgF,OAAO,EAAE;UAAI,CAAC,EAC7D;YAAEF,MAAM,EAAE,GAAG;YAAEC,KAAK,EAAEjC,MAAM,CAAC9C,kBAAkB;YAAEgF,OAAO,EAAE;UAAI,CAAC;QAEnE;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,IAAI,EAAE,CAAC;QACPpC,MAAM,EAAE,CAACA,MAAM,CAAC9C,kBAAkB,CAAC;QACnCmF,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,CAAC;QACdC,KAAK,EAAE;UACLH,IAAI,EAAE;QACR;MACF,CAAC;MACDI,KAAK,EAAE;QACLlE,UAAU,EAAEA,UAAU;QACtBmE,MAAM,EAAE;UACNrB,KAAK,EAAE;YACLpB,MAAM,EAAE,MAAM;YACdqB,QAAQ,EAAE;UACZ;QACF,CAAC;QACDqB,UAAU,EAAE;UAAE9B,IAAI,EAAE;QAAM,CAAC;QAC3B+B,SAAS,EAAE;UAAE/B,IAAI,EAAE;QAAM;MAC3B,CAAC;MACDgC,KAAK,EAAE;QACLhC,IAAI,EAAE;MACR,CAAC;MACDZ,MAAM,EAAE,CAACA,MAAM,CAAC9C,kBAAkB,CAAC;MACnC2F,IAAI,EAAE;QACJjC,IAAI,EAAE,KAAK;QACXkC,OAAO,EAAE;UACPC,IAAI,EAAE,EAAE;UACRC,KAAK,EAAE,EAAE;UACTC,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE;QACV;MACF,CAAC;MACDC,MAAM,EAAE;QACNvC,IAAI,EAAE;MACR,CAAC;MACDwC,OAAO,EAAE;QACPC,CAAC,EAAE;UACDrC,SAAS,EAAE,SAAAA,CAASC,GAAG,EAAE;YACvB,OAAOA,GAAG,CAAC+C,OAAO,CAAC,CAAC,CAAC;UACvB;QACF;MACF;IACF,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAGA,CAACC,GAAG,EAAEC,OAAO,EAAEC,SAAS,KAAK;MACvD,IAAIF,GAAG,CAACG,OAAO,EAAE;QACf;QACAH,GAAG,CAACG,OAAO,CAACC,SAAS,GAAG,EAAE;;QAE1B;QACAC,UAAU,CAAC,MAAM;UACf,IAAIL,GAAG,CAACG,OAAO,EAAE;YACf,IAAI;cACFrJ,OAAO,CAACC,GAAG,CAAC,qCAAqCmJ,SAAS,QAAQ,CAAC;cACnE,MAAM5D,KAAK,GAAG,IAAIlG,UAAU,CAAC4J,GAAG,CAACG,OAAO,EAAEF,OAAO,CAAC;cAClD3D,KAAK,CAACgE,MAAM,CAAC,CAAC;YAChB,CAAC,CAAC,OAAOC,KAAK,EAAE;cACdzJ,OAAO,CAACyJ,KAAK,CAAC,2CAA2CL,SAAS,SAAS,EAAEK,KAAK,CAAC;YACrF;UACF;QACF,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC;;IAED;IACAzJ,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;MAC5DyJ,eAAe,EAAElG,wBAAwB,CAAC9B,MAAM;MAChDiI,kBAAkB,EAAEnG,wBAAwB,CAACoG,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;MACjE4D,SAAS,EAAErG,wBAAwB;MACnCsG,kBAAkB,EAAEhG,2BAA2B,CAACpC,MAAM;MACtDqI,qBAAqB,EAAEjG,2BAA2B,CAAC8F,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;MACvE+D,YAAY,EAAElG;IAChB,CAAC,CAAC;IAEF,IAAIN,wBAAwB,CAAC9B,MAAM,GAAG,CAAC,IAAI8B,wBAAwB,CAACoG,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MACxFgD,mBAAmB,CAAC/I,mBAAmB,EAAEmF,uBAAuB,EAAE,mBAAmB,CAAC;IACxF,CAAC,MAAM,IAAInF,mBAAmB,CAACmJ,OAAO,EAAE;MACtCnJ,mBAAmB,CAACmJ,OAAO,CAACC,SAAS,GAAG,uHAAuH;IACjK;IAEA,IAAIxF,2BAA2B,CAACpC,MAAM,GAAG,CAAC,IAAIoC,2BAA2B,CAAC8F,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MAC9FgD,mBAAmB,CAAC9I,sBAAsB,EAAEmI,0BAA0B,EAAE,sBAAsB,CAAC;IACjG,CAAC,MAAM,IAAInI,sBAAsB,CAACkJ,OAAO,EAAE;MACzClJ,sBAAsB,CAACkJ,OAAO,CAACC,SAAS,GAAG,0HAA0H;IACvK;IAEA,IAAIpF,4BAA4B,CAACxC,MAAM,GAAG,CAAC,IAAIwC,4BAA4B,CAAC0F,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MAChGgD,mBAAmB,CAAC7I,uBAAuB,EAAEqI,2BAA2B,EAAE,uBAAuB,CAAC;IACpG,CAAC,MAAM,IAAIrI,uBAAuB,CAACiJ,OAAO,EAAE;MAC1CjJ,uBAAuB,CAACiJ,OAAO,CAACC,SAAS,GAAG,2HAA2H;IACzK;;IAEA;IACA,IAAIjF,uBAAuB,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,uBAAuB,CAACuF,IAAI,CAAC3D,GAAG,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MACrGgD,mBAAmB,CAAC5I,iBAAiB,EAAEyI,qBAAqB,EAAE,iBAAiB,CAAC;IAClF,CAAC,MAAM,IAAIzI,iBAAiB,CAACgJ,OAAO,EAAE;MACpC;MACAhJ,iBAAiB,CAACgJ,OAAO,CAACC,SAAS,GAAG,gHAAgH;IACxJ;IAEA,IAAI7E,sBAAsB,CAAC/C,MAAM,GAAG,CAAC,IAAI+C,sBAAsB,CAACmF,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MACpFgD,mBAAmB,CAAC3I,qBAAqB,EAAEyI,yBAAyB,EAAE,sBAAsB,CAAC;IAC/F,CAAC,MAAM,IAAIzI,qBAAqB,CAAC+I,OAAO,EAAE;MACxC/I,qBAAqB,CAAC+I,OAAO,CAACC,SAAS,GAAG,0HAA0H;IACtK;EACF,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAGA,CAAA,kBACvBzK,OAAA;IAAK0K,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/B3K,OAAA;MAAK0K,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9E3K,OAAA;QAAK0K,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpD3K,OAAA;UAAK0K,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAE1B3K,OAAA;YAAK0K,SAAS,EAAC;UAAkF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxG/K,OAAA;YAAK0K,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/K,OAAA;YAAK0K,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,IAAI,CAAChK,YAAY,CAAC,CAAC,EAAE;IACnB,oBAAOf,OAAA,CAACyK,gBAAgB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;;EAEA;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC7B,MAAMvH,UAAU,GAAG,EAAAmH,sBAAA,GAAA3K,eAAe,CAACyB,sBAAsB,cAAAkJ,sBAAA,uBAAtCA,sBAAA,CAAwCrJ,GAAG,CAACmC,IAAI,IACjEd,eAAe,CAACc,IAAI,CAACb,IAAI,EAAEa,IAAI,CAACZ,KAAK,CACvC,CAAC,KAAI,EAAE;IAEP,MAAMa,wBAAwB,GAAG,EAAAkH,sBAAA,GAAA5K,eAAe,CAACyB,sBAAsB,cAAAmJ,sBAAA,uBAAtCA,sBAAA,CAAwCtJ,GAAG,CAACmC,IAAI,IAAI;MACnF;MACA,MAAME,KAAK,GAAGF,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACI,sBAAsB,IAAIJ,IAAI,CAACK,oBAAoB,IAAI,CAAC;MACvF,OAAOC,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER,MAAMK,2BAA2B,GAAG,EAAA6G,sBAAA,GAAA7K,eAAe,CAAC8B,sBAAsB,cAAA+I,sBAAA,uBAAtCA,sBAAA,CAAwCvJ,GAAG,CAACmC,IAAI,IAAI;MACtF;MACA,MAAME,KAAK,GAAGF,IAAI,CAACQ,GAAG,IAAIR,IAAI,CAACS,yBAAyB,IAAIT,IAAI,CAACU,uBAAuB,IAAI,CAAC;MAC7F,OAAOJ,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER,MAAMS,4BAA4B,GAAG,EAAA0G,sBAAA,GAAA9K,eAAe,CAACgC,wBAAwB,cAAA8I,sBAAA,uBAAxCA,sBAAA,CAA0CxJ,GAAG,CAACmC,IAAI,IAAI;MACzF;MACA,MAAME,KAAK,GAAGF,IAAI,CAACY,GAAG,IAAIZ,IAAI,CAACa,0BAA0B,IAAIb,IAAI,CAACzB,wBAAwB,IAAI,CAAC;MAC/F,OAAO+B,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC;IAC/B,CAAC,CAAC,KAAI,EAAE;IAER,MAAMgB,sBAAsB,GAAG,EAAAoG,sBAAA,GAAA/K,eAAe,CAACoC,kBAAkB,cAAA2I,sBAAA,uBAAlCA,sBAAA,CAAoCzJ,GAAG,CAACmC,IAAI,IACzEM,UAAU,CAACN,IAAI,CAACmB,GAAG,CAAC,IAAI,CAC1B,CAAC,KAAI,EAAE;;IAEP;IACA,MAAMoG,sBAAsB,GAAGtH,wBAAwB,CAACoG,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;IAC5E,MAAM8E,yBAAyB,GAAGjH,2BAA2B,CAAC8F,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;IAClF,MAAM+E,0BAA0B,GAAG9G,4BAA4B,CAAC0F,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;IACpF,MAAMgF,2BAA2B,GAAGxG,sBAAsB,CAACmF,IAAI,CAAC3D,GAAG,IAAIA,GAAG,GAAG,CAAC,CAAC;IAE/E,OAAQ3C,UAAU,CAAC5B,MAAM,GAAG,CAAC,KAAKoJ,sBAAsB,IAAIC,yBAAyB,IAC7EC,0BAA0B,IAAIC,2BAA2B,CAAC;EACpE,CAAC;EAED,IAAI,CAACT,gBAAgB,CAAC,CAAC,EAAE;IACvB,oBACEhL,OAAA;MAAK0K,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B3K,OAAA;QAAK0K,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxE3K,OAAA;UAAK0K,SAAS,EAAC,uCAAuC;UAAAC,QAAA,eACpD3K,OAAA;YAAK0K,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B3K,OAAA;cAAK0K,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN/K,OAAA;cAAK0K,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN/K,OAAA;cAAK0K,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,kBAC1B,EAACtJ,MAAM,CAACC,IAAI,CAAChB,eAAe,IAAI,CAAC,CAAC,CAAC,CAACoL,IAAI,CAAC,IAAI,CAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/K,OAAA;IAAK0K,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAE/B3K,OAAA;MAAK0K,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAGxE3K,OAAA;QAAK0K,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvG3K,OAAA;UACE0K,SAAS,EAAC,sCAAsC;UAChD9D,KAAK,EAAE1G,eAAgB;UAAAyK,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/K,OAAA;UAAG0K,SAAS,EAAC,2BAA2B;UAAC9D,KAAK,EAAExG,mBAAoB;UAAAuK,QAAA,EAAC;QAErE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN/K,OAAA;QAAK0K,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD3K,OAAA;UACE0K,SAAS,EAAC,2CAA2C;UACrD9D,KAAK,EAAExG,mBAAoB;UAAAuK,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN/K,OAAA;UAAK0J,GAAG,EAAEhJ;QAAoB;UAAAkK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAGN/K,OAAA;QAAK0K,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD3K,OAAA;UACE0K,SAAS,EAAC,2CAA2C;UACrD9D,KAAK,EAAExG,mBAAoB;UAAAuK,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN/K,OAAA;UAAK0J,GAAG,EAAE/I;QAAuB;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxC/K,OAAA;UAAK0K,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACzE3K,OAAA;YACI0K,SAAS,EAAC,6BAA6B;YACvC9D,KAAK,EAAE;cAAC,GAAGxG,mBAAmB;cAAE0G,UAAU,EAAE;YAAS,CAAE;YAAA6D,QAAA,EAC1D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/K,OAAA;YAAK4G,KAAK,EAAEvG,gBAAiB;YAAAsK,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/K,OAAA;QAAK0K,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC5D3K,OAAA;UACE0K,SAAS,EAAC,2CAA2C;UACrD9D,KAAK,EAAExG,mBAAoB;UAAAuK,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN/K,OAAA;UAAK0J,GAAG,EAAE9I,uBAAwB;UAAC8J,SAAS,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGA,CAAC,eAGN/K,OAAA;MAAK0K,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAEhF3K,OAAA;QAAK0K,SAAS,EAAC,0FAA0F;QAAAC,QAAA,gBACvG3K,OAAA;UACE0K,SAAS,EAAC,sCAAsC;UAChD9D,KAAK,EAAE1G,eAAgB;UAAAyK,QAAA,EACxB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/K,OAAA;UAAG0K,SAAS,EAAC,2BAA2B;UAAC9D,KAAK,EAAExG,mBAAoB;UAAAuK,QAAA,EAAC;QAErE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN/K,OAAA;QAAK0K,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD3K,OAAA;UACE0K,SAAS,EAAC,2CAA2C;UACrD9D,KAAK,EAAExG,mBAAoB;UAAAuK,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN/K,OAAA;UAAK0J,GAAG,EAAE7I;QAAkB;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnC/K,OAAA;UAAK0K,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACzE3K,OAAA;YACE0K,SAAS,EAAC,wBAAwB;YAClC9D,KAAK,EAAE;cAAC,GAAGxG,mBAAmB;cAAE0G,UAAU,EAAE;YAAS,CAAE;YAAA6D,QAAA,EACxD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/K,OAAA;YAAK4G,KAAK,EAAEvG,gBAAiB;YAAAsK,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAKN/K,OAAA;QAAK0K,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBACtD3K,OAAA;UACE0K,SAAS,EAAC,2CAA2C;UACrD9D,KAAK,EAAExG,mBAAoB;UAAAuK,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACN/K,OAAA;UAAK0J,GAAG,EAAE5I;QAAsB;UAAA8J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvC/K,OAAA;UAAK0K,SAAS,EAAC,4DAA4D;UAAAC,QAAA,gBACzE3K,OAAA;YACI0K,SAAS,EAAC,wBAAwB;YAClC9D,KAAK,EAAE;cAAC,GAAGxG,mBAAmB;cAAE0G,UAAU,EAAE;YAAS,CAAE;YAAA6D,QAAA,EAC1D;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN/K,OAAA;YAAK4G,KAAK,EAAEvG,gBAAiB;YAAAsK,QAAA,EAAC;UAE9B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxK,EAAA,CAn3BIN,8BAA8B;AAAA0L,EAAA,GAA9B1L,8BAA8B;AAq3BpC,eAAeA,8BAA8B;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}