/*
  Warnings:

  - A unique constraint covering the columns `[userId,realmId,year,month,accountId]` on the table `TrialBalanceReport` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "TrialBalanceReport_userId_realmId_year_month_key";

-- AlterTable
ALTER TABLE "Account" ALTER COLUMN "modifiedAt" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "AccountPayableAgingSummaryReport" ALTER COLUMN "modifiedAt" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "AccountReceivableAgingSummaryReport" ALTER COLUMN "modifiedAt" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "BalanceSheetReport" ALTER COLUMN "modifiedAt" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "ProfitLossReport" ALTER COLUMN "modifiedAt" SET DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "TrialBalanceReport" ALTER COLUMN "modifiedAt" SET DEFAULT CURRENT_TIMESTAMP;

-- CreateIndex
CREATE UNIQUE INDEX "TrialBalanceReport_userId_realmId_year_month_accountId_key" ON "TrialBalanceReport"("userId", "realmId", "year", "month", "accountId");
