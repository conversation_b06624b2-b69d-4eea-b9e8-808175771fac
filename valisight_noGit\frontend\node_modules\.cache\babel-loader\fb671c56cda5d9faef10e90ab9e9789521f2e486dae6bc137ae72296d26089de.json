{"ast": null, "code": "\"use strict\";\n\nexports.clamp = clamp;\nvar _index = require(\"./max.js\");\nvar _index2 = require(\"./min.js\");\n\n/**\n * @name clamp\n * @category Interval Helpers\n * @summary Return a date bounded by the start and the end of the given interval\n *\n * @description\n * Clamps a date to the lower bound with the start of the interval and the upper\n * bound with the end of the interval.\n *\n * - When the date is less than the start of the interval, the start is returned.\n * - When the date is greater than the end of the interval, the end is returned.\n * - Otherwise the date is returned.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be bounded\n * @param interval - The interval to bound to\n *\n * @returns The date bounded by the start and the end of the interval\n *\n * @example\n * // What is Mar, 21, 2021 bounded to an interval starting at Mar, 22, 2021 and ending at Apr, 01, 2021\n * const result = clamp(new Date(2021, 2, 21), {\n *   start: new Date(2021, 2, 22),\n *   end: new Date(2021, 3, 1),\n * })\n * //=> Mon Mar 22 2021 00:00:00\n */\nfunction clamp(date, interval) {\n  return (0, _index2.min)([(0, _index.max)([date, interval.start]), interval.end]);\n}", "map": {"version": 3, "names": ["exports", "clamp", "_index", "require", "_index2", "date", "interval", "min", "max", "start", "end"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/clamp.js"], "sourcesContent": ["\"use strict\";\nexports.clamp = clamp;\nvar _index = require(\"./max.js\");\nvar _index2 = require(\"./min.js\");\n\n/**\n * @name clamp\n * @category Interval Helpers\n * @summary Return a date bounded by the start and the end of the given interval\n *\n * @description\n * Clamps a date to the lower bound with the start of the interval and the upper\n * bound with the end of the interval.\n *\n * - When the date is less than the start of the interval, the start is returned.\n * - When the date is greater than the end of the interval, the end is returned.\n * - Otherwise the date is returned.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be bounded\n * @param interval - The interval to bound to\n *\n * @returns The date bounded by the start and the end of the interval\n *\n * @example\n * // What is Mar, 21, 2021 bounded to an interval starting at Mar, 22, 2021 and ending at Apr, 01, 2021\n * const result = clamp(new Date(2021, 2, 21), {\n *   start: new Date(2021, 2, 22),\n *   end: new Date(2021, 3, 1),\n * })\n * //=> Mon Mar 22 2021 00:00:00\n */\nfunction clamp(date, interval) {\n  return (0, _index2.min)([\n    (0, _index.max)([date, interval.start]),\n    interval.end,\n  ]);\n}\n"], "mappings": "AAAA,YAAY;;AACZA,OAAO,CAACC,KAAK,GAAGA,KAAK;AACrB,IAAIC,MAAM,GAAGC,OAAO,CAAC,UAAU,CAAC;AAChC,IAAIC,OAAO,GAAGD,OAAO,CAAC,UAAU,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,KAAKA,CAACI,IAAI,EAAEC,QAAQ,EAAE;EAC7B,OAAO,CAAC,CAAC,EAAEF,OAAO,CAACG,GAAG,EAAE,CACtB,CAAC,CAAC,EAAEL,MAAM,CAACM,GAAG,EAAE,CAACH,IAAI,EAAEC,QAAQ,CAACG,KAAK,CAAC,CAAC,EACvCH,QAAQ,CAACI,GAAG,CACb,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}