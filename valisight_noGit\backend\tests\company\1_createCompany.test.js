import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js";
const baseUrl = `/api/v1/company`
import { getToken } from "../helpers/companyHelpers.js";
import { prisma } from "../../src/db/prisma.js";


describe("Company Registration API", () => {    
      let authToken;
      before(async function () {
        authToken = await getToken();
      });
   
         
    it("should register a company successfully", async () => {
        const res = await request(app)
            .post(`${baseUrl}/`)
            .set("Authorization", `Bearer ${authToken}`) 
            .send({
                name: "Test Company",
                fiscal_year_end: "2025-03-31",
                country: "USA",
                description: "A test company",
                naics: "12345",
                state:"Florida",
                city:"Anna Maria",
                users:[]
            })   
            
        expect(res._body.statusCode).to.equal(201);
        expect(res._body.success).to.equal(true);
    });

    it("should return validation error", async () => {
        const res = await request(app)
        .post(`${baseUrl}/`)
        .set("Authorization", `Bearer ${authToken}`) 
        .send({
            name:"hello"
        })                
        expect(res._body.statusCode).to.equal(400);
        expect(res.body.success).to.equal(false);
    });
    it("should return error if company already exists", async () => {
        const payload = { name: "Test Company",
            fiscal_year_end: "2025-03-31",
            country: "USA",
            description: "A test company",
            naics: "12345",
            state:"Florida",
            city:"Anna Maria"}
        const res = await request(app)
            .post(`${baseUrl}/`)
            .set("Authorization", `Bearer ${authToken}`) 
            .send(payload) 

        expect(res._body.statusCode).to.equal(400);
        expect(res._body.message).to.contain(`"${payload.name}"`);
    });


    it("should handle errors during company registration", async () => {
    
        const res = await request(app)
          .post(`${baseUrl}/`)
          .set("Authorization", `Bearer ${authToken}`)
          .send({
            name: "Error Company",
            fiscal_year_end: "2025-03-31",
            country: "USA",
            state: "Florida",
            city: "Anna Maria",
            users: [0],
          });          
    
        expect(res._body.statusCode).to.equal(500);
        expect(res._body.success).to.equal(false);
    
      });
});
