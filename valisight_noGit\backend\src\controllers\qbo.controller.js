// backend\src\controllers\qbo.controller.js

import { PrismaClient } from '@prisma/client';
import axios from 'axios';
import qboService from '../services/qbo.service.js';
import { ErrorHandler } from '../utils/errorHandler.js';
import {
  handleErrorResponse,
  handleSuccessResponse,
} from '../utils/responseHandler.js';
const prisma = new PrismaClient();

const qboConnect = async (req, res) => {
  try {
    const { companyId } = req.query;

    // Validate required parameters
    if (!companyId) {
      throw new ErrorHandler('Company ID is required', 400);
    }

    // Validate companyId is a valid number
    const parsedCompanyId = Number(companyId);
    if (isNaN(parsedCompanyId) || parsedCompanyId <= 0) {
      throw new ErrorHandler('Invalid company ID format', 400);
    }

    // Validate user authentication
    if (!req.user || !req.user.id) {
      throw new ErrorHandler('User authentication required', 401);
    }

    // Check if company exists
    const company = await prisma.company.findUnique({
      where: { id: parsedCompanyId },
    });

    if (!company) {
      throw new ErrorHandler('Company not found', 404);
    }

    // Generate QuickBooks authorization URL
    const url = await qboService.getAuthorizationUrl(
      req.user.id,
      parsedCompanyId,
    );

    if (!url) {
      throw new ErrorHandler(
        'Failed to generate QuickBooks authorization URL',
        500,
      );
    }

    // SUCCESS RESPONSE
    return handleSuccessResponse(
      res,
      'QuickBooks authorization URL generated successfully',
      { url, companyId: parsedCompanyId },
    );
  } catch (error) {
    return handleErrorResponse(error, res, req, 'QBO Connect');
  }
};

// Updated QBO Callback Handler
const qboCallback = async (req, res) => {
  try {
    const { code, realmId, state } = req.query;

    // Validate required parameters
    if (!code) {
      throw new ErrorHandler('Authorization code is required', 400);
    }

    if (!realmId) {
      throw new ErrorHandler(
        'QuickBooks company ID (realmId) is required',
        400,
      );
    }

    if (!state) {
      throw new ErrorHandler(
        'State parameter is required for security validation',
        400,
      );
    }

    // Process the callback
    const result = await qboService.processCallback(code, realmId, state);

    if (!result) {
      throw new ErrorHandler('Failed to process QuickBooks callback', 500);
    }

    // Set success cookie
    res.cookie(
      'qbo_status',
      result.message || 'QuickBooks connected successfully',
      {
        httpOnly: false,
        maxAge: 60 * 1000, // 1 minute
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
      },
    );

    // SUCCESS RESPONSE
    return handleSuccessResponse(
      res,
      result.message || 'QuickBooks authorization completed successfully',
      result.data || {},
    );
  } catch (error) {
    const companyId = error.companyId || getCompanyIdFromState(req.query.state);

    // Set error cookies
    const errorMessage =
      error instanceof ErrorHandler
        ? error.message
        : 'An unexpected error occurred while processing QuickBooks callback';

    res.cookie('qbo_error', errorMessage, {
      httpOnly: false,
      maxAge: 60 * 1000,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
    });

    res.cookie('qbo_status', 'error', {
      httpOnly: false,
      maxAge: 60 * 1000,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
    });

    return handleErrorResponse(error, res, req, 'QBO Callback', {
      ...(companyId && { companyId }),
    });
  }
};

// Updated QBO Status Handler
const qboStatus = async (req, res) => {
  try {
    const { companyId } = req.query;

    // Validate required parameters
    if (!companyId) {
      throw new ErrorHandler('Company ID is required', 400);
    }

    // Validate companyId is a valid number
    const parsedCompanyId = Number(companyId);
    if (isNaN(parsedCompanyId) || parsedCompanyId <= 0) {
      throw new ErrorHandler('Invalid company ID format', 400);
    }

    // Validate user authentication
    if (!req.user || !req.user.id) {
      throw new ErrorHandler('User authentication required', 401);
    }

    // Get integration status
    const status = await qboService.getIntegrationStatus(parsedCompanyId);

    if (!status) {
      throw new ErrorHandler(
        'Unable to retrieve QuickBooks integration status',
        500,
      );
    }

    // SUCCESS RESPONSE
    return handleSuccessResponse(
      res,
      'QuickBooks integration status retrieved successfully',
      status,
    );
  } catch (error) {
    return handleErrorResponse(error, res, req, 'QBO Status', {
      connected: false,
      companyId: req.query?.companyId,
      connectionStatus: 'disconnected',
      lastSync: null,
    });
  }
};

const qboDisconnect = async (req, res) => {
  try {
    const { companyId } = req.query;

    // Get the company to retrieve the access token
    const company = await qboService.ensureCompanyExists(companyId);

    // Revoke the QBO access token if it exists
    if (company.qboAccessToken) {
      try {
        await qboService.revokeToken(company.qboAccessToken);
      } catch (err) {
        // If the error is "invalid or already revoked token", ignore it
        if (
          err.message === 'Invalid or already revoked token' ||
          err.message.includes('invalid_grant')
        ) {
          // Log and continue
          console.warn(
            `[QBO] Token already invalid/revoked for company ${companyId}`,
          );
        } else {
          // For other errors, rethrow
          throw err;
        }
      }
    }

    // Disconnect integration (removes tokens, updates status, etc.)
    await qboService.disconnectIntegration(companyId);

    res.json({
      success: true,
      message: 'Disconnected from QuickBooks.',
    });
  } catch (error) {
    const statusCode = getStatusCode(error);
    res.status(statusCode).json({
      success: false,
      message: error.message || 'Error disconnecting',
    });
  }
};

// Helper function to determine appropriate HTTP status code
const getStatusCode = (error) => {
  if (
    error.message.includes('required') ||
    error.message.includes('must be') ||
    error.message.includes('Invalid')
  ) {
    return 400; // Bad Request
  }
  if (
    error.message.includes('not found') ||
    (error.message.includes('No') && error.message.includes('found'))
  ) {
    return 404; // Not Found
  }
  if (
    error.message.includes('Authentication') ||
    error.message.includes('expired') ||
    error.message.includes('token')
  ) {
    return 401; // Unauthorized
  }
  if (
    error.message.includes('forbidden') ||
    error.message.includes('permissions')
  ) {
    return 403; // Forbidden
  }
  return 500; // Internal Server Error
};

// Helper function to extract companyId from state when error occurs
const getCompanyIdFromState = (state) => {
  try {
    if (!state) return '';
    const parsedState = JSON.parse(state);
    return parsedState.companyId || '';
  } catch {
    return '';
  }
};

const syncHandler = async (req, res) => {
  try {
    const { companyId } = req.body;
    const { reportType } = req.params;

    // Validate required parameters
    if (!companyId) {
      return res.status(400).json({
        success: false,
        message: 'Company ID is required',
        data: [],
        error: 'Missing companyId in request body',
      });
    }

    if (!reportType) {
      return res.status(400).json({
        success: false,
        message: 'Report type is required',
        data: [],
        error: 'Missing reportType in request params',
      });
    }

    // Fetch company data
    const companyData = await prisma.company.findFirst({
      where: { id: companyId },
    });

    if (!companyData) {
      return res.status(404).json({
        success: false,
        message: 'Company not found',
        data: [],
        error: 'Company with the provided ID does not exist',
      });
    }

    // Define endpoint mapping
    const endpointMapping = {
      accounts: process.env.QUICKBOOKS_SYNC_COA_URL,
      'trial-balance': process.env.QUICKBOOKS_SYNC_TRIAL_BALANCE_URL,
      'profit-loss': process.env.QUICKBOOKS_SYNC_PROFIT_LOSS_URL,
      'balance-sheet': process.env.QUICKBOOKS_SYNC_BALANCE_SHEET_URL,
      'ap-aging': process.env.QUICKBOOKS_SYNC_ACCOUNT_PAYABLE_URL,
      'ar-aging': process.env.QUICKBOOKS_SYNC_ACCOUNT_RECEIVABLE_URL,
    };

    const endpoint = endpointMapping[reportType];

    // Validate report type and endpoint
    if (!endpoint) {
      const validReportTypes = Object.keys(endpointMapping);
      return res.status(400).json({
        success: false,
        message: `Invalid report type. Valid types are: ${validReportTypes.join(', ')}`,
        data: [],
        error: 'Invalid report type',
      });
    }

    // Prepare payload
    const payload = {
      companyId: companyData.id,
      userId: companyData.userId,
      dumpToDatabase: true,
    };

    // Make API call to sync service

    const response = await axios.post(endpoint, payload, {
      timeout: 30000, // 30 second timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Update sync date for accounts report
    if (reportType === 'accounts' && response.data) {
      await prisma.company.update({
        where: { id: companyId },
        data: {
          TransactionReportLastSyncDate: new Date(),
        },
      });
    }

    // Return successful response
    return res.status(200).json({
      success: true,
      message: `${reportType} sync completed successfully`,
      data: response.data || [],
    });
  } catch (error) {
    console.error('Sync Handler Error:', {
      companyId: req.body?.companyId,
      reportType: req.params?.reportType,
      error: error.message,
      stack: error.stack,
    });

    // Handle specific error types
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      return res.status(408).json({
        success: false,
        message: 'Request timeout - sync service took too long to respond',
        data: [],
        error: 'Request timeout',
      });
    }

    if (error.response) {
      // API responded with error status
      return res.status(error.response.status || 500).json({
        success: false,
        message: 'Sync service returned an error',
        data: [],
        error: error.response.data?.message || 'External API error',
      });
    }

    if (error.request) {
      // Network error
      return res.status(503).json({
        success: false,
        message: 'Unable to reach sync service',
        data: [],
        error: 'Network error',
      });
    }

    // Database or other internal errors
    return res.status(500).json({
      success: false,
      message: 'Internal server error during sync process',
      data: [],
      error: 'Internal server error',
    });
  }
};

const qboController = {
  qboConnect,
  qboCallback,
  qboStatus,
  qboDisconnect,
  syncHandler,
};

export { qboController };
