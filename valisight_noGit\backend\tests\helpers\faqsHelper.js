import { prisma } from "../../src/db/prisma.js"

export const getFaqsId = async()=>{
    let faqs = await prisma.faqs.findMany({
        where:{
            OR:[
                {answer:"30 days return."
                },
                {answer:"Yes, worldwide."
                },
                {answer:"Docker is a containerization platform."}

            ]
        },
        select:{
            id:true
        }
    })
    return faqs
}
