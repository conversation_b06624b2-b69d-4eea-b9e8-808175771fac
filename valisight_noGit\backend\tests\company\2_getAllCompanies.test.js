import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
const baseUrl = `/api/v1/company`;
import { getToken } from "../helpers/companyHelpers.js";


describe("Get All Companies API", () => {
  let authToken;
  before(async function () {
    authToken = await getToken();
  });


  it("should fetch all companies successfully", async () => {
    const res = await request(app)
      .get(`${baseUrl}/all`)
      .set("Authorization", `Bearer ${authToken}`)
      .query({ page: 1, pageSize: 10, search: "Test" });    
    
    expect(res._body.statusCode).to.equal(200);
    expect(res._body.success).to.be.true;
    expect(res.body.companies).to.be.an("array");
    expect(res.body.pagination).to.have.property("currentPage");
  });

  it("should return validation error if pageSize is invalid", async () => {
    const res = await request(app)
      .get(`${baseUrl}/all`)
      .set("Authorization", `Bearer ${authToken}`)
      .query({ page: 1, pageSize: "invalid" });    

    expect(res._body.statusCode).to.equal(400);
    expect(res.body.success).to.be.false;
    expect(res.body.message).to.contain("Page");
  });
  

  it("should return empty array if no companies found", async () => {
    const res = await request(app)
      .get(`${baseUrl}/all`)
      .set("Authorization", `Bearer ${authToken}`)
      .query({ page: 100, pageSize: 10, search: "NonExistingCompany" });
    expect(res._body.statusCode).to.equal(200);
    expect(res._body.success).to.be.true;
    expect(res._body.companies).to.be.an("array").that.is.empty;
  });

  it("should return unauthorized error if no token is provided", async () => {
    const res = await request(app)
      .get(`${baseUrl}/all`)
      .query({ page: 1, pageSize: 10 });

    expect(res._body.statusCode).to.equal(401);
    expect(res._body.success).to.be.false;
    expect(res._body.message).to.contain("Unauthorized");
  });
});
