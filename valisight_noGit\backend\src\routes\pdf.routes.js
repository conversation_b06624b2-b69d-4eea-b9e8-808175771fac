import { Router } from 'express';
import * as pdfController from '../controllers/pdf.controller.js';
import { authenticate } from '../middleware/auth.middleware.js';

export const pdfRoute = Router();

// Generate PDF from custom report data
// pdfRoute.post('/generate-custom-report', authenticate, pdfController.generateCustomReportPDF);

// Generate PDF from HTML content
pdfRoute.post('/generate-from-html', authenticate, pdfController.generatePDFFromHTML);