{"ast": null, "code": "\"use strict\";\n\nexports.formatRelative = void 0;\nconst formatRelativeLocale = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];\nexports.formatRelative = formatRelative;", "map": {"version": 3, "names": ["exports", "formatRelative", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "token", "_date", "_baseDate", "_options"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/node_modules/date-fns/locale/en-US/_lib/formatRelative.js"], "sourcesContent": ["\"use strict\";\nexports.formatRelative = void 0;\n\nconst formatRelativeLocale = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: \"P\",\n};\n\nconst formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\nexports.formatRelative = formatRelative;\n"], "mappings": "AAAA,YAAY;;AACZA,OAAO,CAACC,cAAc,GAAG,KAAK,CAAC;AAE/B,MAAMC,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,oBAAoB;EAC9BC,SAAS,EAAE,kBAAkB;EAC7BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMP,cAAc,GAAGA,CAACQ,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KACvDV,oBAAoB,CAACO,KAAK,CAAC;AAC7BT,OAAO,CAACC,cAAc,GAAGA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}