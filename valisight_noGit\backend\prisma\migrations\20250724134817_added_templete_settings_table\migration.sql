-- CreateEnum
CREATE TYPE "ReportTemplate" AS ENUM ('DEEPSIGHT');

-- CreateEnum
CREATE TYPE "TemplateType" AS ENUM ('GLOBAL', 'CUSTOM');

-- CreateTable
CREATE TABLE "TemplateSettings" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER,
    "reportType" "ReportTemplate" NOT NULL DEFAULT 'DEEPSIGHT',
    "templateType" "TemplateType" NOT NULL DEFAULT 'GLOBAL',
    "settings" JSONB NOT NULL,
    "updatedBy" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TemplateSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TemplateSettings_userId_idx" ON "TemplateSettings"("userId");

-- CreateIndex
CREATE INDEX "TemplateSettings_templateType_idx" ON "TemplateSettings"("templateType");

-- CreateIndex
CREATE INDEX "TemplateSettings_reportType_idx" ON "TemplateSettings"("reportType");

-- CreateIndex
CREATE UNIQUE INDEX "TemplateSettings_userId_reportType_templateType_key" ON "TemplateSettings"("userId", "reportType", "templateType");

-- CreateIndex
CREATE UNIQUE INDEX "TemplateSettings_templateType_reportType_key" ON "TemplateSettings"("templateType", "reportType");

-- AddForeignKey
ALTER TABLE "TemplateSettings" ADD CONSTRAINT "TemplateSettings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TemplateSettings" ADD CONSTRAINT "TemplateSettings_updatedBy_fkey" FOREIGN KEY ("updatedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
