{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\JASON_NEW\\\\valisight_noGit\\\\frontend\\\\src\\\\pages\\\\reports\\\\CustomizeReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { IconButton, Tooltip, CircularProgress, Box, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Button } from '@mui/material';\nimport { Sync as SyncIcon, ArrowBack, Download as DownloadIcon } from '@mui/icons-material';\nimport { Snackbar, Alert } from '@mui/material';\nimport { useSearchParams } from 'react-router-dom';\nimport TableOfContents from './ReportPages/TableOfContents';\nimport ReportSummary from './ReportPages/ReportSummary';\nimport FiscalYearDashboard from './ReportPages/FiscalYear';\nimport ExpenseSummaryDashboard from './ReportPages/ExpenseSummary';\nimport OperationalEfficiencyDashboard from './ReportPages/OperationalEfficiency';\nimport LiquiditySummaryDashboard from './ReportPages/LiquiditySummary';\nimport ProfitLoss13MonthDashboard from './ReportPages/MonthTrailing';\nimport ProfitLossMonthlyDashboard from './ReportPages/Monthly';\nimport ProfitLossYTDDashboard from './ReportPages/YearToDate';\nimport BalanceSheetDashboard from './ReportPages/BalanceSheet';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport axiosInstance from '../../services/axiosInstance';\nimport { downloadPDFFromBase64 } from '../../services/pdf';\nimport DeepSightCoverPage from './ReportPages/CoverPage';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CustomizeTemplateWithPreview = () => {\n  _s();\n  const [searchParams] = useSearchParams();\n  const params = useParams();\n  const companyId = params.id;\n\n  // API Data State\n  const [reportData, setReportData] = useState(null);\n  const [isLoadingData, setIsLoadingData] = useState(true);\n  const [dataError, setDataError] = useState(null);\n\n  // Template Settings State\n  const [templateSettings, setTemplateSettings] = useState(null);\n  const [initialSettings, setInitialSettings] = useState(null); // Store initial settings for reset\n  const [isLoadingSettings, setIsLoadingSettings] = useState(true);\n  const [settingsError, setSettingsError] = useState(null);\n\n  // Content Settings State (NEW)\n  const [contentSettings, setContentSettings] = useState(null);\n  const [isLoadingContentSettings, setIsLoadingContentSettings] = useState(true);\n  const [contentSettingsError, setContentSettingsError] = useState(null);\n\n  // UI State\n  const [showSuccess, setShowSuccess] = useState(false);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState('Deepsight');\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\n  const [isSavingSettings, setIsSavingSettings] = useState(false);\n\n  // Modal State\n  const [showSaveConfirmModal, setShowSaveConfirmModal] = useState(false);\n  const [showResetConfirmModal, setShowResetConfirmModal] = useState(false);\n  const navigate = useNavigate();\n\n  // Default settings fallback\n  const defaultSettings = {\n    header: {\n      fontStyle: 'Helvetica',\n      fontType: 'Bold',\n      fontSize: 44,\n      color: '#1e7c8c'\n    },\n    heading: {\n      fontStyle: 'Helvetica',\n      fontType: 'Bold',\n      fontSize: 36,\n      color: '#1e7c8c'\n    },\n    subHeading: {\n      fontStyle: 'Helvetica',\n      fontType: 'Bold',\n      fontSize: 22,\n      color: '#1e7c8c'\n    },\n    content: {\n      fontStyle: 'Helvetica',\n      fontType: 'Regular',\n      fontSize: 15,\n      color: '#333333'\n    }\n  };\n  const fontStyles = [\"Open Sans\", 'Calibri', 'Arial', 'Times New Roman', 'Georgia', 'Verdana', 'Helvetica'];\n  const fontTypes = ['Regular', 'Bold'];\n\n  // Font size constraints\n  const fontSizeConstraints = {\n    header: {\n      min: 32,\n      max: 48\n    },\n    heading: {\n      min: 32,\n      max: 42\n    },\n    subHeading: {\n      min: 18,\n      max: 28\n    },\n    content: {\n      min: 9,\n      max: 20\n    }\n  };\n\n  // Fetch content settings from API (NEW)\n  const fetchContentSettings = async () => {\n    try {\n      setIsLoadingContentSettings(true);\n      setContentSettingsError(null);\n      const response = await axiosInstance.get(`/content-settings/company/${companyId}/DEEPSIGHT`);\n      if (response.data && response.data.success) {\n        const settingsData = response.data.data;\n        console.log('Content settings loaded from API:', settingsData);\n        setContentSettings(settingsData);\n      } else {\n        throw new Error('Failed to fetch content settings');\n      }\n    } catch (error) {\n      console.error('Error fetching content settings:', error);\n      setContentSettingsError(error.message);\n\n      // Set default content settings if API fails\n      const defaultContentSettings = {\n        chartSettings: {\n          incomeSummary: true,\n          netIncome: true,\n          grossProfitMargin: true,\n          netProfitMargin: true,\n          roaAndRoe: true\n        }\n      };\n      setContentSettings(defaultContentSettings);\n      setSuccessMessage('Using default chart settings. Failed to load from server.');\n      setShowSuccess(true);\n    } finally {\n      setIsLoadingContentSettings(false);\n    }\n  };\n\n  // Fetch template settings from API\n  const fetchTemplateSettings = async () => {\n    try {\n      setIsLoadingSettings(true);\n      setSettingsError(null);\n      const response = await axiosInstance.get('/template-settings');\n      if (response.data && response.data.success) {\n        const settingsData = response.data.data.settings;\n\n        // Store settings in localStorage\n        localStorage.setItem('templateSettings', JSON.stringify(settingsData));\n\n        // Set both current and initial settings\n        setTemplateSettings(settingsData);\n        setInitialSettings(settingsData);\n        console.log('Template settings loaded from API:', response.data.data);\n      } else {\n        throw new Error('Failed to fetch template settings');\n      }\n    } catch (error) {\n      console.error('Error fetching template settings:', error);\n      setSettingsError(error.message);\n\n      // Fallback to localStorage or default settings\n      const savedSettings = localStorage.getItem('templateSettings');\n      const fallbackSettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;\n      setTemplateSettings(fallbackSettings);\n      setInitialSettings(fallbackSettings);\n      setSuccessMessage('Using cached settings. Failed to load from server.');\n      setShowSuccess(true);\n    } finally {\n      setIsLoadingSettings(false);\n    }\n  };\n\n  // Load settings from URL params (for PDF generation) or fetch from API\n  const initializeSettings = () => {\n    try {\n      // First check if settings are in URL (for PDF generation)\n      const urlSettings = searchParams.get('templateSettings');\n      if (urlSettings) {\n        const parsedSettings = JSON.parse(urlSettings);\n        setTemplateSettings(parsedSettings);\n        setInitialSettings(parsedSettings);\n        setIsLoadingSettings(false);\n        return;\n      }\n\n      // Otherwise fetch from API\n      fetchTemplateSettings();\n    } catch (error) {\n      console.error('Error initializing settings:', error);\n      setTemplateSettings(defaultSettings);\n      setInitialSettings(defaultSettings);\n      setIsLoadingSettings(false);\n    }\n  };\n\n  // Fetch report data from API\n  const fetchReportData = async () => {\n    try {\n      setIsLoadingData(true);\n      setDataError(null);\n      const response = await axiosInstance.get('/report/1/generate-calculation');\n      if (response.data && response.data.success) {\n        console.log('CustomizeReport - API Response:', response.data.data);\n        setReportData(response.data.data);\n      } else {\n        throw new Error('Failed to fetch report data');\n      }\n    } catch (error) {\n      console.error('Error fetching report data:', error);\n      setDataError(error.message);\n      setSuccessMessage('Failed to load report data. Please try again.');\n      setShowSuccess(true);\n    } finally {\n      setIsLoadingData(false);\n    }\n  };\n\n  // Generate and download PDF\n  // In CustomizeReport.jsx, update the handleDownloadPDF function\n\n  // In CustomizeReport.jsx, update the handleDownloadPDF function\n\n  const handleDownloadPDF = async () => {\n    try {\n      setIsGeneratingPDF(true);\n\n      // Get the scrollable content panel that contains all components\n      const contentPanel = document.querySelector('.flex-1.overflow-y-auto.bg-gray-200');\n      if (!contentPanel) {\n        throw new Error('Could not find report content to generate PDF');\n      }\n\n      // Wait for charts to fully render before capturing content\n      await new Promise(resolve => setTimeout(resolve, 3000));\n\n      // FREEZE CHART ELEMENTS AND LEGENDS BEFORE CLONING\n      const freezeChartsAndLegends = container => {\n        // 1. Freeze Chart Elements\n        const chartContainers = container.querySelectorAll('div[id*=\"apex\"]');\n        console.log(`Found ${chartContainers.length} charts to freeze`);\n        chartContainers.forEach((chartContainer, index) => {\n          try {\n            // Freeze all SVG elements within the chart\n            const svgElements = chartContainer.querySelectorAll('svg, svg *');\n            svgElements.forEach(svgEl => {\n              // Get computed styles and apply them inline\n              const computedStyle = window.getComputedStyle(svgEl);\n\n              // Apply critical styles inline to prevent changes\n              if (svgEl.tagName.toLowerCase() === 'svg') {\n                svgEl.style.width = computedStyle.width;\n                svgEl.style.height = computedStyle.height;\n                svgEl.style.overflow = 'visible';\n              }\n\n              // Freeze stroke and fill properties\n              if (computedStyle.stroke && computedStyle.stroke !== 'none') {\n                svgEl.style.stroke = computedStyle.stroke;\n              }\n              if (computedStyle.strokeWidth && computedStyle.strokeWidth !== '0px') {\n                svgEl.style.strokeWidth = computedStyle.strokeWidth;\n              }\n              if (computedStyle.fill && computedStyle.fill !== 'none') {\n                svgEl.style.fill = computedStyle.fill;\n              }\n              if (computedStyle.strokeDasharray && computedStyle.strokeDasharray !== 'none') {\n                svgEl.style.strokeDasharray = computedStyle.strokeDasharray;\n              }\n\n              // Prevent any transformations from changing\n              if (computedStyle.transform && computedStyle.transform !== 'none') {\n                svgEl.style.transform = computedStyle.transform;\n              }\n            });\n\n            // Freeze the chart container dimensions and position\n            const containerRect = chartContainer.getBoundingClientRect();\n            chartContainer.style.width = containerRect.width + 'px';\n            chartContainer.style.height = containerRect.height + 'px';\n            chartContainer.style.position = 'relative';\n            chartContainer.style.overflow = 'visible';\n\n            // Freeze all ApexCharts specific elements\n            const apexElements = chartContainer.querySelectorAll('[class*=\"apexcharts\"]');\n            apexElements.forEach(element => {\n              const computedStyle = window.getComputedStyle(element);\n\n              // Preserve positioning\n              if (computedStyle.position && computedStyle.position !== 'static') {\n                element.style.position = computedStyle.position;\n                element.style.top = computedStyle.top;\n                element.style.left = computedStyle.left;\n                element.style.right = computedStyle.right;\n                element.style.bottom = computedStyle.bottom;\n              }\n\n              // Preserve dimensions\n              element.style.width = computedStyle.width;\n              element.style.height = computedStyle.height;\n\n              // Preserve display and visibility\n              element.style.display = computedStyle.display;\n              element.style.visibility = computedStyle.visibility;\n              element.style.opacity = computedStyle.opacity;\n            });\n\n            // Specifically handle grid lines and axis elements that might be causing extra lines\n            const gridLines = chartContainer.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');\n            gridLines.forEach(line => {\n              const computedStyle = window.getComputedStyle(line);\n              line.style.stroke = computedStyle.stroke;\n              line.style.strokeWidth = computedStyle.strokeWidth;\n              line.style.strokeDasharray = computedStyle.strokeDasharray;\n              line.style.opacity = computedStyle.opacity;\n            });\n\n            // Handle axis lines\n            const axisLines = chartContainer.querySelectorAll('.apexcharts-xaxis line, .apexcharts-yaxis line');\n            axisLines.forEach(line => {\n              const computedStyle = window.getComputedStyle(line);\n              line.style.stroke = computedStyle.stroke;\n              line.style.strokeWidth = computedStyle.strokeWidth;\n              line.style.opacity = computedStyle.opacity;\n            });\n\n            // Handle data series paths/bars\n            const seriesElements = chartContainer.querySelectorAll('.apexcharts-series path, .apexcharts-series rect, .apexcharts-series circle');\n            seriesElements.forEach(element => {\n              const computedStyle = window.getComputedStyle(element);\n              if (computedStyle.fill) element.style.fill = computedStyle.fill;\n              if (computedStyle.stroke) element.style.stroke = computedStyle.stroke;\n              if (computedStyle.strokeWidth) element.style.strokeWidth = computedStyle.strokeWidth;\n              if (computedStyle.opacity) element.style.opacity = computedStyle.opacity;\n            });\n            console.log(`Chart ${index} frozen with dimensions: ${containerRect.width}x${containerRect.height}`);\n          } catch (error) {\n            console.error(`Error freezing chart ${index}:`, error);\n          }\n        });\n\n        // 2. Freeze Legend Elements (your existing legend freezing code)\n        const legends = container.querySelectorAll('.apexcharts-legend');\n        console.log(`Found ${legends.length} legends to freeze`);\n        legends.forEach((legend, index) => {\n          try {\n            // Get current position relative to viewport\n            const rect = legend.getBoundingClientRect();\n\n            // Find the closest chart container or parent container\n            let chartContainer = legend.closest('div[id*=\"apex\"]');\n            if (!chartContainer) {\n              // Look for chart section container\n              chartContainer = legend.closest('.bg-white.p-6');\n            }\n            if (!chartContainer) {\n              // Fallback to component container\n              chartContainer = legend.closest('.min-h-screen');\n            }\n            if (chartContainer) {\n              const parentRect = chartContainer.getBoundingClientRect();\n\n              // Calculate position relative to the chart container\n              const relativeTop = rect.top - parentRect.top;\n              const relativeLeft = rect.left - parentRect.left;\n\n              // Store original styles for debugging\n              legend.setAttribute('data-original-position', legend.style.position || '');\n              legend.setAttribute('data-original-top', legend.style.top || '');\n              legend.setAttribute('data-original-left', legend.style.left || '');\n\n              // Set the legend to absolute positioning relative to its chart container\n              legend.style.position = 'absolute';\n              legend.style.top = Math.max(0, relativeTop) + 'px';\n              legend.style.left = relativeLeft + 'px';\n              legend.style.right = 'auto';\n              legend.style.bottom = 'auto';\n              legend.style.transform = 'none';\n              legend.style.zIndex = '10';\n\n              // Ensure the chart container has relative positioning\n              const containerPosition = window.getComputedStyle(chartContainer).position;\n              if (containerPosition === 'static') {\n                chartContainer.style.position = 'relative';\n              }\n\n              // Freeze legend's internal styling\n              const legendElements = legend.querySelectorAll('*');\n              legendElements.forEach(el => {\n                const computedStyle = window.getComputedStyle(el);\n                if (computedStyle.color) el.style.color = computedStyle.color;\n                if (computedStyle.fontSize) el.style.fontSize = computedStyle.fontSize;\n                if (computedStyle.fontFamily) el.style.fontFamily = computedStyle.fontFamily;\n                if (computedStyle.fontWeight) el.style.fontWeight = computedStyle.fontWeight;\n              });\n              console.log(`Legend ${index} frozen at position: ${relativeTop}px, ${relativeLeft}px`);\n            } else {\n              console.warn(`Could not find chart container for legend ${index}`);\n\n              // Fallback: just prevent the legend from moving by setting fixed position\n              legend.style.position = 'relative';\n              legend.style.top = '20px';\n              legend.style.left = 'auto';\n              legend.style.right = 'auto';\n              legend.style.transform = 'none';\n              legend.style.margin = '20px auto';\n              legend.style.display = 'flex';\n              legend.style.justifyContent = 'center';\n            }\n          } catch (error) {\n            console.error(`Error freezing legend ${index}:`, error);\n\n            // Emergency fallback - just make it static\n            legend.style.position = 'static';\n            legend.style.margin = '20px auto';\n            legend.style.display = 'flex';\n            legend.style.justifyContent = 'center';\n          }\n        });\n      };\n\n      // Apply the freezing function\n      freezeChartsAndLegends(contentPanel);\n\n      // Wait a bit more for positions to settle after freezing\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      // Now clone the content with frozen chart and legend positions\n      const clonedContent = contentPanel.cloneNode(true);\n\n      // Additional safety check: ensure no extra lines are added during cloning\n      const clonedCharts = clonedContent.querySelectorAll('div[id*=\"apex\"]');\n      clonedCharts.forEach((chart, index) => {\n        // Remove any duplicate or extra grid lines that might have been created\n        const gridLines = chart.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');\n        const seenLines = new Set();\n        gridLines.forEach(line => {\n          const lineKey = `${line.getAttribute('x1')}-${line.getAttribute('y1')}-${line.getAttribute('x2')}-${line.getAttribute('y2')}`;\n          if (seenLines.has(lineKey)) {\n            // Duplicate line, remove it\n            line.remove();\n          } else {\n            seenLines.add(lineKey);\n          }\n        });\n\n        // Ensure chart maintains its frozen dimensions\n        chart.style.minHeight = 'auto';\n        chart.style.maxHeight = 'none';\n      });\n\n      // Additional safety check: fix any legends that might still be mispositioned in cloned content\n      const clonedLegends = clonedContent.querySelectorAll('.apexcharts-legend');\n      clonedLegends.forEach((legend, index) => {\n        // If legend still has a very high top value, reset it\n        const topValue = parseFloat(legend.style.top) || 0;\n        if (topValue > 500) {\n          console.log(`Fixing mispositioned cloned legend ${index} with top: ${topValue}px`);\n          legend.style.top = '20px';\n          legend.style.position = 'relative';\n          legend.style.margin = '20px auto 10px auto';\n          legend.style.display = 'flex';\n          legend.style.justifyContent = 'center';\n        }\n      });\n\n      // Find all components and modify them for proper header repetition\n      const components = clonedContent.querySelectorAll('.min-h-screen');\n      components.forEach(component => {\n        const componentHeader = component.querySelector('.component-header');\n        const reportHeader = component.querySelector('.report-header');\n        const metricGrid = component.querySelector(\".metrics-flex\");\n        let header = componentHeader || reportHeader;\n        if (header) {\n          if (!header.classList.contains('component-header')) {\n            header.classList.add('component-header');\n          }\n          const allChildren = Array.from(component.children);\n          let nonHeaderChildren;\n          if (componentHeader) {\n            nonHeaderChildren = allChildren.filter(child => !child.classList.contains('component-header'));\n          } else if (reportHeader) {\n            nonHeaderChildren = allChildren.filter(child => !child.classList.contains('report-header'));\n          }\n          if (nonHeaderChildren && nonHeaderChildren.length > 0) {\n            const contentGroup = document.createElement('div');\n            contentGroup.className = 'component-content';\n            nonHeaderChildren.forEach(child => {\n              contentGroup.appendChild(child);\n            });\n            component.appendChild(contentGroup);\n          }\n          component.style.display = 'table';\n          component.style.width = '100%';\n        }\n      });\n\n      // Create HTML with minimal CSS changes\n      const htmlContent = `\n      <!DOCTYPE html>\n      <html lang=\"en\">\n      <head>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Custom Report</title>\n        <script src=\"https://cdn.tailwindcss.com\"></script>\n        <link href=\"https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap\" rel=\"stylesheet\">\n        <style>\n          @page {\n            size: A4 portrait;\n            margin-top: 1in;\n            margin-bottom : 1in;\n            margin-left : 0in;\n            margin-right : 0 in;\n          }\n\n          body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            line-height: 1.6;\n            color: #374151;\n            background: white;\n          }\n\n          .page-break {\n            page-break-before: always;\n          }\n\n          .no-break {\n            page-break-inside: avoid;\n          }\n\n          /* Minimal styles - rely on frozen positions */\n          .border-b-4.border-blue-900 {\n            border-bottom: 4px solid #1e3a8a !important;\n            page-break-inside: avoid;\n            display: table-header-group;\n          }\n\n          .report-header {\n            page-break-inside: avoid;\n          }\n\n          .component-header {\n            display: flex !important;\n            justify-content: space-between !important;\n            align-items: center !important;\n            page-break-inside: avoid;\n          }\n\n          .metrics-flex{\n            display: flex !important;\n            justify-content: space-between !important;\n            align-items: center !important;\n          }\n          \n          .metrics-flex > * {\n            flex: 1 1 0% !important;\n            text-align: center;\n          }\n\n          .component-content {\n            display: table-row-group;\n          }\n\n          .min-h-screen {\n            display: table !important;\n            width: 100% !important;\n            min-height: auto !important;\n            page-break-inside: avoid;\n          }\n\n          .min-h-screen:not(:first-child) {\n            page-break-before: always;\n          }\n\n          /* Trust the frozen chart and legend positions */\n          div[id*=\"apex\"] {\n            page-break-inside: avoid;\n          }\n\n          .apexcharts-legend {\n            page-break-inside: avoid;\n          }\n\n          .text-teal-600 {\n            color: #0d9488 !important;\n          }\n\n          .bg-gray-200 {\n            background-color: #ffffff !important;\n          }\n\n          .bg-white {\n            background-color: #ffffff !important;\n          }\n\n          .overflow-y-auto {\n            overflow: visible !important;\n          }\n        </style>\n      </head>\n      <body>\n        <div class=\"report-container\">\n          ${clonedContent.innerHTML}\n        </div>\n      </body>\n      </html>\n    `;\n      console.log(\"Html content prepared for PDF generation with frozen charts and legends\", htmlContent);\n\n      // Generate PDF using the HTML content\n      const {\n        generatePDFFromHTML\n      } = await import('../../services/pdf');\n      const response = await generatePDFFromHTML(htmlContent, {\n        format: 'A4',\n        orientation: 'portrait',\n        printBackground: true,\n        margin: {\n          top: '0in',\n          right: '0in',\n          bottom: '0in',\n          left: '0in'\n        }\n      });\n      if (response.success && response.data.pdf) {\n        const filename = `Custom_Report_${new Date().toISOString().split('T')[0]}.pdf`;\n        const downloadSuccess = downloadPDFFromBase64(response.data.pdf, filename);\n        if (downloadSuccess) {\n          setSuccessMessage('PDF downloaded successfully!');\n          setShowSuccess(true);\n        } else {\n          throw new Error('Failed to download PDF');\n        }\n      } else {\n        throw new Error(response.message || 'Failed to generate PDF');\n      }\n    } catch (error) {\n      console.error('PDF Generation Error:', error);\n      setSuccessMessage('Failed to generate PDF. Please try again.');\n      setShowSuccess(true);\n    } finally {\n      setIsGeneratingPDF(false);\n    }\n  };\n\n  // Initialize data on component mount\n  useEffect(() => {\n    initializeSettings();\n    fetchReportData();\n    fetchContentSettings(); // NEW: Fetch content settings\n  }, [companyId]); // Add companyId as dependency\n\n  const handleBackToDashboard = () => {\n    navigate(`/company/${companyId}`, {\n      state: {\n        activeTab: 'reports'\n      }\n    });\n  };\n  const handleSettingChange = (section, property, value) => {\n    // Apply font size constraints\n    if (property === 'fontSize') {\n      const constraints = fontSizeConstraints[section];\n      if (constraints) {\n        value = Math.max(constraints.min, Math.min(constraints.max, value));\n      }\n    }\n    setTemplateSettings(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [property]: value\n      }\n    }));\n  };\n  const handleCloseSuccess = () => {\n    setShowSuccess(false);\n  };\n\n  // Save settings to API\n  const saveSettingsToAPI = async () => {\n    try {\n      setIsSavingSettings(true);\n      const payload = {\n        settings: templateSettings\n      };\n      const response = await axiosInstance.put('/template-settings', payload);\n      if (response.data && response.data.success) {\n        // Update localStorage with the saved settings\n        localStorage.setItem('templateSettings', JSON.stringify(templateSettings));\n\n        // Update initial settings to current settings\n        setInitialSettings(templateSettings);\n        setSuccessMessage('Settings saved successfully and applied globally!');\n        setShowSuccess(true);\n        console.log('Settings saved successfully:', response.data);\n      } else {\n        throw new Error('Failed to save template settings');\n      }\n    } catch (error) {\n      console.error('Error saving template settings:', error);\n      setSuccessMessage('Failed to save settings. Please try again.');\n      setShowSuccess(true);\n    } finally {\n      setIsSavingSettings(false);\n    }\n  };\n\n  // Button handlers\n  const handleSave = () => {\n    setShowSaveConfirmModal(true);\n  };\n  const handleConfirmSave = async () => {\n    setShowSaveConfirmModal(false);\n    await saveSettingsToAPI();\n  };\n  const handleCancelSave = () => {\n    setShowSaveConfirmModal(false);\n  };\n  const handleResetToDefault = () => {\n    setShowResetConfirmModal(true);\n  };\n  const handleConfirmReset = () => {\n    try {\n      // Reset to initial settings loaded from API/localStorage\n      if (initialSettings) {\n        setTemplateSettings(initialSettings);\n        setSelectedTemplate('Deepsight');\n\n        // Update localStorage with initial settings\n        localStorage.setItem('templateSettings', JSON.stringify(initialSettings));\n        setSuccessMessage('Settings reset to initial values successfully!');\n        setShowSuccess(true);\n      }\n    } catch (error) {\n      console.error('Error resetting settings:', error);\n      setSuccessMessage('Error resetting settings. Please try again.');\n      setShowSuccess(true);\n    }\n    setShowResetConfirmModal(false);\n  };\n  const handleCancelReset = () => {\n    setShowResetConfirmModal(false);\n  };\n  const handleResync = () => {\n    fetchReportData();\n    fetchContentSettings(); // Also resync content settings\n    setSuccessMessage('Data resynced successfully!');\n    setShowSuccess(true);\n  };\n\n  // Convert font type to CSS font-weight\n  const getFontWeight = fontType => {\n    const weights = {\n      'Regular': '400',\n      'Bold': '700'\n    };\n    return weights[fontType] || '400';\n  };\n  const getHeaderStyle = () => {\n    var _templateSettings$hea, _templateSettings$hea2, _templateSettings$hea3, _templateSettings$hea4;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea = templateSettings.header) === null || _templateSettings$hea === void 0 ? void 0 : _templateSettings$hea.fontStyle) || 'Helvetica',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea2 = templateSettings.header) === null || _templateSettings$hea2 === void 0 ? void 0 : _templateSettings$hea2.fontType) || 'Bold'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea3 = templateSettings.header) === null || _templateSettings$hea3 === void 0 ? void 0 : _templateSettings$hea3.fontSize) || 44}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea4 = templateSettings.header) === null || _templateSettings$hea4 === void 0 ? void 0 : _templateSettings$hea4.color) || '#1e7c8c',\n      borderRadius: '8px 8px 0 0',\n      margin: '0'\n    };\n  };\n  const getHeadingStyle = () => {\n    var _templateSettings$hea5, _templateSettings$hea6, _templateSettings$hea7, _templateSettings$hea8;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea5 = templateSettings.heading) === null || _templateSettings$hea5 === void 0 ? void 0 : _templateSettings$hea5.fontStyle) || 'Helvetica',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea6 = templateSettings.heading) === null || _templateSettings$hea6 === void 0 ? void 0 : _templateSettings$hea6.fontType) || 'Bold'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea7 = templateSettings.heading) === null || _templateSettings$hea7 === void 0 ? void 0 : _templateSettings$hea7.fontSize) || 36}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$hea8 = templateSettings.heading) === null || _templateSettings$hea8 === void 0 ? void 0 : _templateSettings$hea8.color) || '#1e7c8c'\n    };\n  };\n  const getContentStyle = () => {\n    var _templateSettings$con, _templateSettings$con2, _templateSettings$con3, _templateSettings$con4;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$con = templateSettings.content) === null || _templateSettings$con === void 0 ? void 0 : _templateSettings$con.fontStyle) || 'Helvetica',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$con2 = templateSettings.content) === null || _templateSettings$con2 === void 0 ? void 0 : _templateSettings$con2.fontType) || 'Regular'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$con3 = templateSettings.content) === null || _templateSettings$con3 === void 0 ? void 0 : _templateSettings$con3.fontSize) || 15}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$con4 = templateSettings.content) === null || _templateSettings$con4 === void 0 ? void 0 : _templateSettings$con4.color) || '#333333',\n      lineHeight: '1.6',\n      margin: '0'\n    };\n  };\n  const getSubHeadingStyle = () => {\n    var _templateSettings$sub, _templateSettings$sub2, _templateSettings$sub3, _templateSettings$sub4;\n    return {\n      fontFamily: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$sub = templateSettings.subHeading) === null || _templateSettings$sub === void 0 ? void 0 : _templateSettings$sub.fontStyle) || 'Helvetica',\n      fontWeight: getFontWeight((templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$sub2 = templateSettings.subHeading) === null || _templateSettings$sub2 === void 0 ? void 0 : _templateSettings$sub2.fontType) || 'Bold'),\n      fontSize: `${(templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$sub3 = templateSettings.subHeading) === null || _templateSettings$sub3 === void 0 ? void 0 : _templateSettings$sub3.fontSize) || 22}px`,\n      color: (templateSettings === null || templateSettings === void 0 ? void 0 : (_templateSettings$sub4 = templateSettings.subHeading) === null || _templateSettings$sub4 === void 0 ? void 0 : _templateSettings$sub4.color) || '#1e7c8c',\n      padding: '0'\n    };\n  };\n\n  // Show loading state while fetching initial settings or content settings\n  if (isLoadingSettings || isLoadingContentSettings || !templateSettings) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 40\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 842,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 text-lg text-gray-600\",\n          children: \"Loading settings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 841,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 840,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Snackbar, {\n      open: showSuccess,\n      autoHideDuration: 4000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'top',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: dataError || settingsError || contentSettingsError ? \"error\" : \"success\",\n        variant: \"filled\",\n        sx: {\n          backgroundColor: dataError || settingsError || contentSettingsError ? '#d32f2f' : '#1976d2',\n          '& .MuiAlert-icon': {\n            color: 'white'\n          }\n        },\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 858,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showSaveConfirmModal,\n      onClose: handleCancelSave,\n      \"aria-labelledby\": \"save-dialog-title\",\n      \"aria-describedby\": \"save-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"save-dialog-title\",\n        children: \"Confirm Save Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"save-dialog-description\",\n          children: \"Are you sure you want to save these template settings? These settings will be applied globally and will affect all future reports.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 884,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 883,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCancelSave,\n          color: \"primary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmSave,\n          color: \"primary\",\n          variant: \"contained\",\n          disabled: isSavingSettings,\n          children: isSavingSettings ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16,\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 17\n            }, this), \"Saving...\"]\n          }, void 0, true) : 'Save Settings'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 888,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 874,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showResetConfirmModal,\n      onClose: handleCancelReset,\n      \"aria-labelledby\": \"reset-dialog-title\",\n      \"aria-describedby\": \"reset-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"reset-dialog-title\",\n        children: \"Confirm Reset Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"reset-dialog-description\",\n          children: \"Are you sure you want to reset the template settings? This will restore the settings to their initial values when you first loaded this page. Any unsaved changes will be lost.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCancelReset,\n          color: \"primary\",\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmReset,\n          color: \"primary\",\n          variant: \"contained\",\n          children: \"Reset Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 929,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 911,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-96 flex-shrink-0 h-screen flex flex-col fixed left-0 top-0 z-10 bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-[14px] border-b bg-gray-50 flex items-center shadow\",\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Back to Dashboard\",\n          placement: \"bottom\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleBackToDashboard,\n            sx: {\n              color: 'rgb(75, 85, 99)',\n              padding: '6px',\n              marginRight: '12px',\n              '&:hover': {\n                backgroundColor: 'rgba(75, 85, 99, 0.1)'\n              },\n              '&:focus': {\n                outline: 'none'\n              },\n              transition: 'all 0.2s'\n            },\n            children: /*#__PURE__*/_jsxDEV(ArrowBack, {\n              fontSize: \"medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 938,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Customize Template\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 957,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 space-y-6 overflow-y-auto flex-1 shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-gray-700 mb-3\",\n            children: \"Header\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-xs text-gray-600 mb-1\",\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: templateSettings.header.fontStyle,\n              onChange: e => handleSettingChange('header', 'fontStyle', e.target.value),\n              className: \"w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: font,\n                children: font\n              }, font, false, {\n                fileName: _jsxFileName,\n                lineNumber: 973,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 967,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: templateSettings.header.fontType,\n                onChange: e => handleSettingChange('header', 'fontType', e.target.value),\n                className: \"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: type,\n                  children: type\n                }, type, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 979,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: [\"Font Size (\", fontSizeConstraints.header.min, \"-\", fontSizeConstraints.header.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: fontSizeConstraints.header.min,\n                max: fontSizeConstraints.header.max,\n                value: templateSettings.header.fontSize,\n                onChange: e => handleSettingChange('header', 'fontSize', parseInt(e.target.value)),\n                className: \"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 996,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: templateSettings.header.color,\n                onChange: e => handleSettingChange('header', 'color', e.target.value),\n                className: \"w-full border border-gray-300  h-9 rounded cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 978,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-gray-700 mb-3\",\n            children: \"Heading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-xs text-gray-600 mb-1\",\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1023,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: templateSettings.heading.fontStyle,\n              onChange: e => handleSettingChange('heading', 'fontStyle', e.target.value),\n              className: \"w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: font,\n                children: font\n              }, font, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1030,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1024,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: templateSettings.heading.fontType,\n                onChange: e => handleSettingChange('heading', 'fontType', e.target.value),\n                className: \"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: type,\n                  children: type\n                }, type, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: [\"Font Size (\", fontSizeConstraints.heading.min, \"-\", fontSizeConstraints.heading.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1050,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: fontSizeConstraints.heading.min,\n                max: fontSizeConstraints.heading.max,\n                value: templateSettings.heading.fontSize,\n                onChange: e => handleSettingChange('heading', 'fontSize', parseInt(e.target.value)),\n                className: \"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1064,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: templateSettings.heading.color,\n                onChange: e => handleSettingChange('heading', 'color', e.target.value),\n                className: \"w-full border border-gray-300 h-9 rounded cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1063,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-gray-700 mb-3\",\n            children: \"Sub-Heading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1077,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-xs text-gray-600 mb-1\",\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: templateSettings.subHeading.fontStyle,\n              onChange: e => handleSettingChange('subHeading', 'fontStyle', e.target.value),\n              className: \"w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: font,\n                children: font\n              }, font, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1087,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1081,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1079,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1094,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: templateSettings.subHeading.fontType,\n                onChange: e => handleSettingChange('subHeading', 'fontType', e.target.value),\n                className: \"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: type,\n                  children: type\n                }, type, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1101,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1095,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1093,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: [\"Font Size (\", fontSizeConstraints.subHeading.min, \"-\", fontSizeConstraints.subHeading.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: fontSizeConstraints.subHeading.min,\n                max: fontSizeConstraints.subHeading.max,\n                value: templateSettings.subHeading.fontSize,\n                onChange: e => handleSettingChange('subHeading', 'fontSize', parseInt(e.target.value)),\n                className: \"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1121,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: templateSettings.subHeading.color,\n                onChange: e => handleSettingChange('subHeading', 'color', e.target.value),\n                className: \"w-full border border-gray-300 h-9 rounded cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1092,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1076,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-gray-700 mb-3\",\n            children: \"Content\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-xs text-gray-600 mb-1\",\n              children: \"Font Style\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: templateSettings.content.fontStyle,\n              onChange: e => handleSettingChange('content', 'fontStyle', e.target.value),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: fontStyles.map(font => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: font,\n                children: font\n              }, font, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-3 gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: \"Font Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: templateSettings.content.fontType,\n                onChange: e => handleSettingChange('content', 'fontType', e.target.value),\n                className: \"w-full px-2 py-2 border border-gray-300  rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: fontTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: type,\n                  children: type\n                }, type, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1158,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: [\"Font Size (\", fontSizeConstraints.content.min, \"-\", fontSizeConstraints.content.max, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                min: fontSizeConstraints.content.min,\n                max: fontSizeConstraints.content.max,\n                value: templateSettings.content.fontSize,\n                onChange: e => handleSettingChange('content', 'fontSize', parseInt(e.target.value)),\n                className: \"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs text-gray-600 mb-1\",\n                children: \"Color\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"color\",\n                value: templateSettings.content.color,\n                onChange: e => handleSettingChange('content', 'color', e.target.value),\n                className: \"w-full border border-gray-300 h-9 rounded cursor-pointer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1179,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-7 border-t border-gray-300\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSave,\n              disabled: isSavingSettings,\n              className: \"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-neutral-800\",\n              children: isSavingSettings ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 14,\n                  sx: {\n                    mr: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 21\n                }, this), \"SAVING...\"]\n              }, void 0, true) : 'SAVE'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleResetToDefault,\n              className: \"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-neutral-800\",\n              children: \"RESET\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1207,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 960,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 936,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col flex-1 h-screen\",\n      style: {\n        marginLeft: '384px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-3 flex items-center bg-gray-50 justify-end fixed top-0 z-5 shadow\",\n        style: {\n          left: '384px',\n          right: '0'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-end space-x-3\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: isLoadingData ? \"Loading data...\" : \"Resync data\",\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: handleResync,\n                  disabled: isLoadingData,\n                  sx: {\n                    color: isLoadingData ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',\n                    '&:hover': {\n                      backgroundColor: 'rgba(156, 163, 175, 0.1)'\n                    },\n                    '&:focus': {\n                      outline: 'none'\n                    },\n                    '&:disabled': {\n                      color: 'rgba(156, 163, 175, 0.6)',\n                      cursor: 'not-allowed'\n                    },\n                    transition: 'all 0.2s',\n                    padding: '8px'\n                  },\n                  children: isLoadingData ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 16,\n                    color: \"inherit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1248,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(SyncIcon, {\n                    fontSize: \"medium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1250,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1228,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1227,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: isGeneratingPDF ? \"Generating PDF...\" : \"Download PDF\",\n              placement: \"top\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  onClick: handleDownloadPDF,\n                  disabled: isGeneratingPDF || isLoadingData,\n                  sx: {\n                    color: isGeneratingPDF || isLoadingData ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',\n                    '&:hover': {\n                      backgroundColor: 'rgba(156, 163, 175, 0.1)'\n                    },\n                    '&:focus': {\n                      outline: 'none'\n                    },\n                    '&:disabled': {\n                      color: 'rgba(156, 163, 175, 0.6)',\n                      cursor: 'not-allowed'\n                    },\n                    transition: 'all 0.2s',\n                    padding: '8px'\n                  },\n                  children: isGeneratingPDF ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 16,\n                    color: \"inherit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1278,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(DownloadIcon, {\n                    fontSize: \"medium\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1280,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1258,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1224,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1221,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto bg-gray-200\",\n        style: {\n          marginTop: '60px',\n          width: '100%'\n        },\n        children: isLoadingData ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 40\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 text-lg text-gray-600\",\n              children: \"Loading report data...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1298,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1296,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1295,\n          columnNumber: 13\n        }, this) : dataError ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-64\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg text-red-600 mb-4\",\n              children: \"Failed to load report data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1304,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: fetchReportData,\n              className: \"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n              children: \"Retry\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1305,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1303,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1302,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(DeepSightCoverPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1316,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableOfContents, {\n            headingTextStyle: getHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            subHeadingTextStyle: getSubHeadingStyle()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1318,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ReportSummary, {\n            headerTextStyle: getHeaderStyle(),\n            headingTextStyle: getHeadingStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1323,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FiscalYearDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            fiscalData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1329,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ExpenseSummaryDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData,\n            contentSettings: contentSettings\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1336,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(OperationalEfficiencyDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            operationalData: reportData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1343,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LiquiditySummaryDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            liquidityData: reportData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1349,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ProfitLoss13MonthDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1355,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ProfitLossMonthlyDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1361,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ProfitLossYTDDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1368,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(BalanceSheetDashboard, {\n            headerTextStyle: getHeaderStyle(),\n            subHeadingTextStyle: getSubHeadingStyle(),\n            contentTextStyle: getContentStyle(),\n            reportData: reportData\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1374,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1219,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 850,\n    columnNumber: 5\n  }, this);\n};\n_s(CustomizeTemplateWithPreview, \"L5y9+ay2zbObIVe3f47lrGh6+WA=\", false, function () {\n  return [useSearchParams, useParams, useNavigate];\n});\n_c = CustomizeTemplateWithPreview;\nexport default CustomizeTemplateWithPreview;\nvar _c;\n$RefreshReg$(_c, \"CustomizeTemplateWithPreview\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "IconButton", "<PERSON><PERSON><PERSON>", "CircularProgress", "Box", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "Sync", "SyncIcon", "ArrowBack", "Download", "DownloadIcon", "Snackbar", "<PERSON><PERSON>", "useSearchParams", "TableOfContents", "ReportSummary", "FiscalYearDashboard", "ExpenseSummaryDashboard", "OperationalEfficiencyDashboard", "LiquiditySummaryDashboard", "ProfitLoss13MonthDashboard", "ProfitLossMonthlyDashboard", "ProfitLossYTDDashboard", "BalanceSheetDashboard", "useNavigate", "useParams", "axiosInstance", "downloadPDFFromBase64", "DeepSightCoverPage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomizeTemplateWithPreview", "_s", "searchParams", "params", "companyId", "id", "reportData", "setReportData", "isLoadingData", "setIsLoadingData", "dataError", "setDataError", "templateSettings", "setTemplateSettings", "initialSettings", "setInitialSettings", "isLoadingSettings", "setIsLoadingSettings", "settingsError", "setSettingsError", "contentSettings", "setContentSettings", "isLoadingContentSettings", "setIsLoadingContentSettings", "contentSettingsError", "setContentSettingsError", "showSuccess", "setShowSuccess", "successMessage", "setSuccessMessage", "selectedTemplate", "setSelectedTemplate", "isGeneratingPDF", "setIsGeneratingPDF", "isSavingSettings", "setIsSavingSettings", "showSaveConfirmModal", "setShowSaveConfirmModal", "showResetConfirmModal", "setShowResetConfirmModal", "navigate", "defaultSettings", "header", "fontStyle", "fontType", "fontSize", "color", "heading", "subHeading", "content", "fontStyles", "fontTypes", "fontSizeConstraints", "min", "max", "fetchContentSettings", "response", "get", "data", "success", "settingsData", "console", "log", "Error", "error", "message", "defaultContentSettings", "chartSettings", "incomeSummary", "netIncome", "grossProfitMargin", "netProfitMargin", "roaAndRoe", "fetchTemplateSettings", "settings", "localStorage", "setItem", "JSON", "stringify", "savedSettings", "getItem", "fallbackSettings", "parse", "initializeSettings", "urlSettings", "parsedSettings", "fetchReportData", "handleDownloadPDF", "contentPanel", "document", "querySelector", "Promise", "resolve", "setTimeout", "freezeChartsAndLegends", "container", "chartContainers", "querySelectorAll", "length", "for<PERSON>ach", "chartContainer", "index", "svgElements", "svgEl", "computedStyle", "window", "getComputedStyle", "tagName", "toLowerCase", "style", "width", "height", "overflow", "stroke", "strokeWidth", "fill", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transform", "containerRect", "getBoundingClientRect", "position", "apexElements", "element", "top", "left", "right", "bottom", "display", "visibility", "opacity", "gridLines", "line", "axisLines", "seriesElements", "legends", "legend", "rect", "closest", "parentRect", "relativeTop", "relativeLeft", "setAttribute", "Math", "zIndex", "containerPosition", "legendElements", "el", "fontFamily", "fontWeight", "warn", "margin", "justifyContent", "cloned<PERSON><PERSON>nt", "cloneNode", "cloned<PERSON>harts", "chart", "seenLines", "Set", "lineKey", "getAttribute", "has", "remove", "add", "minHeight", "maxHeight", "clonedLegends", "topValue", "parseFloat", "components", "component", "componentHeader", "reportHeader", "metricGrid", "classList", "contains", "allChildren", "Array", "from", "children", "nonHeaderChildren", "filter", "child", "contentGroup", "createElement", "className", "append<PERSON><PERSON><PERSON>", "htmlContent", "innerHTML", "generatePDFFromHTML", "format", "orientation", "printBackground", "pdf", "filename", "Date", "toISOString", "split", "downloadSuccess", "handleBackToDashboard", "state", "activeTab", "handleSettingChange", "section", "property", "value", "constraints", "prev", "handleCloseSuccess", "saveSettingsToAPI", "payload", "put", "handleSave", "handleConfirmSave", "handleCancelSave", "handleResetToDefault", "handleConfirmReset", "handleCancelReset", "handleResync", "getFontWeight", "weights", "getHeaderStyle", "_templateSettings$hea", "_templateSettings$hea2", "_templateSettings$hea3", "_templateSettings$hea4", "borderRadius", "getHeadingStyle", "_templateSettings$hea5", "_templateSettings$hea6", "_templateSettings$hea7", "_templateSettings$hea8", "getContentStyle", "_templateSettings$con", "_templateSettings$con2", "_templateSettings$con3", "_templateSettings$con4", "lineHeight", "getSubHeadingStyle", "_templateSettings$sub", "_templateSettings$sub2", "_templateSettings$sub3", "_templateSettings$sub4", "padding", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "variant", "sx", "backgroundColor", "onClick", "disabled", "mr", "title", "placement", "marginRight", "outline", "transition", "onChange", "e", "target", "map", "font", "type", "parseInt", "marginLeft", "alignItems", "gap", "cursor", "marginTop", "headingTextStyle", "contentTextStyle", "subHeadingTextStyle", "headerTextStyle", "fiscalData", "operationalData", "liquidityData", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/JASON_NEW/valisight_noGit/frontend/src/pages/reports/CustomizeReport.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { IconButton, Tooltip, CircularProgress, Box, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Button } from '@mui/material';\r\nimport { Sync as SyncIcon, ArrowBack, Download as DownloadIcon } from '@mui/icons-material';\r\nimport { Snackbar, Alert } from '@mui/material';\r\nimport { useSearchParams } from 'react-router-dom';\r\nimport TableOfContents from './ReportPages/TableOfContents';\r\nimport ReportSummary from './ReportPages/ReportSummary';\r\nimport FiscalYearDashboard from './ReportPages/FiscalYear';\r\nimport ExpenseSummaryDashboard from './ReportPages/ExpenseSummary';\r\nimport OperationalEfficiencyDashboard from './ReportPages/OperationalEfficiency';\r\nimport LiquiditySummaryDashboard from './ReportPages/LiquiditySummary';\r\nimport ProfitLoss13MonthDashboard from './ReportPages/MonthTrailing';\r\nimport ProfitLossMonthlyDashboard from './ReportPages/Monthly';\r\nimport ProfitLossYTDDashboard from './ReportPages/YearToDate';\r\nimport BalanceSheetDashboard from './ReportPages/BalanceSheet';\r\nimport { useNavigate, useParams } from 'react-router-dom';\r\nimport axiosInstance from '../../services/axiosInstance';\r\nimport { downloadPDFFromBase64 } from '../../services/pdf';\r\nimport DeepSightCoverPage from './ReportPages/CoverPage';\r\n\r\n\r\n\r\nconst CustomizeTemplateWithPreview = () => {\r\n  const [searchParams] = useSearchParams();\r\n\r\n  const params = useParams();\r\n  const companyId = params.id;\r\n\r\n  // API Data State\r\n  const [reportData, setReportData] = useState(null);\r\n  const [isLoadingData, setIsLoadingData] = useState(true);\r\n  const [dataError, setDataError] = useState(null);\r\n\r\n  // Template Settings State\r\n  const [templateSettings, setTemplateSettings] = useState(null);\r\n  const [initialSettings, setInitialSettings] = useState(null); // Store initial settings for reset\r\n  const [isLoadingSettings, setIsLoadingSettings] = useState(true);\r\n  const [settingsError, setSettingsError] = useState(null);\r\n\r\n  // Content Settings State (NEW)\r\n  const [contentSettings, setContentSettings] = useState(null);\r\n  const [isLoadingContentSettings, setIsLoadingContentSettings] = useState(true);\r\n  const [contentSettingsError, setContentSettingsError] = useState(null);\r\n\r\n  // UI State\r\n  const [showSuccess, setShowSuccess] = useState(false);\r\n  const [successMessage, setSuccessMessage] = useState('');\r\n  const [selectedTemplate, setSelectedTemplate] = useState('Deepsight');\r\n  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);\r\n  const [isSavingSettings, setIsSavingSettings] = useState(false);\r\n\r\n  // Modal State\r\n  const [showSaveConfirmModal, setShowSaveConfirmModal] = useState(false);\r\n  const [showResetConfirmModal, setShowResetConfirmModal] = useState(false);\r\n\r\n  const navigate = useNavigate();\r\n\r\n  // Default settings fallback\r\n  const defaultSettings = {\r\n    header: {\r\n      fontStyle: 'Helvetica',\r\n      fontType: 'Bold',\r\n      fontSize: 44,\r\n      color: '#1e7c8c'\r\n    },\r\n    heading: {\r\n      fontStyle: 'Helvetica',\r\n      fontType: 'Bold',\r\n      fontSize: 36,\r\n      color: '#1e7c8c'\r\n    },\r\n    subHeading: {\r\n      fontStyle: 'Helvetica',\r\n      fontType: 'Bold',\r\n      fontSize: 22,\r\n      color: '#1e7c8c'\r\n    },\r\n    content: {\r\n      fontStyle: 'Helvetica',\r\n      fontType: 'Regular',\r\n      fontSize: 15,\r\n      color: '#333333'\r\n    }\r\n  };\r\n\r\n  const fontStyles = [\"Open Sans\",'Calibri', 'Arial', 'Times New Roman', 'Georgia', 'Verdana', 'Helvetica'];\r\n  const fontTypes = ['Regular', 'Bold'];\r\n\r\n  // Font size constraints\r\n  const fontSizeConstraints = {\r\n    header: { min: 32, max: 48 },\r\n    heading: { min: 32, max: 42 },\r\n    subHeading: { min: 18, max: 28 },\r\n    content: { min: 9, max: 20 }\r\n  };\r\n\r\n  // Fetch content settings from API (NEW)\r\n  const fetchContentSettings = async () => {\r\n    try {\r\n      setIsLoadingContentSettings(true);\r\n      setContentSettingsError(null);\r\n\r\n      const response = await axiosInstance.get(`/content-settings/company/${companyId}/DEEPSIGHT`);\r\n\r\n      if (response.data && response.data.success) {\r\n        const settingsData = response.data.data;\r\n        console.log('Content settings loaded from API:', settingsData);\r\n        setContentSettings(settingsData);\r\n      } else {\r\n        throw new Error('Failed to fetch content settings');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching content settings:', error);\r\n      setContentSettingsError(error.message);\r\n\r\n      // Set default content settings if API fails\r\n      const defaultContentSettings = {\r\n        chartSettings: {\r\n          incomeSummary: true,\r\n          netIncome: true,\r\n          grossProfitMargin: true,\r\n          netProfitMargin: true,\r\n          roaAndRoe: true,\r\n        }\r\n      };\r\n      setContentSettings(defaultContentSettings);\r\n\r\n      setSuccessMessage('Using default chart settings. Failed to load from server.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsLoadingContentSettings(false);\r\n    }\r\n  };\r\n\r\n  // Fetch template settings from API\r\n  const fetchTemplateSettings = async () => {\r\n    try {\r\n      setIsLoadingSettings(true);\r\n      setSettingsError(null);\r\n\r\n      const response = await axiosInstance.get('/template-settings');\r\n\r\n      if (response.data && response.data.success) {\r\n        const settingsData = response.data.data.settings;\r\n\r\n        // Store settings in localStorage\r\n        localStorage.setItem('templateSettings', JSON.stringify(settingsData));\r\n\r\n        // Set both current and initial settings\r\n        setTemplateSettings(settingsData);\r\n        setInitialSettings(settingsData);\r\n\r\n        console.log('Template settings loaded from API:', response.data.data);\r\n      } else {\r\n        throw new Error('Failed to fetch template settings');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching template settings:', error);\r\n      setSettingsError(error.message);\r\n\r\n      // Fallback to localStorage or default settings\r\n      const savedSettings = localStorage.getItem('templateSettings');\r\n      const fallbackSettings = savedSettings ? JSON.parse(savedSettings) : defaultSettings;\r\n\r\n      setTemplateSettings(fallbackSettings);\r\n      setInitialSettings(fallbackSettings);\r\n\r\n      setSuccessMessage('Using cached settings. Failed to load from server.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsLoadingSettings(false);\r\n    }\r\n  };\r\n\r\n  // Load settings from URL params (for PDF generation) or fetch from API\r\n  const initializeSettings = () => {\r\n    try {\r\n      // First check if settings are in URL (for PDF generation)\r\n      const urlSettings = searchParams.get('templateSettings');\r\n      if (urlSettings) {\r\n        const parsedSettings = JSON.parse(urlSettings);\r\n        setTemplateSettings(parsedSettings);\r\n        setInitialSettings(parsedSettings);\r\n        setIsLoadingSettings(false);\r\n        return;\r\n      }\r\n\r\n      // Otherwise fetch from API\r\n      fetchTemplateSettings();\r\n    } catch (error) {\r\n      console.error('Error initializing settings:', error);\r\n      setTemplateSettings(defaultSettings);\r\n      setInitialSettings(defaultSettings);\r\n      setIsLoadingSettings(false);\r\n    }\r\n  };\r\n\r\n  // Fetch report data from API\r\n  const fetchReportData = async () => {\r\n    try {\r\n      setIsLoadingData(true);\r\n      setDataError(null);\r\n\r\n      const response = await axiosInstance.get('/report/1/generate-calculation');\r\n\r\n      if (response.data && response.data.success) {\r\n        console.log('CustomizeReport - API Response:', response.data.data);\r\n        setReportData(response.data.data);\r\n      } else {\r\n        throw new Error('Failed to fetch report data');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching report data:', error);\r\n      setDataError(error.message);\r\n      setSuccessMessage('Failed to load report data. Please try again.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsLoadingData(false);\r\n    }\r\n  };\r\n\r\n  // Generate and download PDF\r\n// In CustomizeReport.jsx, update the handleDownloadPDF function\r\n\r\n// In CustomizeReport.jsx, update the handleDownloadPDF function\r\n\r\nconst handleDownloadPDF = async () => {\r\n  try {\r\n    setIsGeneratingPDF(true);\r\n\r\n    // Get the scrollable content panel that contains all components\r\n    const contentPanel = document.querySelector('.flex-1.overflow-y-auto.bg-gray-200');\r\n    if (!contentPanel) {\r\n      throw new Error('Could not find report content to generate PDF');\r\n    }\r\n\r\n    // Wait for charts to fully render before capturing content\r\n    await new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n    // FREEZE CHART ELEMENTS AND LEGENDS BEFORE CLONING\r\n    const freezeChartsAndLegends = (container) => {\r\n      // 1. Freeze Chart Elements\r\n      const chartContainers = container.querySelectorAll('div[id*=\"apex\"]');\r\n      console.log(`Found ${chartContainers.length} charts to freeze`);\r\n      \r\n      chartContainers.forEach((chartContainer, index) => {\r\n        try {\r\n          // Freeze all SVG elements within the chart\r\n          const svgElements = chartContainer.querySelectorAll('svg, svg *');\r\n          svgElements.forEach(svgEl => {\r\n            // Get computed styles and apply them inline\r\n            const computedStyle = window.getComputedStyle(svgEl);\r\n            \r\n            // Apply critical styles inline to prevent changes\r\n            if (svgEl.tagName.toLowerCase() === 'svg') {\r\n              svgEl.style.width = computedStyle.width;\r\n              svgEl.style.height = computedStyle.height;\r\n              svgEl.style.overflow = 'visible';\r\n            }\r\n            \r\n            // Freeze stroke and fill properties\r\n            if (computedStyle.stroke && computedStyle.stroke !== 'none') {\r\n              svgEl.style.stroke = computedStyle.stroke;\r\n            }\r\n            if (computedStyle.strokeWidth && computedStyle.strokeWidth !== '0px') {\r\n              svgEl.style.strokeWidth = computedStyle.strokeWidth;\r\n            }\r\n            if (computedStyle.fill && computedStyle.fill !== 'none') {\r\n              svgEl.style.fill = computedStyle.fill;\r\n            }\r\n            if (computedStyle.strokeDasharray && computedStyle.strokeDasharray !== 'none') {\r\n              svgEl.style.strokeDasharray = computedStyle.strokeDasharray;\r\n            }\r\n            \r\n            // Prevent any transformations from changing\r\n            if (computedStyle.transform && computedStyle.transform !== 'none') {\r\n              svgEl.style.transform = computedStyle.transform;\r\n            }\r\n          });\r\n          \r\n          // Freeze the chart container dimensions and position\r\n          const containerRect = chartContainer.getBoundingClientRect();\r\n          chartContainer.style.width = containerRect.width + 'px';\r\n          chartContainer.style.height = containerRect.height + 'px';\r\n          chartContainer.style.position = 'relative';\r\n          chartContainer.style.overflow = 'visible';\r\n          \r\n          // Freeze all ApexCharts specific elements\r\n          const apexElements = chartContainer.querySelectorAll('[class*=\"apexcharts\"]');\r\n          apexElements.forEach(element => {\r\n            const computedStyle = window.getComputedStyle(element);\r\n            \r\n            // Preserve positioning\r\n            if (computedStyle.position && computedStyle.position !== 'static') {\r\n              element.style.position = computedStyle.position;\r\n              element.style.top = computedStyle.top;\r\n              element.style.left = computedStyle.left;\r\n              element.style.right = computedStyle.right;\r\n              element.style.bottom = computedStyle.bottom;\r\n            }\r\n            \r\n            // Preserve dimensions\r\n            element.style.width = computedStyle.width;\r\n            element.style.height = computedStyle.height;\r\n            \r\n            // Preserve display and visibility\r\n            element.style.display = computedStyle.display;\r\n            element.style.visibility = computedStyle.visibility;\r\n            element.style.opacity = computedStyle.opacity;\r\n          });\r\n          \r\n          // Specifically handle grid lines and axis elements that might be causing extra lines\r\n          const gridLines = chartContainer.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');\r\n          gridLines.forEach(line => {\r\n            const computedStyle = window.getComputedStyle(line);\r\n            line.style.stroke = computedStyle.stroke;\r\n            line.style.strokeWidth = computedStyle.strokeWidth;\r\n            line.style.strokeDasharray = computedStyle.strokeDasharray;\r\n            line.style.opacity = computedStyle.opacity;\r\n          });\r\n          \r\n          // Handle axis lines\r\n          const axisLines = chartContainer.querySelectorAll('.apexcharts-xaxis line, .apexcharts-yaxis line');\r\n          axisLines.forEach(line => {\r\n            const computedStyle = window.getComputedStyle(line);\r\n            line.style.stroke = computedStyle.stroke;\r\n            line.style.strokeWidth = computedStyle.strokeWidth;\r\n            line.style.opacity = computedStyle.opacity;\r\n          });\r\n          \r\n          // Handle data series paths/bars\r\n          const seriesElements = chartContainer.querySelectorAll('.apexcharts-series path, .apexcharts-series rect, .apexcharts-series circle');\r\n          seriesElements.forEach(element => {\r\n            const computedStyle = window.getComputedStyle(element);\r\n            if (computedStyle.fill) element.style.fill = computedStyle.fill;\r\n            if (computedStyle.stroke) element.style.stroke = computedStyle.stroke;\r\n            if (computedStyle.strokeWidth) element.style.strokeWidth = computedStyle.strokeWidth;\r\n            if (computedStyle.opacity) element.style.opacity = computedStyle.opacity;\r\n          });\r\n          \r\n          console.log(`Chart ${index} frozen with dimensions: ${containerRect.width}x${containerRect.height}`);\r\n          \r\n        } catch (error) {\r\n          console.error(`Error freezing chart ${index}:`, error);\r\n        }\r\n      });\r\n      \r\n      // 2. Freeze Legend Elements (your existing legend freezing code)\r\n      const legends = container.querySelectorAll('.apexcharts-legend');\r\n      console.log(`Found ${legends.length} legends to freeze`);\r\n      \r\n      legends.forEach((legend, index) => {\r\n        try {\r\n          // Get current position relative to viewport\r\n          const rect = legend.getBoundingClientRect();\r\n          \r\n          // Find the closest chart container or parent container\r\n          let chartContainer = legend.closest('div[id*=\"apex\"]');\r\n          if (!chartContainer) {\r\n            // Look for chart section container\r\n            chartContainer = legend.closest('.bg-white.p-6');\r\n          }\r\n          if (!chartContainer) {\r\n            // Fallback to component container\r\n            chartContainer = legend.closest('.min-h-screen');\r\n          }\r\n\r\n          if (chartContainer) {\r\n            const parentRect = chartContainer.getBoundingClientRect();\r\n            \r\n            // Calculate position relative to the chart container\r\n            const relativeTop = rect.top - parentRect.top;\r\n            const relativeLeft = rect.left - parentRect.left;\r\n            \r\n            // Store original styles for debugging\r\n            legend.setAttribute('data-original-position', legend.style.position || '');\r\n            legend.setAttribute('data-original-top', legend.style.top || '');\r\n            legend.setAttribute('data-original-left', legend.style.left || '');\r\n            \r\n            // Set the legend to absolute positioning relative to its chart container\r\n            legend.style.position = 'absolute';\r\n            legend.style.top = Math.max(0, relativeTop) + 'px';\r\n            legend.style.left = relativeLeft + 'px';\r\n            legend.style.right = 'auto';\r\n            legend.style.bottom = 'auto';\r\n            legend.style.transform = 'none';\r\n            legend.style.zIndex = '10';\r\n            \r\n            // Ensure the chart container has relative positioning\r\n            const containerPosition = window.getComputedStyle(chartContainer).position;\r\n            if (containerPosition === 'static') {\r\n              chartContainer.style.position = 'relative';\r\n            }\r\n            \r\n            // Freeze legend's internal styling\r\n            const legendElements = legend.querySelectorAll('*');\r\n            legendElements.forEach(el => {\r\n              const computedStyle = window.getComputedStyle(el);\r\n              if (computedStyle.color) el.style.color = computedStyle.color;\r\n              if (computedStyle.fontSize) el.style.fontSize = computedStyle.fontSize;\r\n              if (computedStyle.fontFamily) el.style.fontFamily = computedStyle.fontFamily;\r\n              if (computedStyle.fontWeight) el.style.fontWeight = computedStyle.fontWeight;\r\n            });\r\n            \r\n            console.log(`Legend ${index} frozen at position: ${relativeTop}px, ${relativeLeft}px`);\r\n            \r\n          } else {\r\n            console.warn(`Could not find chart container for legend ${index}`);\r\n            \r\n            // Fallback: just prevent the legend from moving by setting fixed position\r\n            legend.style.position = 'relative';\r\n            legend.style.top = '20px';\r\n            legend.style.left = 'auto';\r\n            legend.style.right = 'auto';\r\n            legend.style.transform = 'none';\r\n            legend.style.margin = '20px auto';\r\n            legend.style.display = 'flex';\r\n            legend.style.justifyContent = 'center';\r\n          }\r\n          \r\n        } catch (error) {\r\n          console.error(`Error freezing legend ${index}:`, error);\r\n          \r\n          // Emergency fallback - just make it static\r\n          legend.style.position = 'static';\r\n          legend.style.margin = '20px auto';\r\n          legend.style.display = 'flex';\r\n          legend.style.justifyContent = 'center';\r\n        }\r\n      });\r\n    };\r\n\r\n    // Apply the freezing function\r\n    freezeChartsAndLegends(contentPanel);\r\n\r\n    // Wait a bit more for positions to settle after freezing\r\n    await new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n    // Now clone the content with frozen chart and legend positions\r\n    const clonedContent = contentPanel.cloneNode(true);\r\n\r\n    // Additional safety check: ensure no extra lines are added during cloning\r\n    const clonedCharts = clonedContent.querySelectorAll('div[id*=\"apex\"]');\r\n    clonedCharts.forEach((chart, index) => {\r\n      // Remove any duplicate or extra grid lines that might have been created\r\n      const gridLines = chart.querySelectorAll('.apexcharts-gridlines-horizontal line, .apexcharts-gridlines-vertical line');\r\n      const seenLines = new Set();\r\n      \r\n      gridLines.forEach(line => {\r\n        const lineKey = `${line.getAttribute('x1')}-${line.getAttribute('y1')}-${line.getAttribute('x2')}-${line.getAttribute('y2')}`;\r\n        if (seenLines.has(lineKey)) {\r\n          // Duplicate line, remove it\r\n          line.remove();\r\n        } else {\r\n          seenLines.add(lineKey);\r\n        }\r\n      });\r\n      \r\n      // Ensure chart maintains its frozen dimensions\r\n      chart.style.minHeight = 'auto';\r\n      chart.style.maxHeight = 'none';\r\n    });\r\n\r\n    // Additional safety check: fix any legends that might still be mispositioned in cloned content\r\n    const clonedLegends = clonedContent.querySelectorAll('.apexcharts-legend');\r\n    clonedLegends.forEach((legend, index) => {\r\n      // If legend still has a very high top value, reset it\r\n      const topValue = parseFloat(legend.style.top) || 0;\r\n      if (topValue > 500) {\r\n        console.log(`Fixing mispositioned cloned legend ${index} with top: ${topValue}px`);\r\n        legend.style.top = '20px';\r\n        legend.style.position = 'relative';\r\n        legend.style.margin = '20px auto 10px auto';\r\n        legend.style.display = 'flex';\r\n        legend.style.justifyContent = 'center';\r\n      }\r\n    });\r\n\r\n    // Find all components and modify them for proper header repetition\r\n    const components = clonedContent.querySelectorAll('.min-h-screen');\r\n    components.forEach((component) => {\r\n      const componentHeader = component.querySelector('.component-header');\r\n      const reportHeader = component.querySelector('.report-header');\r\n      const metricGrid = component.querySelector(\".metrics-flex\");\r\n\r\n      let header = componentHeader || reportHeader;\r\n\r\n      if (header) {\r\n        if (!header.classList.contains('component-header')) {\r\n          header.classList.add('component-header');\r\n        }\r\n\r\n        const allChildren = Array.from(component.children);\r\n        let nonHeaderChildren;\r\n\r\n        if (componentHeader) {\r\n          nonHeaderChildren = allChildren.filter(child =>\r\n            !child.classList.contains('component-header')\r\n          );\r\n        } else if (reportHeader) {\r\n          nonHeaderChildren = allChildren.filter(child =>\r\n            !child.classList.contains('report-header')\r\n          );\r\n        }\r\n\r\n        if (nonHeaderChildren && nonHeaderChildren.length > 0) {\r\n          const contentGroup = document.createElement('div');\r\n          contentGroup.className = 'component-content';\r\n\r\n          nonHeaderChildren.forEach(child => {\r\n            contentGroup.appendChild(child);\r\n          });\r\n\r\n          component.appendChild(contentGroup);\r\n        }\r\n\r\n        component.style.display = 'table';\r\n        component.style.width = '100%';\r\n      }\r\n    });\r\n\r\n    // Create HTML with minimal CSS changes\r\n    const htmlContent = `\r\n      <!DOCTYPE html>\r\n      <html lang=\"en\">\r\n      <head>\r\n        <meta charset=\"UTF-8\">\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n        <title>Custom Report</title>\r\n        <script src=\"https://cdn.tailwindcss.com\"></script>\r\n        <link href=\"https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap\" rel=\"stylesheet\">\r\n        <style>\r\n          @page {\r\n            size: A4 portrait;\r\n            margin-top: 1in;\r\n            margin-bottom : 1in;\r\n            margin-left : 0in;\r\n            margin-right : 0 in;\r\n          }\r\n\r\n          body {\r\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n            line-height: 1.6;\r\n            color: #374151;\r\n            background: white;\r\n          }\r\n\r\n          .page-break {\r\n            page-break-before: always;\r\n          }\r\n\r\n          .no-break {\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          /* Minimal styles - rely on frozen positions */\r\n          .border-b-4.border-blue-900 {\r\n            border-bottom: 4px solid #1e3a8a !important;\r\n            page-break-inside: avoid;\r\n            display: table-header-group;\r\n          }\r\n\r\n          .report-header {\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .component-header {\r\n            display: flex !important;\r\n            justify-content: space-between !important;\r\n            align-items: center !important;\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .metrics-flex{\r\n            display: flex !important;\r\n            justify-content: space-between !important;\r\n            align-items: center !important;\r\n          }\r\n          \r\n          .metrics-flex > * {\r\n            flex: 1 1 0% !important;\r\n            text-align: center;\r\n          }\r\n\r\n          .component-content {\r\n            display: table-row-group;\r\n          }\r\n\r\n          .min-h-screen {\r\n            display: table !important;\r\n            width: 100% !important;\r\n            min-height: auto !important;\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .min-h-screen:not(:first-child) {\r\n            page-break-before: always;\r\n          }\r\n\r\n          /* Trust the frozen chart and legend positions */\r\n          div[id*=\"apex\"] {\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .apexcharts-legend {\r\n            page-break-inside: avoid;\r\n          }\r\n\r\n          .text-teal-600 {\r\n            color: #0d9488 !important;\r\n          }\r\n\r\n          .bg-gray-200 {\r\n            background-color: #ffffff !important;\r\n          }\r\n\r\n          .bg-white {\r\n            background-color: #ffffff !important;\r\n          }\r\n\r\n          .overflow-y-auto {\r\n            overflow: visible !important;\r\n          }\r\n        </style>\r\n      </head>\r\n      <body>\r\n        <div class=\"report-container\">\r\n          ${clonedContent.innerHTML}\r\n        </div>\r\n      </body>\r\n      </html>\r\n    `;\r\n\r\n    console.log(\"Html content prepared for PDF generation with frozen charts and legends\", htmlContent);\r\n\r\n    // Generate PDF using the HTML content\r\n    const { generatePDFFromHTML } = await import('../../services/pdf');\r\n    const response = await generatePDFFromHTML(htmlContent, {\r\n      format: 'A4',\r\n      orientation: 'portrait',\r\n      printBackground: true,\r\n      margin: {\r\n        top: '0in',\r\n        right: '0in',\r\n        bottom: '0in',\r\n        left: '0in'\r\n      }\r\n    });\r\n\r\n    if (response.success && response.data.pdf) {\r\n      const filename = `Custom_Report_${new Date().toISOString().split('T')[0]}.pdf`;\r\n      const downloadSuccess = downloadPDFFromBase64(response.data.pdf, filename);\r\n\r\n      if (downloadSuccess) {\r\n        setSuccessMessage('PDF downloaded successfully!');\r\n        setShowSuccess(true);\r\n      } else {\r\n        throw new Error('Failed to download PDF');\r\n      }\r\n    } else {\r\n      throw new Error(response.message || 'Failed to generate PDF');\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('PDF Generation Error:', error);\r\n    setSuccessMessage('Failed to generate PDF. Please try again.');\r\n    setShowSuccess(true);\r\n  } finally {\r\n    setIsGeneratingPDF(false);\r\n  }\r\n};\r\n\r\n  // Initialize data on component mount\r\n  useEffect(() => {\r\n    initializeSettings();\r\n    fetchReportData();\r\n    fetchContentSettings(); // NEW: Fetch content settings\r\n  }, [companyId]); // Add companyId as dependency\r\n\r\n  const handleBackToDashboard = () => {\r\n    navigate(`/company/${companyId}`, {\r\n      state: { activeTab: 'reports' }\r\n    });\r\n  };\r\n\r\n  const handleSettingChange = (section, property, value) => {\r\n    // Apply font size constraints\r\n    if (property === 'fontSize') {\r\n      const constraints = fontSizeConstraints[section];\r\n      if (constraints) {\r\n        value = Math.max(constraints.min, Math.min(constraints.max, value));\r\n      }\r\n    }\r\n\r\n    setTemplateSettings(prev => ({\r\n      ...prev,\r\n      [section]: {\r\n        ...prev[section],\r\n        [property]: value\r\n      }\r\n    }));\r\n  };\r\n\r\n  const handleCloseSuccess = () => {\r\n    setShowSuccess(false);\r\n  };\r\n\r\n  // Save settings to API\r\n  const saveSettingsToAPI = async () => {\r\n    try {\r\n      setIsSavingSettings(true);\r\n\r\n      const payload = {\r\n        settings: templateSettings\r\n      };\r\n\r\n      const response = await axiosInstance.put('/template-settings', payload);\r\n\r\n      if (response.data && response.data.success) {\r\n        // Update localStorage with the saved settings\r\n        localStorage.setItem('templateSettings', JSON.stringify(templateSettings));\r\n\r\n        // Update initial settings to current settings\r\n        setInitialSettings(templateSettings);\r\n\r\n        setSuccessMessage('Settings saved successfully and applied globally!');\r\n        setShowSuccess(true);\r\n\r\n        console.log('Settings saved successfully:', response.data);\r\n      } else {\r\n        throw new Error('Failed to save template settings');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving template settings:', error);\r\n      setSuccessMessage('Failed to save settings. Please try again.');\r\n      setShowSuccess(true);\r\n    } finally {\r\n      setIsSavingSettings(false);\r\n    }\r\n  };\r\n\r\n  // Button handlers\r\n  const handleSave = () => {\r\n    setShowSaveConfirmModal(true);\r\n  };\r\n\r\n  const handleConfirmSave = async () => {\r\n    setShowSaveConfirmModal(false);\r\n    await saveSettingsToAPI();\r\n  };\r\n\r\n  const handleCancelSave = () => {\r\n    setShowSaveConfirmModal(false);\r\n  };\r\n\r\n  const handleResetToDefault = () => {\r\n    setShowResetConfirmModal(true);\r\n  };\r\n\r\n  const handleConfirmReset = () => {\r\n    try {\r\n      // Reset to initial settings loaded from API/localStorage\r\n      if (initialSettings) {\r\n        setTemplateSettings(initialSettings);\r\n        setSelectedTemplate('Deepsight');\r\n\r\n        // Update localStorage with initial settings\r\n        localStorage.setItem('templateSettings', JSON.stringify(initialSettings));\r\n\r\n        setSuccessMessage('Settings reset to initial values successfully!');\r\n        setShowSuccess(true);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error resetting settings:', error);\r\n      setSuccessMessage('Error resetting settings. Please try again.');\r\n      setShowSuccess(true);\r\n    }\r\n    setShowResetConfirmModal(false);\r\n  };\r\n\r\n  const handleCancelReset = () => {\r\n    setShowResetConfirmModal(false);\r\n  };\r\n\r\n\r\n  const handleResync = () => {\r\n    fetchReportData();\r\n    fetchContentSettings(); // Also resync content settings\r\n    setSuccessMessage('Data resynced successfully!');\r\n    setShowSuccess(true);\r\n  };\r\n\r\n\r\n\r\n  // Convert font type to CSS font-weight\r\n  const getFontWeight = (fontType) => {\r\n    const weights = {\r\n      'Regular': '400',\r\n      'Bold': '700'\r\n    };\r\n    return weights[fontType] || '400';\r\n  };\r\n\r\n  const getHeaderStyle = () => ({\r\n    fontFamily: templateSettings?.header?.fontStyle || 'Helvetica',\r\n    fontWeight: getFontWeight(templateSettings?.header?.fontType || 'Bold'),\r\n    fontSize: `${templateSettings?.header?.fontSize || 44}px`,\r\n    color: templateSettings?.header?.color || '#1e7c8c',\r\n    borderRadius: '8px 8px 0 0',\r\n    margin: '0',\r\n  });\r\n\r\n  const getHeadingStyle = () => ({\r\n    fontFamily: templateSettings?.heading?.fontStyle || 'Helvetica',\r\n    fontWeight: getFontWeight(templateSettings?.heading?.fontType || 'Bold'),\r\n    fontSize: `${templateSettings?.heading?.fontSize || 36}px`,\r\n    color: templateSettings?.heading?.color || '#1e7c8c',\r\n  });\r\n\r\n  const getContentStyle = () => ({\r\n    fontFamily: templateSettings?.content?.fontStyle || 'Helvetica',\r\n    fontWeight: getFontWeight(templateSettings?.content?.fontType || 'Regular'),\r\n    fontSize: `${templateSettings?.content?.fontSize || 15}px`,\r\n    color: templateSettings?.content?.color || '#333333',\r\n    lineHeight: '1.6',\r\n    margin: '0'\r\n  });\r\n\r\n  const getSubHeadingStyle = () => ({\r\n    fontFamily: templateSettings?.subHeading?.fontStyle || 'Helvetica',\r\n    fontWeight: getFontWeight(templateSettings?.subHeading?.fontType || 'Bold'),\r\n    fontSize: `${templateSettings?.subHeading?.fontSize || 22}px`,\r\n    color: templateSettings?.subHeading?.color || '#1e7c8c',\r\n    padding: '0'\r\n  });\r\n\r\n  // Show loading state while fetching initial settings or content settings\r\n  if (isLoadingSettings || isLoadingContentSettings || !templateSettings) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-screen\">\r\n        <div className=\"text-center\">\r\n          <CircularProgress size={40} />\r\n          <div className=\"mt-4 text-lg text-gray-600\">Loading settings...</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Snackbar for success/error messages */}\r\n      <Snackbar\r\n        open={showSuccess}\r\n        autoHideDuration={4000}\r\n        onClose={handleCloseSuccess}\r\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\r\n      >\r\n        <Alert\r\n          onClose={handleCloseSuccess}\r\n          severity={dataError || settingsError || contentSettingsError ? \"error\" : \"success\"}\r\n          variant=\"filled\"\r\n          sx={{\r\n            backgroundColor: dataError || settingsError || contentSettingsError ? '#d32f2f' : '#1976d2',\r\n            '& .MuiAlert-icon': {\r\n              color: 'white',\r\n            },\r\n          }}\r\n        >\r\n          {successMessage}\r\n        </Alert>\r\n      </Snackbar>\r\n\r\n      {/* Save Confirmation Modal */}\r\n      <Dialog\r\n        open={showSaveConfirmModal}\r\n        onClose={handleCancelSave}\r\n        aria-labelledby=\"save-dialog-title\"\r\n        aria-describedby=\"save-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"save-dialog-title\">\r\n          Confirm Save Settings\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"save-dialog-description\">\r\n            Are you sure you want to save these template settings? These settings will be applied globally and will affect all future reports.\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCancelSave} color=\"primary\">\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleConfirmSave}\r\n            color=\"primary\"\r\n            variant=\"contained\"\r\n            disabled={isSavingSettings}\r\n          >\r\n            {isSavingSettings ? (\r\n              <>\r\n                <CircularProgress size={16} sx={{ mr: 1 }} />\r\n                Saving...\r\n              </>\r\n            ) : (\r\n              'Save Settings'\r\n            )}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Reset Confirmation Modal */}\r\n      <Dialog\r\n        open={showResetConfirmModal}\r\n        onClose={handleCancelReset}\r\n        aria-labelledby=\"reset-dialog-title\"\r\n        aria-describedby=\"reset-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"reset-dialog-title\">\r\n          Confirm Reset Settings\r\n        </DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText id=\"reset-dialog-description\">\r\n            Are you sure you want to reset the template settings? This will restore the settings to their initial values when you first loaded this page. Any unsaved changes will be lost.\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCancelReset} color=\"primary\">\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={handleConfirmReset} color=\"primary\" variant=\"contained\">\r\n            Reset Settings\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Fixed Sidebar - Full Height */}\r\n      <div className=\"w-96 flex-shrink-0 h-screen flex flex-col fixed left-0 top-0 z-10 bg-gray-50\">\r\n        <div className=\"px-4 py-[14px] border-b bg-gray-50 flex items-center shadow\">\r\n          <Tooltip title=\"Back to Dashboard\" placement=\"bottom\">\r\n            <IconButton\r\n              onClick={handleBackToDashboard}\r\n              sx={{\r\n                color: 'rgb(75, 85, 99)',\r\n                padding: '6px',\r\n                marginRight: '12px',\r\n                '&:hover': {\r\n                  backgroundColor: 'rgba(75, 85, 99, 0.1)',\r\n                },\r\n                '&:focus': {\r\n                  outline: 'none',\r\n                },\r\n                transition: 'all 0.2s',\r\n              }}\r\n            >\r\n              <ArrowBack fontSize=\"medium\" />\r\n            </IconButton>\r\n          </Tooltip>\r\n          <h2 className=\"text-lg font-semibold text-gray-900\">Customize Template</h2>\r\n        </div>\r\n\r\n        <div className=\"p-4 space-y-6 overflow-y-auto flex-1 shadow\">\r\n          {/* Header Section */}\r\n          <div>\r\n            <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Header</h3>\r\n\r\n            <div className=\"mb-3\">\r\n              <label className=\"block text-xs text-gray-600 mb-1\">Font Style</label>\r\n              <select\r\n                value={templateSettings.header.fontStyle}\r\n                onChange={(e) => handleSettingChange('header', 'fontStyle', e.target.value)}\r\n                className=\"w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n                {fontStyles.map(font => (\r\n                  <option key={font} value={font}>{font}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">Font Type</label>\r\n                <select\r\n                  value={templateSettings.header.fontType}\r\n                  onChange={(e) => handleSettingChange('header', 'fontType', e.target.value)}\r\n                  className=\"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                >\r\n                  {fontTypes.map(type => (\r\n                    <option key={type} value={type}>{type}</option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">\r\n                  Font Size ({fontSizeConstraints.header.min}-{fontSizeConstraints.header.max})\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min={fontSizeConstraints.header.min}\r\n                  max={fontSizeConstraints.header.max}\r\n                  value={templateSettings.header.fontSize}\r\n                  onChange={(e) => handleSettingChange('header', 'fontSize', parseInt(e.target.value))}\r\n                  className=\"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">Color</label>\r\n                <input\r\n                  type=\"color\"\r\n                  value={templateSettings.header.color}\r\n                  onChange={(e) => handleSettingChange('header', 'color', e.target.value)}\r\n                  className=\"w-full border border-gray-300  h-9 rounded cursor-pointer\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Heading Section */}\r\n          <div>\r\n            <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Heading</h3>\r\n\r\n            <div className=\"mb-3\">\r\n              <label className=\"block text-xs text-gray-600 mb-1\">Font Style</label>\r\n              <select\r\n                value={templateSettings.heading.fontStyle}\r\n                onChange={(e) => handleSettingChange('heading', 'fontStyle', e.target.value)}\r\n                className=\"w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n                {fontStyles.map(font => (\r\n                  <option key={font} value={font}>{font}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">Font Type</label>\r\n                <select\r\n                  value={templateSettings.heading.fontType}\r\n                  onChange={(e) => handleSettingChange('heading', 'fontType', e.target.value)}\r\n                  className=\"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                >\r\n                  {fontTypes.map(type => (\r\n                    <option key={type} value={type}>{type}</option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">\r\n                  Font Size ({fontSizeConstraints.heading.min}-{fontSizeConstraints.heading.max})\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min={fontSizeConstraints.heading.min}\r\n                  max={fontSizeConstraints.heading.max}\r\n                  value={templateSettings.heading.fontSize}\r\n                  onChange={(e) => handleSettingChange('heading', 'fontSize', parseInt(e.target.value))}\r\n                  className=\"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">Color</label>\r\n                <input\r\n                  type=\"color\"\r\n                  value={templateSettings.heading.color}\r\n                  onChange={(e) => handleSettingChange('heading', 'color', e.target.value)}\r\n                  className=\"w-full border border-gray-300 h-9 rounded cursor-pointer\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Sub-Heading Section */}\r\n          <div>\r\n            <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Sub-Heading</h3>\r\n\r\n            <div className=\"mb-3\">\r\n              <label className=\"block text-xs text-gray-600 mb-1\">Font Style</label>\r\n              <select\r\n                value={templateSettings.subHeading.fontStyle}\r\n                onChange={(e) => handleSettingChange('subHeading', 'fontStyle', e.target.value)}\r\n                className=\"w-full border border-gray-300 px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n                {fontStyles.map(font => (\r\n                  <option key={font} value={font}>{font}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">Font Type</label>\r\n                <select\r\n                  value={templateSettings.subHeading.fontType}\r\n                  onChange={(e) => handleSettingChange('subHeading', 'fontType', e.target.value)}\r\n                  className=\"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                >\r\n                  {fontTypes.map(type => (\r\n                    <option key={type} value={type}>{type}</option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">\r\n                  Font Size ({fontSizeConstraints.subHeading.min}-{fontSizeConstraints.subHeading.max})\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min={fontSizeConstraints.subHeading.min}\r\n                  max={fontSizeConstraints.subHeading.max}\r\n                  value={templateSettings.subHeading.fontSize}\r\n                  onChange={(e) => handleSettingChange('subHeading', 'fontSize', parseInt(e.target.value))}\r\n                  className=\"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">Color</label>\r\n                <input\r\n                  type=\"color\"\r\n                  value={templateSettings.subHeading.color}\r\n                  onChange={(e) => handleSettingChange('subHeading', 'color', e.target.value)}\r\n                  className=\"w-full border border-gray-300 h-9 rounded cursor-pointer\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Content Section */}\r\n          <div>\r\n            <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Content</h3>\r\n\r\n            <div className=\"mb-3\">\r\n              <label className=\"block text-xs text-gray-600 mb-1\">Font Style</label>\r\n              <select\r\n                value={templateSettings.content.fontStyle}\r\n                onChange={(e) => handleSettingChange('content', 'fontStyle', e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n                {fontStyles.map(font => (\r\n                  <option key={font} value={font}>{font}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-3 gap-2 mb-3\">\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">Font Type</label>\r\n                <select\r\n                  value={templateSettings.content.fontType}\r\n                  onChange={(e) => handleSettingChange('content', 'fontType', e.target.value)}\r\n                  className=\"w-full px-2 py-2 border border-gray-300  rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                >\r\n                  {fontTypes.map(type => (\r\n                    <option key={type} value={type}>{type}</option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">\r\n                  Font Size ({fontSizeConstraints.content.min}-{fontSizeConstraints.content.max})\r\n                </label>\r\n                <input\r\n                  type=\"number\"\r\n                  min={fontSizeConstraints.content.min}\r\n                  max={fontSizeConstraints.content.max}\r\n                  value={templateSettings.content.fontSize}\r\n                  onChange={(e) => handleSettingChange('content', 'fontSize', parseInt(e.target.value))}\r\n                  className=\"w-full border border-gray-300 px-2 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-xs text-gray-600 mb-1\">Color</label>\r\n                <input\r\n                  type=\"color\"\r\n                  value={templateSettings.content.color}\r\n                  onChange={(e) => handleSettingChange('content', 'color', e.target.value)}\r\n                  className=\"w-full border border-gray-300 h-9 rounded cursor-pointer\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"pt-7 border-t border-gray-300\">\r\n            <div className=\"flex flex-row gap-4\">\r\n              <button\r\n                onClick={handleSave}\r\n                disabled={isSavingSettings}\r\n                className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-neutral-800\"\r\n              >\r\n                {isSavingSettings ? (\r\n                  <>\r\n                    <CircularProgress size={14} sx={{ mr: 1 }} />\r\n                    SAVING...\r\n                  </>\r\n                ) : (\r\n                  'SAVE'\r\n                )}\r\n              </button>\r\n\r\n              <button\r\n                onClick={handleResetToDefault}\r\n                className=\"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:ring-offset-neutral-800\"\r\n              >\r\n                RESET\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content Area - Header and Content */}\r\n      <div className=\"flex flex-col flex-1 h-screen\" style={{ marginLeft: '384px' }}>\r\n        {/* Fixed Header */}\r\n        <div className=\"px-6 py-3 flex items-center bg-gray-50 justify-end fixed top-0 z-5 shadow\"\r\n          style={{ left: '384px', right: '0' }}>\r\n\r\n          <div className=\"flex items-center justify-end space-x-3\">\r\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n              <Tooltip title={isLoadingData ? \"Loading data...\" : \"Resync data\"} placement=\"top\">\r\n                <span>\r\n                  <IconButton\r\n                    onClick={handleResync}\r\n                    disabled={isLoadingData}\r\n                    sx={{\r\n                      color: isLoadingData ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',\r\n                      '&:hover': {\r\n                        backgroundColor: 'rgba(156, 163, 175, 0.1)',\r\n                      },\r\n                      '&:focus': {\r\n                        outline: 'none',\r\n                      },\r\n                      '&:disabled': {\r\n                        color: 'rgba(156, 163, 175, 0.6)',\r\n                        cursor: 'not-allowed',\r\n                      },\r\n                      transition: 'all 0.2s',\r\n                      padding: '8px',\r\n                    }}\r\n                  >\r\n                    {isLoadingData ? (\r\n                      <CircularProgress size={16} color=\"inherit\" />\r\n                    ) : (\r\n                      <SyncIcon fontSize=\"medium\" />\r\n                    )}\r\n                  </IconButton>\r\n                </span>\r\n              </Tooltip>\r\n\r\n              <Tooltip title={isGeneratingPDF ? \"Generating PDF...\" : \"Download PDF\"} placement=\"top\">\r\n                <span>\r\n                  <IconButton\r\n                    onClick={handleDownloadPDF}\r\n                    disabled={isGeneratingPDF || isLoadingData}\r\n                    sx={{\r\n                      color: (isGeneratingPDF || isLoadingData) ? 'rgba(156, 163, 175, 0.6)' : 'rgb(75, 85, 99)',\r\n                      '&:hover': {\r\n                        backgroundColor: 'rgba(156, 163, 175, 0.1)',\r\n                      },\r\n                      '&:focus': {\r\n                        outline: 'none',\r\n                      },\r\n                      '&:disabled': {\r\n                        color: 'rgba(156, 163, 175, 0.6)',\r\n                        cursor: 'not-allowed',\r\n                      },\r\n                      transition: 'all 0.2s',\r\n                      padding: '8px',\r\n                    }}\r\n                  >\r\n                    {isGeneratingPDF ? (\r\n                      <CircularProgress size={16} color=\"inherit\" />\r\n                    ) : (\r\n                      <DownloadIcon fontSize=\"medium\" />\r\n                    )}\r\n                  </IconButton>\r\n                </span>\r\n              </Tooltip>\r\n            </Box>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Scrollable Content Panel */}\r\n        <div\r\n          className=\"flex-1 overflow-y-auto bg-gray-200\"\r\n          style={{ marginTop: '60px', width: '100%' }}\r\n        >\r\n          {isLoadingData ? (\r\n            <div className=\"flex items-center justify-center h-64\">\r\n              <div className=\"text-center\">\r\n                <CircularProgress size={40} />\r\n                <div className=\"mt-4 text-lg text-gray-600\">Loading report data...</div>\r\n              </div>\r\n            </div>\r\n          ) : dataError ? (\r\n            <div className=\"flex items-center justify-center h-64\">\r\n              <div className=\"text-center\">\r\n                <div className=\"text-lg text-red-600 mb-4\">Failed to load report data</div>\r\n                <button\r\n                  onClick={fetchReportData}\r\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\r\n                >\r\n                  Retry\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <>\r\n\r\n              <DeepSightCoverPage />\r\n\r\n              <TableOfContents\r\n                headingTextStyle={getHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n              />\r\n              <ReportSummary\r\n                headerTextStyle={getHeaderStyle()}\r\n                headingTextStyle={getHeadingStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n              />\r\n              <FiscalYearDashboard\r\n                headerTextStyle={getHeaderStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                fiscalData={reportData}\r\n                contentSettings={contentSettings}\r\n              />\r\n              <ExpenseSummaryDashboard\r\n                headerTextStyle={getHeaderStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                reportData={reportData}\r\n                contentSettings={contentSettings}\r\n              />\r\n              <OperationalEfficiencyDashboard\r\n                headerTextStyle={getHeaderStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                operationalData={reportData}\r\n              />\r\n              <LiquiditySummaryDashboard\r\n                headerTextStyle={getHeaderStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                liquidityData={reportData}\r\n              />\r\n              <ProfitLoss13MonthDashboard\r\n                headerTextStyle={getHeaderStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                reportData={reportData}\r\n              />\r\n              <ProfitLossMonthlyDashboard\r\n                headerTextStyle={getHeaderStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                reportData={reportData}\r\n\r\n              />\r\n              <ProfitLossYTDDashboard\r\n                headerTextStyle={getHeaderStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                reportData={reportData}\r\n              />\r\n              <BalanceSheetDashboard\r\n                headerTextStyle={getHeaderStyle()}\r\n                subHeadingTextStyle={getSubHeadingStyle()}\r\n                contentTextStyle={getContentStyle()}\r\n                reportData={reportData}\r\n\r\n              />\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomizeTemplateWithPreview;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,GAAG,EAAEC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,MAAM,QAAQ,eAAe;AACxJ,SAASC,IAAI,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,IAAIC,YAAY,QAAQ,qBAAqB;AAC3F,SAASC,QAAQ,EAAEC,KAAK,QAAQ,eAAe;AAC/C,SAASC,eAAe,QAAQ,kBAAkB;AAClD,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,mBAAmB,MAAM,0BAA0B;AAC1D,OAAOC,uBAAuB,MAAM,8BAA8B;AAClE,OAAOC,8BAA8B,MAAM,qCAAqC;AAChF,OAAOC,yBAAyB,MAAM,gCAAgC;AACtE,OAAOC,0BAA0B,MAAM,6BAA6B;AACpE,OAAOC,0BAA0B,MAAM,uBAAuB;AAC9D,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,SAASC,qBAAqB,QAAQ,oBAAoB;AAC1D,OAAOC,kBAAkB,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIzD,MAAMC,4BAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,YAAY,CAAC,GAAGtB,eAAe,CAAC,CAAC;EAExC,MAAMuB,MAAM,GAAGX,SAAS,CAAC,CAAC;EAC1B,MAAMY,SAAS,GAAGD,MAAM,CAACE,EAAE;;EAE3B;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiD,SAAS,EAAEC,YAAY,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACuD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6D,wBAAwB,EAAEC,2BAA2B,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC9E,MAAM,CAAC+D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;;EAEtE;EACA,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,WAAW,CAAC;EACrE,MAAM,CAACuE,eAAe,EAAEC,kBAAkB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAAC2E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAAC6E,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAEzE,MAAM+E,QAAQ,GAAGjD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMkD,eAAe,GAAG;IACtBC,MAAM,EAAE;MACNC,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;IACDC,OAAO,EAAE;MACPJ,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;IACDE,UAAU,EAAE;MACVL,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT,CAAC;IACDG,OAAO,EAAE;MACPN,SAAS,EAAE,WAAW;MACtBC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;IACT;EACF,CAAC;EAED,MAAMI,UAAU,GAAG,CAAC,WAAW,EAAC,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC;EACzG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;;EAErC;EACA,MAAMC,mBAAmB,GAAG;IAC1BV,MAAM,EAAE;MAAEW,GAAG,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC;IAC5BP,OAAO,EAAE;MAAEM,GAAG,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC;IAC7BN,UAAU,EAAE;MAAEK,GAAG,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC;IAChCL,OAAO,EAAE;MAAEI,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAG;EAC7B,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFhC,2BAA2B,CAAC,IAAI,CAAC;MACjCE,uBAAuB,CAAC,IAAI,CAAC;MAE7B,MAAM+B,QAAQ,GAAG,MAAM/D,aAAa,CAACgE,GAAG,CAAC,6BAA6BrD,SAAS,YAAY,CAAC;MAE5F,IAAIoD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAMC,YAAY,GAAGJ,QAAQ,CAACE,IAAI,CAACA,IAAI;QACvCG,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,YAAY,CAAC;QAC9DvC,kBAAkB,CAACuC,YAAY,CAAC;MAClC,CAAC,MAAM;QACL,MAAM,IAAIG,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDvC,uBAAuB,CAACuC,KAAK,CAACC,OAAO,CAAC;;MAEtC;MACA,MAAMC,sBAAsB,GAAG;QAC7BC,aAAa,EAAE;UACbC,aAAa,EAAE,IAAI;UACnBC,SAAS,EAAE,IAAI;UACfC,iBAAiB,EAAE,IAAI;UACvBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAE;QACb;MACF,CAAC;MACDnD,kBAAkB,CAAC6C,sBAAsB,CAAC;MAE1CrC,iBAAiB,CAAC,2DAA2D,CAAC;MAC9EF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRJ,2BAA2B,CAAC,KAAK,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMkD,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFxD,oBAAoB,CAAC,IAAI,CAAC;MAC1BE,gBAAgB,CAAC,IAAI,CAAC;MAEtB,MAAMqC,QAAQ,GAAG,MAAM/D,aAAa,CAACgE,GAAG,CAAC,oBAAoB,CAAC;MAE9D,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1C,MAAMC,YAAY,GAAGJ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACgB,QAAQ;;QAEhD;QACAC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAAClB,YAAY,CAAC,CAAC;;QAEtE;QACA/C,mBAAmB,CAAC+C,YAAY,CAAC;QACjC7C,kBAAkB,CAAC6C,YAAY,CAAC;QAEhCC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEN,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACvE,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAAC,mCAAmC,CAAC;MACtD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD7C,gBAAgB,CAAC6C,KAAK,CAACC,OAAO,CAAC;;MAE/B;MACA,MAAMc,aAAa,GAAGJ,YAAY,CAACK,OAAO,CAAC,kBAAkB,CAAC;MAC9D,MAAMC,gBAAgB,GAAGF,aAAa,GAAGF,IAAI,CAACK,KAAK,CAACH,aAAa,CAAC,GAAGtC,eAAe;MAEpF5B,mBAAmB,CAACoE,gBAAgB,CAAC;MACrClE,kBAAkB,CAACkE,gBAAgB,CAAC;MAEpCpD,iBAAiB,CAAC,oDAAoD,CAAC;MACvEF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRV,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMkE,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI;MACF;MACA,MAAMC,WAAW,GAAGlF,YAAY,CAACuD,GAAG,CAAC,kBAAkB,CAAC;MACxD,IAAI2B,WAAW,EAAE;QACf,MAAMC,cAAc,GAAGR,IAAI,CAACK,KAAK,CAACE,WAAW,CAAC;QAC9CvE,mBAAmB,CAACwE,cAAc,CAAC;QACnCtE,kBAAkB,CAACsE,cAAc,CAAC;QAClCpE,oBAAoB,CAAC,KAAK,CAAC;QAC3B;MACF;;MAEA;MACAwD,qBAAqB,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDnD,mBAAmB,CAAC4B,eAAe,CAAC;MACpC1B,kBAAkB,CAAC0B,eAAe,CAAC;MACnCxB,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMqE,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF7E,gBAAgB,CAAC,IAAI,CAAC;MACtBE,YAAY,CAAC,IAAI,CAAC;MAElB,MAAM6C,QAAQ,GAAG,MAAM/D,aAAa,CAACgE,GAAG,CAAC,gCAAgC,CAAC;MAE1E,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1CE,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEN,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;QAClEnD,aAAa,CAACiD,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACnC,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAAC,6BAA6B,CAAC;MAChD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDrD,YAAY,CAACqD,KAAK,CAACC,OAAO,CAAC;MAC3BpC,iBAAiB,CAAC,+CAA+C,CAAC;MAClEF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRlB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACF;;EAEA;;EAEA,MAAM8E,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFtD,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,MAAMuD,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,qCAAqC,CAAC;MAClF,IAAI,CAACF,YAAY,EAAE;QACjB,MAAM,IAAIzB,KAAK,CAAC,+CAA+C,CAAC;MAClE;;MAEA;MACA,MAAM,IAAI4B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,sBAAsB,GAAIC,SAAS,IAAK;QAC5C;QACA,MAAMC,eAAe,GAAGD,SAAS,CAACE,gBAAgB,CAAC,iBAAiB,CAAC;QACrEpC,OAAO,CAACC,GAAG,CAAC,SAASkC,eAAe,CAACE,MAAM,mBAAmB,CAAC;QAE/DF,eAAe,CAACG,OAAO,CAAC,CAACC,cAAc,EAAEC,KAAK,KAAK;UACjD,IAAI;YACF;YACA,MAAMC,WAAW,GAAGF,cAAc,CAACH,gBAAgB,CAAC,YAAY,CAAC;YACjEK,WAAW,CAACH,OAAO,CAACI,KAAK,IAAI;cAC3B;cACA,MAAMC,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACH,KAAK,CAAC;;cAEpD;cACA,IAAIA,KAAK,CAACI,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;gBACzCL,KAAK,CAACM,KAAK,CAACC,KAAK,GAAGN,aAAa,CAACM,KAAK;gBACvCP,KAAK,CAACM,KAAK,CAACE,MAAM,GAAGP,aAAa,CAACO,MAAM;gBACzCR,KAAK,CAACM,KAAK,CAACG,QAAQ,GAAG,SAAS;cAClC;;cAEA;cACA,IAAIR,aAAa,CAACS,MAAM,IAAIT,aAAa,CAACS,MAAM,KAAK,MAAM,EAAE;gBAC3DV,KAAK,CAACM,KAAK,CAACI,MAAM,GAAGT,aAAa,CAACS,MAAM;cAC3C;cACA,IAAIT,aAAa,CAACU,WAAW,IAAIV,aAAa,CAACU,WAAW,KAAK,KAAK,EAAE;gBACpEX,KAAK,CAACM,KAAK,CAACK,WAAW,GAAGV,aAAa,CAACU,WAAW;cACrD;cACA,IAAIV,aAAa,CAACW,IAAI,IAAIX,aAAa,CAACW,IAAI,KAAK,MAAM,EAAE;gBACvDZ,KAAK,CAACM,KAAK,CAACM,IAAI,GAAGX,aAAa,CAACW,IAAI;cACvC;cACA,IAAIX,aAAa,CAACY,eAAe,IAAIZ,aAAa,CAACY,eAAe,KAAK,MAAM,EAAE;gBAC7Eb,KAAK,CAACM,KAAK,CAACO,eAAe,GAAGZ,aAAa,CAACY,eAAe;cAC7D;;cAEA;cACA,IAAIZ,aAAa,CAACa,SAAS,IAAIb,aAAa,CAACa,SAAS,KAAK,MAAM,EAAE;gBACjEd,KAAK,CAACM,KAAK,CAACQ,SAAS,GAAGb,aAAa,CAACa,SAAS;cACjD;YACF,CAAC,CAAC;;YAEF;YACA,MAAMC,aAAa,GAAGlB,cAAc,CAACmB,qBAAqB,CAAC,CAAC;YAC5DnB,cAAc,CAACS,KAAK,CAACC,KAAK,GAAGQ,aAAa,CAACR,KAAK,GAAG,IAAI;YACvDV,cAAc,CAACS,KAAK,CAACE,MAAM,GAAGO,aAAa,CAACP,MAAM,GAAG,IAAI;YACzDX,cAAc,CAACS,KAAK,CAACW,QAAQ,GAAG,UAAU;YAC1CpB,cAAc,CAACS,KAAK,CAACG,QAAQ,GAAG,SAAS;;YAEzC;YACA,MAAMS,YAAY,GAAGrB,cAAc,CAACH,gBAAgB,CAAC,uBAAuB,CAAC;YAC7EwB,YAAY,CAACtB,OAAO,CAACuB,OAAO,IAAI;cAC9B,MAAMlB,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACgB,OAAO,CAAC;;cAEtD;cACA,IAAIlB,aAAa,CAACgB,QAAQ,IAAIhB,aAAa,CAACgB,QAAQ,KAAK,QAAQ,EAAE;gBACjEE,OAAO,CAACb,KAAK,CAACW,QAAQ,GAAGhB,aAAa,CAACgB,QAAQ;gBAC/CE,OAAO,CAACb,KAAK,CAACc,GAAG,GAAGnB,aAAa,CAACmB,GAAG;gBACrCD,OAAO,CAACb,KAAK,CAACe,IAAI,GAAGpB,aAAa,CAACoB,IAAI;gBACvCF,OAAO,CAACb,KAAK,CAACgB,KAAK,GAAGrB,aAAa,CAACqB,KAAK;gBACzCH,OAAO,CAACb,KAAK,CAACiB,MAAM,GAAGtB,aAAa,CAACsB,MAAM;cAC7C;;cAEA;cACAJ,OAAO,CAACb,KAAK,CAACC,KAAK,GAAGN,aAAa,CAACM,KAAK;cACzCY,OAAO,CAACb,KAAK,CAACE,MAAM,GAAGP,aAAa,CAACO,MAAM;;cAE3C;cACAW,OAAO,CAACb,KAAK,CAACkB,OAAO,GAAGvB,aAAa,CAACuB,OAAO;cAC7CL,OAAO,CAACb,KAAK,CAACmB,UAAU,GAAGxB,aAAa,CAACwB,UAAU;cACnDN,OAAO,CAACb,KAAK,CAACoB,OAAO,GAAGzB,aAAa,CAACyB,OAAO;YAC/C,CAAC,CAAC;;YAEF;YACA,MAAMC,SAAS,GAAG9B,cAAc,CAACH,gBAAgB,CAAC,4EAA4E,CAAC;YAC/HiC,SAAS,CAAC/B,OAAO,CAACgC,IAAI,IAAI;cACxB,MAAM3B,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACyB,IAAI,CAAC;cACnDA,IAAI,CAACtB,KAAK,CAACI,MAAM,GAAGT,aAAa,CAACS,MAAM;cACxCkB,IAAI,CAACtB,KAAK,CAACK,WAAW,GAAGV,aAAa,CAACU,WAAW;cAClDiB,IAAI,CAACtB,KAAK,CAACO,eAAe,GAAGZ,aAAa,CAACY,eAAe;cAC1De,IAAI,CAACtB,KAAK,CAACoB,OAAO,GAAGzB,aAAa,CAACyB,OAAO;YAC5C,CAAC,CAAC;;YAEF;YACA,MAAMG,SAAS,GAAGhC,cAAc,CAACH,gBAAgB,CAAC,gDAAgD,CAAC;YACnGmC,SAAS,CAACjC,OAAO,CAACgC,IAAI,IAAI;cACxB,MAAM3B,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACyB,IAAI,CAAC;cACnDA,IAAI,CAACtB,KAAK,CAACI,MAAM,GAAGT,aAAa,CAACS,MAAM;cACxCkB,IAAI,CAACtB,KAAK,CAACK,WAAW,GAAGV,aAAa,CAACU,WAAW;cAClDiB,IAAI,CAACtB,KAAK,CAACoB,OAAO,GAAGzB,aAAa,CAACyB,OAAO;YAC5C,CAAC,CAAC;;YAEF;YACA,MAAMI,cAAc,GAAGjC,cAAc,CAACH,gBAAgB,CAAC,6EAA6E,CAAC;YACrIoC,cAAc,CAAClC,OAAO,CAACuB,OAAO,IAAI;cAChC,MAAMlB,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACgB,OAAO,CAAC;cACtD,IAAIlB,aAAa,CAACW,IAAI,EAAEO,OAAO,CAACb,KAAK,CAACM,IAAI,GAAGX,aAAa,CAACW,IAAI;cAC/D,IAAIX,aAAa,CAACS,MAAM,EAAES,OAAO,CAACb,KAAK,CAACI,MAAM,GAAGT,aAAa,CAACS,MAAM;cACrE,IAAIT,aAAa,CAACU,WAAW,EAAEQ,OAAO,CAACb,KAAK,CAACK,WAAW,GAAGV,aAAa,CAACU,WAAW;cACpF,IAAIV,aAAa,CAACyB,OAAO,EAAEP,OAAO,CAACb,KAAK,CAACoB,OAAO,GAAGzB,aAAa,CAACyB,OAAO;YAC1E,CAAC,CAAC;YAEFpE,OAAO,CAACC,GAAG,CAAC,SAASuC,KAAK,4BAA4BiB,aAAa,CAACR,KAAK,IAAIQ,aAAa,CAACP,MAAM,EAAE,CAAC;UAEtG,CAAC,CAAC,OAAO/C,KAAK,EAAE;YACdH,OAAO,CAACG,KAAK,CAAC,wBAAwBqC,KAAK,GAAG,EAAErC,KAAK,CAAC;UACxD;QACF,CAAC,CAAC;;QAEF;QACA,MAAMsE,OAAO,GAAGvC,SAAS,CAACE,gBAAgB,CAAC,oBAAoB,CAAC;QAChEpC,OAAO,CAACC,GAAG,CAAC,SAASwE,OAAO,CAACpC,MAAM,oBAAoB,CAAC;QAExDoC,OAAO,CAACnC,OAAO,CAAC,CAACoC,MAAM,EAAElC,KAAK,KAAK;UACjC,IAAI;YACF;YACA,MAAMmC,IAAI,GAAGD,MAAM,CAAChB,qBAAqB,CAAC,CAAC;;YAE3C;YACA,IAAInB,cAAc,GAAGmC,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;YACtD,IAAI,CAACrC,cAAc,EAAE;cACnB;cACAA,cAAc,GAAGmC,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;YAClD;YACA,IAAI,CAACrC,cAAc,EAAE;cACnB;cACAA,cAAc,GAAGmC,MAAM,CAACE,OAAO,CAAC,eAAe,CAAC;YAClD;YAEA,IAAIrC,cAAc,EAAE;cAClB,MAAMsC,UAAU,GAAGtC,cAAc,CAACmB,qBAAqB,CAAC,CAAC;;cAEzD;cACA,MAAMoB,WAAW,GAAGH,IAAI,CAACb,GAAG,GAAGe,UAAU,CAACf,GAAG;cAC7C,MAAMiB,YAAY,GAAGJ,IAAI,CAACZ,IAAI,GAAGc,UAAU,CAACd,IAAI;;cAEhD;cACAW,MAAM,CAACM,YAAY,CAAC,wBAAwB,EAAEN,MAAM,CAAC1B,KAAK,CAACW,QAAQ,IAAI,EAAE,CAAC;cAC1Ee,MAAM,CAACM,YAAY,CAAC,mBAAmB,EAAEN,MAAM,CAAC1B,KAAK,CAACc,GAAG,IAAI,EAAE,CAAC;cAChEY,MAAM,CAACM,YAAY,CAAC,oBAAoB,EAAEN,MAAM,CAAC1B,KAAK,CAACe,IAAI,IAAI,EAAE,CAAC;;cAElE;cACAW,MAAM,CAAC1B,KAAK,CAACW,QAAQ,GAAG,UAAU;cAClCe,MAAM,CAAC1B,KAAK,CAACc,GAAG,GAAGmB,IAAI,CAACxF,GAAG,CAAC,CAAC,EAAEqF,WAAW,CAAC,GAAG,IAAI;cAClDJ,MAAM,CAAC1B,KAAK,CAACe,IAAI,GAAGgB,YAAY,GAAG,IAAI;cACvCL,MAAM,CAAC1B,KAAK,CAACgB,KAAK,GAAG,MAAM;cAC3BU,MAAM,CAAC1B,KAAK,CAACiB,MAAM,GAAG,MAAM;cAC5BS,MAAM,CAAC1B,KAAK,CAACQ,SAAS,GAAG,MAAM;cAC/BkB,MAAM,CAAC1B,KAAK,CAACkC,MAAM,GAAG,IAAI;;cAE1B;cACA,MAAMC,iBAAiB,GAAGvC,MAAM,CAACC,gBAAgB,CAACN,cAAc,CAAC,CAACoB,QAAQ;cAC1E,IAAIwB,iBAAiB,KAAK,QAAQ,EAAE;gBAClC5C,cAAc,CAACS,KAAK,CAACW,QAAQ,GAAG,UAAU;cAC5C;;cAEA;cACA,MAAMyB,cAAc,GAAGV,MAAM,CAACtC,gBAAgB,CAAC,GAAG,CAAC;cACnDgD,cAAc,CAAC9C,OAAO,CAAC+C,EAAE,IAAI;gBAC3B,MAAM1C,aAAa,GAAGC,MAAM,CAACC,gBAAgB,CAACwC,EAAE,CAAC;gBACjD,IAAI1C,aAAa,CAAC1D,KAAK,EAAEoG,EAAE,CAACrC,KAAK,CAAC/D,KAAK,GAAG0D,aAAa,CAAC1D,KAAK;gBAC7D,IAAI0D,aAAa,CAAC3D,QAAQ,EAAEqG,EAAE,CAACrC,KAAK,CAAChE,QAAQ,GAAG2D,aAAa,CAAC3D,QAAQ;gBACtE,IAAI2D,aAAa,CAAC2C,UAAU,EAAED,EAAE,CAACrC,KAAK,CAACsC,UAAU,GAAG3C,aAAa,CAAC2C,UAAU;gBAC5E,IAAI3C,aAAa,CAAC4C,UAAU,EAAEF,EAAE,CAACrC,KAAK,CAACuC,UAAU,GAAG5C,aAAa,CAAC4C,UAAU;cAC9E,CAAC,CAAC;cAEFvF,OAAO,CAACC,GAAG,CAAC,UAAUuC,KAAK,wBAAwBsC,WAAW,OAAOC,YAAY,IAAI,CAAC;YAExF,CAAC,MAAM;cACL/E,OAAO,CAACwF,IAAI,CAAC,6CAA6ChD,KAAK,EAAE,CAAC;;cAElE;cACAkC,MAAM,CAAC1B,KAAK,CAACW,QAAQ,GAAG,UAAU;cAClCe,MAAM,CAAC1B,KAAK,CAACc,GAAG,GAAG,MAAM;cACzBY,MAAM,CAAC1B,KAAK,CAACe,IAAI,GAAG,MAAM;cAC1BW,MAAM,CAAC1B,KAAK,CAACgB,KAAK,GAAG,MAAM;cAC3BU,MAAM,CAAC1B,KAAK,CAACQ,SAAS,GAAG,MAAM;cAC/BkB,MAAM,CAAC1B,KAAK,CAACyC,MAAM,GAAG,WAAW;cACjCf,MAAM,CAAC1B,KAAK,CAACkB,OAAO,GAAG,MAAM;cAC7BQ,MAAM,CAAC1B,KAAK,CAAC0C,cAAc,GAAG,QAAQ;YACxC;UAEF,CAAC,CAAC,OAAOvF,KAAK,EAAE;YACdH,OAAO,CAACG,KAAK,CAAC,yBAAyBqC,KAAK,GAAG,EAAErC,KAAK,CAAC;;YAEvD;YACAuE,MAAM,CAAC1B,KAAK,CAACW,QAAQ,GAAG,QAAQ;YAChCe,MAAM,CAAC1B,KAAK,CAACyC,MAAM,GAAG,WAAW;YACjCf,MAAM,CAAC1B,KAAK,CAACkB,OAAO,GAAG,MAAM;YAC7BQ,MAAM,CAAC1B,KAAK,CAAC0C,cAAc,GAAG,QAAQ;UACxC;QACF,CAAC,CAAC;MACJ,CAAC;;MAED;MACAzD,sBAAsB,CAACN,YAAY,CAAC;;MAEpC;MACA,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAM4D,aAAa,GAAGhE,YAAY,CAACiE,SAAS,CAAC,IAAI,CAAC;;MAElD;MACA,MAAMC,YAAY,GAAGF,aAAa,CAACvD,gBAAgB,CAAC,iBAAiB,CAAC;MACtEyD,YAAY,CAACvD,OAAO,CAAC,CAACwD,KAAK,EAAEtD,KAAK,KAAK;QACrC;QACA,MAAM6B,SAAS,GAAGyB,KAAK,CAAC1D,gBAAgB,CAAC,4EAA4E,CAAC;QACtH,MAAM2D,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;QAE3B3B,SAAS,CAAC/B,OAAO,CAACgC,IAAI,IAAI;UACxB,MAAM2B,OAAO,GAAG,GAAG3B,IAAI,CAAC4B,YAAY,CAAC,IAAI,CAAC,IAAI5B,IAAI,CAAC4B,YAAY,CAAC,IAAI,CAAC,IAAI5B,IAAI,CAAC4B,YAAY,CAAC,IAAI,CAAC,IAAI5B,IAAI,CAAC4B,YAAY,CAAC,IAAI,CAAC,EAAE;UAC7H,IAAIH,SAAS,CAACI,GAAG,CAACF,OAAO,CAAC,EAAE;YAC1B;YACA3B,IAAI,CAAC8B,MAAM,CAAC,CAAC;UACf,CAAC,MAAM;YACLL,SAAS,CAACM,GAAG,CAACJ,OAAO,CAAC;UACxB;QACF,CAAC,CAAC;;QAEF;QACAH,KAAK,CAAC9C,KAAK,CAACsD,SAAS,GAAG,MAAM;QAC9BR,KAAK,CAAC9C,KAAK,CAACuD,SAAS,GAAG,MAAM;MAChC,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAGb,aAAa,CAACvD,gBAAgB,CAAC,oBAAoB,CAAC;MAC1EoE,aAAa,CAAClE,OAAO,CAAC,CAACoC,MAAM,EAAElC,KAAK,KAAK;QACvC;QACA,MAAMiE,QAAQ,GAAGC,UAAU,CAAChC,MAAM,CAAC1B,KAAK,CAACc,GAAG,CAAC,IAAI,CAAC;QAClD,IAAI2C,QAAQ,GAAG,GAAG,EAAE;UAClBzG,OAAO,CAACC,GAAG,CAAC,sCAAsCuC,KAAK,cAAciE,QAAQ,IAAI,CAAC;UAClF/B,MAAM,CAAC1B,KAAK,CAACc,GAAG,GAAG,MAAM;UACzBY,MAAM,CAAC1B,KAAK,CAACW,QAAQ,GAAG,UAAU;UAClCe,MAAM,CAAC1B,KAAK,CAACyC,MAAM,GAAG,qBAAqB;UAC3Cf,MAAM,CAAC1B,KAAK,CAACkB,OAAO,GAAG,MAAM;UAC7BQ,MAAM,CAAC1B,KAAK,CAAC0C,cAAc,GAAG,QAAQ;QACxC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMiB,UAAU,GAAGhB,aAAa,CAACvD,gBAAgB,CAAC,eAAe,CAAC;MAClEuE,UAAU,CAACrE,OAAO,CAAEsE,SAAS,IAAK;QAChC,MAAMC,eAAe,GAAGD,SAAS,CAAC/E,aAAa,CAAC,mBAAmB,CAAC;QACpE,MAAMiF,YAAY,GAAGF,SAAS,CAAC/E,aAAa,CAAC,gBAAgB,CAAC;QAC9D,MAAMkF,UAAU,GAAGH,SAAS,CAAC/E,aAAa,CAAC,eAAe,CAAC;QAE3D,IAAIhD,MAAM,GAAGgI,eAAe,IAAIC,YAAY;QAE5C,IAAIjI,MAAM,EAAE;UACV,IAAI,CAACA,MAAM,CAACmI,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;YAClDpI,MAAM,CAACmI,SAAS,CAACX,GAAG,CAAC,kBAAkB,CAAC;UAC1C;UAEA,MAAMa,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACR,SAAS,CAACS,QAAQ,CAAC;UAClD,IAAIC,iBAAiB;UAErB,IAAIT,eAAe,EAAE;YACnBS,iBAAiB,GAAGJ,WAAW,CAACK,MAAM,CAACC,KAAK,IAC1C,CAACA,KAAK,CAACR,SAAS,CAACC,QAAQ,CAAC,kBAAkB,CAC9C,CAAC;UACH,CAAC,MAAM,IAAIH,YAAY,EAAE;YACvBQ,iBAAiB,GAAGJ,WAAW,CAACK,MAAM,CAACC,KAAK,IAC1C,CAACA,KAAK,CAACR,SAAS,CAACC,QAAQ,CAAC,eAAe,CAC3C,CAAC;UACH;UAEA,IAAIK,iBAAiB,IAAIA,iBAAiB,CAACjF,MAAM,GAAG,CAAC,EAAE;YACrD,MAAMoF,YAAY,GAAG7F,QAAQ,CAAC8F,aAAa,CAAC,KAAK,CAAC;YAClDD,YAAY,CAACE,SAAS,GAAG,mBAAmB;YAE5CL,iBAAiB,CAAChF,OAAO,CAACkF,KAAK,IAAI;cACjCC,YAAY,CAACG,WAAW,CAACJ,KAAK,CAAC;YACjC,CAAC,CAAC;YAEFZ,SAAS,CAACgB,WAAW,CAACH,YAAY,CAAC;UACrC;UAEAb,SAAS,CAAC5D,KAAK,CAACkB,OAAO,GAAG,OAAO;UACjC0C,SAAS,CAAC5D,KAAK,CAACC,KAAK,GAAG,MAAM;QAChC;MACF,CAAC,CAAC;;MAEF;MACA,MAAM4E,WAAW,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAYlC,aAAa,CAACmC,SAAS;AACnC;AACA;AACA;AACA,KAAK;MAED9H,OAAO,CAACC,GAAG,CAAC,yEAAyE,EAAE4H,WAAW,CAAC;;MAEnG;MACA,MAAM;QAAEE;MAAoB,CAAC,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC;MAClE,MAAMpI,QAAQ,GAAG,MAAMoI,mBAAmB,CAACF,WAAW,EAAE;QACtDG,MAAM,EAAE,IAAI;QACZC,WAAW,EAAE,UAAU;QACvBC,eAAe,EAAE,IAAI;QACrBzC,MAAM,EAAE;UACN3B,GAAG,EAAE,KAAK;UACVE,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,KAAK;UACbF,IAAI,EAAE;QACR;MACF,CAAC,CAAC;MAEF,IAAIpE,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAACsI,GAAG,EAAE;QACzC,MAAMC,QAAQ,GAAG,iBAAiB,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;QAC9E,MAAMC,eAAe,GAAG3M,qBAAqB,CAAC8D,QAAQ,CAACE,IAAI,CAACsI,GAAG,EAAEC,QAAQ,CAAC;QAE1E,IAAII,eAAe,EAAE;UACnBxK,iBAAiB,CAAC,8BAA8B,CAAC;UACjDF,cAAc,CAAC,IAAI,CAAC;QACtB,CAAC,MAAM;UACL,MAAM,IAAIoC,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAACP,QAAQ,CAACS,OAAO,IAAI,wBAAwB,CAAC;MAC/D;IAEF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CnC,iBAAiB,CAAC,2CAA2C,CAAC;MAC9DF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRM,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAEC;EACAvE,SAAS,CAAC,MAAM;IACdyH,kBAAkB,CAAC,CAAC;IACpBG,eAAe,CAAC,CAAC;IACjB/B,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACnD,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEjB,MAAMkM,qBAAqB,GAAGA,CAAA,KAAM;IAClC9J,QAAQ,CAAC,YAAYpC,SAAS,EAAE,EAAE;MAChCmM,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAU;IAChC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,KAAK;IACxD;IACA,IAAID,QAAQ,KAAK,UAAU,EAAE;MAC3B,MAAME,WAAW,GAAGzJ,mBAAmB,CAACsJ,OAAO,CAAC;MAChD,IAAIG,WAAW,EAAE;QACfD,KAAK,GAAG9D,IAAI,CAACxF,GAAG,CAACuJ,WAAW,CAACxJ,GAAG,EAAEyF,IAAI,CAACzF,GAAG,CAACwJ,WAAW,CAACvJ,GAAG,EAAEsJ,KAAK,CAAC,CAAC;MACrE;IACF;IAEA/L,mBAAmB,CAACiM,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAACJ,OAAO,GAAG;QACT,GAAGI,IAAI,CAACJ,OAAO,CAAC;QAChB,CAACC,QAAQ,GAAGC;MACd;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BpL,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMqL,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF7K,mBAAmB,CAAC,IAAI,CAAC;MAEzB,MAAM8K,OAAO,GAAG;QACdvI,QAAQ,EAAE9D;MACZ,CAAC;MAED,MAAM4C,QAAQ,GAAG,MAAM/D,aAAa,CAACyN,GAAG,CAAC,oBAAoB,EAAED,OAAO,CAAC;MAEvE,IAAIzJ,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1C;QACAgB,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAAClE,gBAAgB,CAAC,CAAC;;QAE1E;QACAG,kBAAkB,CAACH,gBAAgB,CAAC;QAEpCiB,iBAAiB,CAAC,mDAAmD,CAAC;QACtEF,cAAc,CAAC,IAAI,CAAC;QAEpBkC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEN,QAAQ,CAACE,IAAI,CAAC;MAC5D,CAAC,MAAM;QACL,MAAM,IAAIK,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDnC,iBAAiB,CAAC,4CAA4C,CAAC;MAC/DF,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,SAAS;MACRQ,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMgL,UAAU,GAAGA,CAAA,KAAM;IACvB9K,uBAAuB,CAAC,IAAI,CAAC;EAC/B,CAAC;EAED,MAAM+K,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC/K,uBAAuB,CAAC,KAAK,CAAC;IAC9B,MAAM2K,iBAAiB,CAAC,CAAC;EAC3B,CAAC;EAED,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhL,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,MAAMiL,oBAAoB,GAAGA,CAAA,KAAM;IACjC/K,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAMgL,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI;MACF;MACA,IAAIzM,eAAe,EAAE;QACnBD,mBAAmB,CAACC,eAAe,CAAC;QACpCiB,mBAAmB,CAAC,WAAW,CAAC;;QAEhC;QACA4C,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,SAAS,CAAChE,eAAe,CAAC,CAAC;QAEzEe,iBAAiB,CAAC,gDAAgD,CAAC;QACnEF,cAAc,CAAC,IAAI,CAAC;MACtB;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDnC,iBAAiB,CAAC,6CAA6C,CAAC;MAChEF,cAAc,CAAC,IAAI,CAAC;IACtB;IACAY,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;EAED,MAAMiL,iBAAiB,GAAGA,CAAA,KAAM;IAC9BjL,wBAAwB,CAAC,KAAK,CAAC;EACjC,CAAC;EAGD,MAAMkL,YAAY,GAAGA,CAAA,KAAM;IACzBnI,eAAe,CAAC,CAAC;IACjB/B,oBAAoB,CAAC,CAAC,CAAC,CAAC;IACxB1B,iBAAiB,CAAC,6BAA6B,CAAC;IAChDF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAID;EACA,MAAM+L,aAAa,GAAI9K,QAAQ,IAAK;IAClC,MAAM+K,OAAO,GAAG;MACd,SAAS,EAAE,KAAK;MAChB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,OAAO,CAAC/K,QAAQ,CAAC,IAAI,KAAK;EACnC,CAAC;EAED,MAAMgL,cAAc,GAAGA,CAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,OAAO;MAC5B7E,UAAU,EAAE,CAAAvI,gBAAgB,aAAhBA,gBAAgB,wBAAAiN,qBAAA,GAAhBjN,gBAAgB,CAAE8B,MAAM,cAAAmL,qBAAA,uBAAxBA,qBAAA,CAA0BlL,SAAS,KAAI,WAAW;MAC9DyG,UAAU,EAAEsE,aAAa,CAAC,CAAA9M,gBAAgB,aAAhBA,gBAAgB,wBAAAkN,sBAAA,GAAhBlN,gBAAgB,CAAE8B,MAAM,cAAAoL,sBAAA,uBAAxBA,sBAAA,CAA0BlL,QAAQ,KAAI,MAAM,CAAC;MACvEC,QAAQ,EAAE,GAAG,CAAAjC,gBAAgB,aAAhBA,gBAAgB,wBAAAmN,sBAAA,GAAhBnN,gBAAgB,CAAE8B,MAAM,cAAAqL,sBAAA,uBAAxBA,sBAAA,CAA0BlL,QAAQ,KAAI,EAAE,IAAI;MACzDC,KAAK,EAAE,CAAAlC,gBAAgB,aAAhBA,gBAAgB,wBAAAoN,sBAAA,GAAhBpN,gBAAgB,CAAE8B,MAAM,cAAAsL,sBAAA,uBAAxBA,sBAAA,CAA0BlL,KAAK,KAAI,SAAS;MACnDmL,YAAY,EAAE,aAAa;MAC3B3E,MAAM,EAAE;IACV,CAAC;EAAA,CAAC;EAEF,MAAM4E,eAAe,GAAGA,CAAA;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,OAAO;MAC7BnF,UAAU,EAAE,CAAAvI,gBAAgB,aAAhBA,gBAAgB,wBAAAuN,sBAAA,GAAhBvN,gBAAgB,CAAEmC,OAAO,cAAAoL,sBAAA,uBAAzBA,sBAAA,CAA2BxL,SAAS,KAAI,WAAW;MAC/DyG,UAAU,EAAEsE,aAAa,CAAC,CAAA9M,gBAAgB,aAAhBA,gBAAgB,wBAAAwN,sBAAA,GAAhBxN,gBAAgB,CAAEmC,OAAO,cAAAqL,sBAAA,uBAAzBA,sBAAA,CAA2BxL,QAAQ,KAAI,MAAM,CAAC;MACxEC,QAAQ,EAAE,GAAG,CAAAjC,gBAAgB,aAAhBA,gBAAgB,wBAAAyN,sBAAA,GAAhBzN,gBAAgB,CAAEmC,OAAO,cAAAsL,sBAAA,uBAAzBA,sBAAA,CAA2BxL,QAAQ,KAAI,EAAE,IAAI;MAC1DC,KAAK,EAAE,CAAAlC,gBAAgB,aAAhBA,gBAAgB,wBAAA0N,sBAAA,GAAhB1N,gBAAgB,CAAEmC,OAAO,cAAAuL,sBAAA,uBAAzBA,sBAAA,CAA2BxL,KAAK,KAAI;IAC7C,CAAC;EAAA,CAAC;EAEF,MAAMyL,eAAe,GAAGA,CAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,OAAO;MAC7BxF,UAAU,EAAE,CAAAvI,gBAAgB,aAAhBA,gBAAgB,wBAAA4N,qBAAA,GAAhB5N,gBAAgB,CAAEqC,OAAO,cAAAuL,qBAAA,uBAAzBA,qBAAA,CAA2B7L,SAAS,KAAI,WAAW;MAC/DyG,UAAU,EAAEsE,aAAa,CAAC,CAAA9M,gBAAgB,aAAhBA,gBAAgB,wBAAA6N,sBAAA,GAAhB7N,gBAAgB,CAAEqC,OAAO,cAAAwL,sBAAA,uBAAzBA,sBAAA,CAA2B7L,QAAQ,KAAI,SAAS,CAAC;MAC3EC,QAAQ,EAAE,GAAG,CAAAjC,gBAAgB,aAAhBA,gBAAgB,wBAAA8N,sBAAA,GAAhB9N,gBAAgB,CAAEqC,OAAO,cAAAyL,sBAAA,uBAAzBA,sBAAA,CAA2B7L,QAAQ,KAAI,EAAE,IAAI;MAC1DC,KAAK,EAAE,CAAAlC,gBAAgB,aAAhBA,gBAAgB,wBAAA+N,sBAAA,GAAhB/N,gBAAgB,CAAEqC,OAAO,cAAA0L,sBAAA,uBAAzBA,sBAAA,CAA2B7L,KAAK,KAAI,SAAS;MACpD8L,UAAU,EAAE,KAAK;MACjBtF,MAAM,EAAE;IACV,CAAC;EAAA,CAAC;EAEF,MAAMuF,kBAAkB,GAAGA,CAAA;IAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAAA,OAAO;MAChC9F,UAAU,EAAE,CAAAvI,gBAAgB,aAAhBA,gBAAgB,wBAAAkO,qBAAA,GAAhBlO,gBAAgB,CAAEoC,UAAU,cAAA8L,qBAAA,uBAA5BA,qBAAA,CAA8BnM,SAAS,KAAI,WAAW;MAClEyG,UAAU,EAAEsE,aAAa,CAAC,CAAA9M,gBAAgB,aAAhBA,gBAAgB,wBAAAmO,sBAAA,GAAhBnO,gBAAgB,CAAEoC,UAAU,cAAA+L,sBAAA,uBAA5BA,sBAAA,CAA8BnM,QAAQ,KAAI,MAAM,CAAC;MAC3EC,QAAQ,EAAE,GAAG,CAAAjC,gBAAgB,aAAhBA,gBAAgB,wBAAAoO,sBAAA,GAAhBpO,gBAAgB,CAAEoC,UAAU,cAAAgM,sBAAA,uBAA5BA,sBAAA,CAA8BnM,QAAQ,KAAI,EAAE,IAAI;MAC7DC,KAAK,EAAE,CAAAlC,gBAAgB,aAAhBA,gBAAgB,wBAAAqO,sBAAA,GAAhBrO,gBAAgB,CAAEoC,UAAU,cAAAiM,sBAAA,uBAA5BA,sBAAA,CAA8BnM,KAAK,KAAI,SAAS;MACvDoM,OAAO,EAAE;IACX,CAAC;EAAA,CAAC;;EAEF;EACA,IAAIlO,iBAAiB,IAAIM,wBAAwB,IAAI,CAACV,gBAAgB,EAAE;IACtE,oBACEf,OAAA;MAAK2L,SAAS,EAAC,2CAA2C;MAAAN,QAAA,eACxDrL,OAAA;QAAK2L,SAAS,EAAC,aAAa;QAAAN,QAAA,gBAC1BrL,OAAA,CAAChC,gBAAgB;UAACsR,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9B1P,OAAA;UAAK2L,SAAS,EAAC,4BAA4B;UAAAN,QAAA,EAAC;QAAmB;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1P,OAAA;IAAK2L,SAAS,EAAC,+BAA+B;IAAAN,QAAA,gBAE5CrL,OAAA,CAACnB,QAAQ;MACP8Q,IAAI,EAAE9N,WAAY;MAClB+N,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAE3C,kBAAmB;MAC5B4C,YAAY,EAAE;QAAEC,QAAQ,EAAE,KAAK;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA3E,QAAA,eAExDrL,OAAA,CAAClB,KAAK;QACJ+Q,OAAO,EAAE3C,kBAAmB;QAC5B+C,QAAQ,EAAEpP,SAAS,IAAIQ,aAAa,IAAIM,oBAAoB,GAAG,OAAO,GAAG,SAAU;QACnFuO,OAAO,EAAC,QAAQ;QAChBC,EAAE,EAAE;UACFC,eAAe,EAAEvP,SAAS,IAAIQ,aAAa,IAAIM,oBAAoB,GAAG,SAAS,GAAG,SAAS;UAC3F,kBAAkB,EAAE;YAClBsB,KAAK,EAAE;UACT;QACF,CAAE;QAAAoI,QAAA,EAEDtJ;MAAc;QAAAwN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX1P,OAAA,CAAC9B,MAAM;MACLyR,IAAI,EAAEpN,oBAAqB;MAC3BsN,OAAO,EAAErC,gBAAiB;MAC1B,mBAAgB,mBAAmB;MACnC,oBAAiB,yBAAyB;MAAAnC,QAAA,gBAE1CrL,OAAA,CAAC1B,WAAW;QAACkC,EAAE,EAAC,mBAAmB;QAAA6K,QAAA,EAAC;MAEpC;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACd1P,OAAA,CAAC5B,aAAa;QAAAiN,QAAA,eACZrL,OAAA,CAAC3B,iBAAiB;UAACmC,EAAE,EAAC,yBAAyB;UAAA6K,QAAA,EAAC;QAEhD;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB1P,OAAA,CAAC7B,aAAa;QAAAkN,QAAA,gBACZrL,OAAA,CAACzB,MAAM;UAAC8R,OAAO,EAAE7C,gBAAiB;UAACvK,KAAK,EAAC,SAAS;UAAAoI,QAAA,EAAC;QAEnD;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1P,OAAA,CAACzB,MAAM;UACL8R,OAAO,EAAE9C,iBAAkB;UAC3BtK,KAAK,EAAC,SAAS;UACfiN,OAAO,EAAC,WAAW;UACnBI,QAAQ,EAAEjO,gBAAiB;UAAAgJ,QAAA,EAE1BhJ,gBAAgB,gBACfrC,OAAA,CAAAE,SAAA;YAAAmL,QAAA,gBACErL,OAAA,CAAChC,gBAAgB;cAACsR,IAAI,EAAE,EAAG;cAACa,EAAE,EAAE;gBAAEI,EAAE,EAAE;cAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE/C;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT1P,OAAA,CAAC9B,MAAM;MACLyR,IAAI,EAAElN,qBAAsB;MAC5BoN,OAAO,EAAElC,iBAAkB;MAC3B,mBAAgB,oBAAoB;MACpC,oBAAiB,0BAA0B;MAAAtC,QAAA,gBAE3CrL,OAAA,CAAC1B,WAAW;QAACkC,EAAE,EAAC,oBAAoB;QAAA6K,QAAA,EAAC;MAErC;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACd1P,OAAA,CAAC5B,aAAa;QAAAiN,QAAA,eACZrL,OAAA,CAAC3B,iBAAiB;UAACmC,EAAE,EAAC,0BAA0B;UAAA6K,QAAA,EAAC;QAEjD;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB1P,OAAA,CAAC7B,aAAa;QAAAkN,QAAA,gBACZrL,OAAA,CAACzB,MAAM;UAAC8R,OAAO,EAAE1C,iBAAkB;UAAC1K,KAAK,EAAC,SAAS;UAAAoI,QAAA,EAAC;QAEpD;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1P,OAAA,CAACzB,MAAM;UAAC8R,OAAO,EAAE3C,kBAAmB;UAACzK,KAAK,EAAC,SAAS;UAACiN,OAAO,EAAC,WAAW;UAAA7E,QAAA,EAAC;QAEzE;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT1P,OAAA;MAAK2L,SAAS,EAAC,8EAA8E;MAAAN,QAAA,gBAC3FrL,OAAA;QAAK2L,SAAS,EAAC,6DAA6D;QAAAN,QAAA,gBAC1ErL,OAAA,CAACjC,OAAO;UAACyS,KAAK,EAAC,mBAAmB;UAACC,SAAS,EAAC,QAAQ;UAAApF,QAAA,eACnDrL,OAAA,CAAClC,UAAU;YACTuS,OAAO,EAAE5D,qBAAsB;YAC/B0D,EAAE,EAAE;cACFlN,KAAK,EAAE,iBAAiB;cACxBoM,OAAO,EAAE,KAAK;cACdqB,WAAW,EAAE,MAAM;cACnB,SAAS,EAAE;gBACTN,eAAe,EAAE;cACnB,CAAC;cACD,SAAS,EAAE;gBACTO,OAAO,EAAE;cACX,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAvF,QAAA,eAEFrL,OAAA,CAACtB,SAAS;cAACsE,QAAQ,EAAC;YAAQ;cAAAuM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACV1P,OAAA;UAAI2L,SAAS,EAAC,qCAAqC;UAAAN,QAAA,EAAC;QAAkB;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAEN1P,OAAA;QAAK2L,SAAS,EAAC,6CAA6C;QAAAN,QAAA,gBAE1DrL,OAAA;UAAAqL,QAAA,gBACErL,OAAA;YAAI2L,SAAS,EAAC,wCAAwC;YAAAN,QAAA,EAAC;UAAM;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAElE1P,OAAA;YAAK2L,SAAS,EAAC,MAAM;YAAAN,QAAA,gBACnBrL,OAAA;cAAO2L,SAAS,EAAC,kCAAkC;cAAAN,QAAA,EAAC;YAAU;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE1P,OAAA;cACE+M,KAAK,EAAEhM,gBAAgB,CAAC8B,MAAM,CAACC,SAAU;cACzC+N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,QAAQ,EAAE,WAAW,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;cAC5EpB,SAAS,EAAC,gHAAgH;cAAAN,QAAA,EAEzHhI,UAAU,CAAC2N,GAAG,CAACC,IAAI,iBAClBjR,OAAA;gBAAmB+M,KAAK,EAAEkE,IAAK;gBAAA5F,QAAA,EAAE4F;cAAI,GAAxBA,IAAI;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1P,OAAA;YAAK2L,SAAS,EAAC,6BAA6B;YAAAN,QAAA,gBAC1CrL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAAS;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrE1P,OAAA;gBACE+M,KAAK,EAAEhM,gBAAgB,CAAC8B,MAAM,CAACE,QAAS;gBACxC8N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,QAAQ,EAAE,UAAU,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;gBAC3EpB,SAAS,EAAC,gHAAgH;gBAAAN,QAAA,EAEzH/H,SAAS,CAAC0N,GAAG,CAACE,IAAI,iBACjBlR,OAAA;kBAAmB+M,KAAK,EAAEmE,IAAK;kBAAA7F,QAAA,EAAE6F;gBAAI,GAAxBA,IAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1P,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,GAAC,aACvC,EAAC9H,mBAAmB,CAACV,MAAM,CAACW,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACV,MAAM,CAACY,GAAG,EAAC,GAC9E;cAAA;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1P,OAAA;gBACEkR,IAAI,EAAC,QAAQ;gBACb1N,GAAG,EAAED,mBAAmB,CAACV,MAAM,CAACW,GAAI;gBACpCC,GAAG,EAAEF,mBAAmB,CAACV,MAAM,CAACY,GAAI;gBACpCsJ,KAAK,EAAEhM,gBAAgB,CAAC8B,MAAM,CAACG,QAAS;gBACxC6N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,QAAQ,EAAE,UAAU,EAAEuE,QAAQ,CAACL,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAC,CAAE;gBACrFpB,SAAS,EAAC;cAAgH;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1P,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAAK;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjE1P,OAAA;gBACEkR,IAAI,EAAC,OAAO;gBACZnE,KAAK,EAAEhM,gBAAgB,CAAC8B,MAAM,CAACI,KAAM;gBACrC4N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,QAAQ,EAAE,OAAO,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;gBACxEpB,SAAS,EAAC;cAA2D;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1P,OAAA;UAAAqL,QAAA,gBACErL,OAAA;YAAI2L,SAAS,EAAC,wCAAwC;YAAAN,QAAA,EAAC;UAAO;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEnE1P,OAAA;YAAK2L,SAAS,EAAC,MAAM;YAAAN,QAAA,gBACnBrL,OAAA;cAAO2L,SAAS,EAAC,kCAAkC;cAAAN,QAAA,EAAC;YAAU;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE1P,OAAA;cACE+M,KAAK,EAAEhM,gBAAgB,CAACmC,OAAO,CAACJ,SAAU;cAC1C+N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;cAC7EpB,SAAS,EAAC,gHAAgH;cAAAN,QAAA,EAEzHhI,UAAU,CAAC2N,GAAG,CAACC,IAAI,iBAClBjR,OAAA;gBAAmB+M,KAAK,EAAEkE,IAAK;gBAAA5F,QAAA,EAAE4F;cAAI,GAAxBA,IAAI;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1P,OAAA;YAAK2L,SAAS,EAAC,6BAA6B;YAAAN,QAAA,gBAC1CrL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAAS;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrE1P,OAAA;gBACE+M,KAAK,EAAEhM,gBAAgB,CAACmC,OAAO,CAACH,QAAS;gBACzC8N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;gBAC5EpB,SAAS,EAAC,gHAAgH;gBAAAN,QAAA,EAEzH/H,SAAS,CAAC0N,GAAG,CAACE,IAAI,iBACjBlR,OAAA;kBAAmB+M,KAAK,EAAEmE,IAAK;kBAAA7F,QAAA,EAAE6F;gBAAI,GAAxBA,IAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1P,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,GAAC,aACvC,EAAC9H,mBAAmB,CAACL,OAAO,CAACM,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACL,OAAO,CAACO,GAAG,EAAC,GAChF;cAAA;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1P,OAAA;gBACEkR,IAAI,EAAC,QAAQ;gBACb1N,GAAG,EAAED,mBAAmB,CAACL,OAAO,CAACM,GAAI;gBACrCC,GAAG,EAAEF,mBAAmB,CAACL,OAAO,CAACO,GAAI;gBACrCsJ,KAAK,EAAEhM,gBAAgB,CAACmC,OAAO,CAACF,QAAS;gBACzC6N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAEuE,QAAQ,CAACL,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAC,CAAE;gBACtFpB,SAAS,EAAC;cAAgH;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1P,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAAK;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjE1P,OAAA;gBACEkR,IAAI,EAAC,OAAO;gBACZnE,KAAK,EAAEhM,gBAAgB,CAACmC,OAAO,CAACD,KAAM;gBACtC4N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;gBACzEpB,SAAS,EAAC;cAA0D;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1P,OAAA;UAAAqL,QAAA,gBACErL,OAAA;YAAI2L,SAAS,EAAC,wCAAwC;YAAAN,QAAA,EAAC;UAAW;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEvE1P,OAAA;YAAK2L,SAAS,EAAC,MAAM;YAAAN,QAAA,gBACnBrL,OAAA;cAAO2L,SAAS,EAAC,kCAAkC;cAAAN,QAAA,EAAC;YAAU;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE1P,OAAA;cACE+M,KAAK,EAAEhM,gBAAgB,CAACoC,UAAU,CAACL,SAAU;cAC7C+N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,YAAY,EAAE,WAAW,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;cAChFpB,SAAS,EAAC,gHAAgH;cAAAN,QAAA,EAEzHhI,UAAU,CAAC2N,GAAG,CAACC,IAAI,iBAClBjR,OAAA;gBAAmB+M,KAAK,EAAEkE,IAAK;gBAAA5F,QAAA,EAAE4F;cAAI,GAAxBA,IAAI;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1P,OAAA;YAAK2L,SAAS,EAAC,6BAA6B;YAAAN,QAAA,gBAC1CrL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAAS;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrE1P,OAAA;gBACE+M,KAAK,EAAEhM,gBAAgB,CAACoC,UAAU,CAACJ,QAAS;gBAC5C8N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;gBAC/EpB,SAAS,EAAC,gHAAgH;gBAAAN,QAAA,EAEzH/H,SAAS,CAAC0N,GAAG,CAACE,IAAI,iBACjBlR,OAAA;kBAAmB+M,KAAK,EAAEmE,IAAK;kBAAA7F,QAAA,EAAE6F;gBAAI,GAAxBA,IAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1P,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,GAAC,aACvC,EAAC9H,mBAAmB,CAACJ,UAAU,CAACK,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACJ,UAAU,CAACM,GAAG,EAAC,GACtF;cAAA;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1P,OAAA;gBACEkR,IAAI,EAAC,QAAQ;gBACb1N,GAAG,EAAED,mBAAmB,CAACJ,UAAU,CAACK,GAAI;gBACxCC,GAAG,EAAEF,mBAAmB,CAACJ,UAAU,CAACM,GAAI;gBACxCsJ,KAAK,EAAEhM,gBAAgB,CAACoC,UAAU,CAACH,QAAS;gBAC5C6N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,YAAY,EAAE,UAAU,EAAEuE,QAAQ,CAACL,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAC,CAAE;gBACzFpB,SAAS,EAAC;cAAgH;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1P,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAAK;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjE1P,OAAA;gBACEkR,IAAI,EAAC,OAAO;gBACZnE,KAAK,EAAEhM,gBAAgB,CAACoC,UAAU,CAACF,KAAM;gBACzC4N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,YAAY,EAAE,OAAO,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;gBAC5EpB,SAAS,EAAC;cAA0D;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1P,OAAA;UAAAqL,QAAA,gBACErL,OAAA;YAAI2L,SAAS,EAAC,wCAAwC;YAAAN,QAAA,EAAC;UAAO;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEnE1P,OAAA;YAAK2L,SAAS,EAAC,MAAM;YAAAN,QAAA,gBACnBrL,OAAA;cAAO2L,SAAS,EAAC,kCAAkC;cAAAN,QAAA,EAAC;YAAU;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE1P,OAAA;cACE+M,KAAK,EAAEhM,gBAAgB,CAACqC,OAAO,CAACN,SAAU;cAC1C+N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,SAAS,EAAE,WAAW,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;cAC7EpB,SAAS,EAAC,gHAAgH;cAAAN,QAAA,EAEzHhI,UAAU,CAAC2N,GAAG,CAACC,IAAI,iBAClBjR,OAAA;gBAAmB+M,KAAK,EAAEkE,IAAK;gBAAA5F,QAAA,EAAE4F;cAAI,GAAxBA,IAAI;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEN1P,OAAA;YAAK2L,SAAS,EAAC,6BAA6B;YAAAN,QAAA,gBAC1CrL,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAAS;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrE1P,OAAA;gBACE+M,KAAK,EAAEhM,gBAAgB,CAACqC,OAAO,CAACL,QAAS;gBACzC8N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;gBAC5EpB,SAAS,EAAC,iHAAiH;gBAAAN,QAAA,EAE1H/H,SAAS,CAAC0N,GAAG,CAACE,IAAI,iBACjBlR,OAAA;kBAAmB+M,KAAK,EAAEmE,IAAK;kBAAA7F,QAAA,EAAE6F;gBAAI,GAAxBA,IAAI;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN1P,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,GAAC,aACvC,EAAC9H,mBAAmB,CAACH,OAAO,CAACI,GAAG,EAAC,GAAC,EAACD,mBAAmB,CAACH,OAAO,CAACK,GAAG,EAAC,GAChF;cAAA;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR1P,OAAA;gBACEkR,IAAI,EAAC,QAAQ;gBACb1N,GAAG,EAAED,mBAAmB,CAACH,OAAO,CAACI,GAAI;gBACrCC,GAAG,EAAEF,mBAAmB,CAACH,OAAO,CAACK,GAAI;gBACrCsJ,KAAK,EAAEhM,gBAAgB,CAACqC,OAAO,CAACJ,QAAS;gBACzC6N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,SAAS,EAAE,UAAU,EAAEuE,QAAQ,CAACL,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAC,CAAE;gBACtFpB,SAAS,EAAC;cAAgH;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3H,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN1P,OAAA;cAAAqL,QAAA,gBACErL,OAAA;gBAAO2L,SAAS,EAAC,kCAAkC;gBAAAN,QAAA,EAAC;cAAK;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjE1P,OAAA;gBACEkR,IAAI,EAAC,OAAO;gBACZnE,KAAK,EAAEhM,gBAAgB,CAACqC,OAAO,CAACH,KAAM;gBACtC4N,QAAQ,EAAGC,CAAC,IAAKlE,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAEkE,CAAC,CAACC,MAAM,CAAChE,KAAK,CAAE;gBACzEpB,SAAS,EAAC;cAA0D;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1P,OAAA;UAAK2L,SAAS,EAAC,+BAA+B;UAAAN,QAAA,eAC5CrL,OAAA;YAAK2L,SAAS,EAAC,qBAAqB;YAAAN,QAAA,gBAClCrL,OAAA;cACEqQ,OAAO,EAAE/C,UAAW;cACpBgD,QAAQ,EAAEjO,gBAAiB;cAC3BsJ,SAAS,EAAC,gRAAgR;cAAAN,QAAA,EAEzRhJ,gBAAgB,gBACfrC,OAAA,CAAAE,SAAA;gBAAAmL,QAAA,gBACErL,OAAA,CAAChC,gBAAgB;kBAACsR,IAAI,EAAE,EAAG;kBAACa,EAAE,EAAE;oBAAEI,EAAE,EAAE;kBAAE;gBAAE;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAE/C;cAAA,eAAE,CAAC,GAEH;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAET1P,OAAA;cACEqQ,OAAO,EAAE5C,oBAAqB;cAC9B9B,SAAS,EAAC,+NAA+N;cAAAN,QAAA,EAC1O;YAED;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1P,OAAA;MAAK2L,SAAS,EAAC,+BAA+B;MAAC3E,KAAK,EAAE;QAAEoK,UAAU,EAAE;MAAQ,CAAE;MAAA/F,QAAA,gBAE5ErL,OAAA;QAAK2L,SAAS,EAAC,2EAA2E;QACxF3E,KAAK,EAAE;UAAEe,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAqD,QAAA,eAErCrL,OAAA;UAAK2L,SAAS,EAAC,yCAAyC;UAAAN,QAAA,eACtDrL,OAAA,CAAC/B,GAAG;YAACkS,EAAE,EAAE;cAAEjI,OAAO,EAAE,MAAM;cAAEmJ,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAjG,QAAA,gBACzDrL,OAAA,CAACjC,OAAO;cAACyS,KAAK,EAAE7P,aAAa,GAAG,iBAAiB,GAAG,aAAc;cAAC8P,SAAS,EAAC,KAAK;cAAApF,QAAA,eAChFrL,OAAA;gBAAAqL,QAAA,eACErL,OAAA,CAAClC,UAAU;kBACTuS,OAAO,EAAEzC,YAAa;kBACtB0C,QAAQ,EAAE3P,aAAc;kBACxBwP,EAAE,EAAE;oBACFlN,KAAK,EAAEtC,aAAa,GAAG,0BAA0B,GAAG,iBAAiB;oBACrE,SAAS,EAAE;sBACTyP,eAAe,EAAE;oBACnB,CAAC;oBACD,SAAS,EAAE;sBACTO,OAAO,EAAE;oBACX,CAAC;oBACD,YAAY,EAAE;sBACZ1N,KAAK,EAAE,0BAA0B;sBACjCsO,MAAM,EAAE;oBACV,CAAC;oBACDX,UAAU,EAAE,UAAU;oBACtBvB,OAAO,EAAE;kBACX,CAAE;kBAAAhE,QAAA,EAED1K,aAAa,gBACZX,OAAA,CAAChC,gBAAgB;oBAACsR,IAAI,EAAE,EAAG;oBAACrM,KAAK,EAAC;kBAAS;oBAAAsM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE9C1P,OAAA,CAACvB,QAAQ;oBAACuE,QAAQ,EAAC;kBAAQ;oBAAAuM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAC9B;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEV1P,OAAA,CAACjC,OAAO;cAACyS,KAAK,EAAErO,eAAe,GAAG,mBAAmB,GAAG,cAAe;cAACsO,SAAS,EAAC,KAAK;cAAApF,QAAA,eACrFrL,OAAA;gBAAAqL,QAAA,eACErL,OAAA,CAAClC,UAAU;kBACTuS,OAAO,EAAE3K,iBAAkB;kBAC3B4K,QAAQ,EAAEnO,eAAe,IAAIxB,aAAc;kBAC3CwP,EAAE,EAAE;oBACFlN,KAAK,EAAGd,eAAe,IAAIxB,aAAa,GAAI,0BAA0B,GAAG,iBAAiB;oBAC1F,SAAS,EAAE;sBACTyP,eAAe,EAAE;oBACnB,CAAC;oBACD,SAAS,EAAE;sBACTO,OAAO,EAAE;oBACX,CAAC;oBACD,YAAY,EAAE;sBACZ1N,KAAK,EAAE,0BAA0B;sBACjCsO,MAAM,EAAE;oBACV,CAAC;oBACDX,UAAU,EAAE,UAAU;oBACtBvB,OAAO,EAAE;kBACX,CAAE;kBAAAhE,QAAA,EAEDlJ,eAAe,gBACdnC,OAAA,CAAChC,gBAAgB;oBAACsR,IAAI,EAAE,EAAG;oBAACrM,KAAK,EAAC;kBAAS;oBAAAsM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAE9C1P,OAAA,CAACpB,YAAY;oBAACoE,QAAQ,EAAC;kBAAQ;oBAAAuM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAClC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1P,OAAA;QACE2L,SAAS,EAAC,oCAAoC;QAC9C3E,KAAK,EAAE;UAAEwK,SAAS,EAAE,MAAM;UAAEvK,KAAK,EAAE;QAAO,CAAE;QAAAoE,QAAA,EAE3C1K,aAAa,gBACZX,OAAA;UAAK2L,SAAS,EAAC,uCAAuC;UAAAN,QAAA,eACpDrL,OAAA;YAAK2L,SAAS,EAAC,aAAa;YAAAN,QAAA,gBAC1BrL,OAAA,CAAChC,gBAAgB;cAACsR,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B1P,OAAA;cAAK2L,SAAS,EAAC,4BAA4B;cAAAN,QAAA,EAAC;YAAsB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GACJ7O,SAAS,gBACXb,OAAA;UAAK2L,SAAS,EAAC,uCAAuC;UAAAN,QAAA,eACpDrL,OAAA;YAAK2L,SAAS,EAAC,aAAa;YAAAN,QAAA,gBAC1BrL,OAAA;cAAK2L,SAAS,EAAC,2BAA2B;cAAAN,QAAA,EAAC;YAA0B;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3E1P,OAAA;cACEqQ,OAAO,EAAE5K,eAAgB;cACzBkG,SAAS,EAAC,mFAAmF;cAAAN,QAAA,EAC9F;YAED;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN1P,OAAA,CAAAE,SAAA;UAAAmL,QAAA,gBAEErL,OAAA,CAACF,kBAAkB;YAAAyP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEtB1P,OAAA,CAAChB,eAAe;YACdyS,gBAAgB,EAAEpD,eAAe,CAAC,CAAE;YACpCqD,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCiD,mBAAmB,EAAE3C,kBAAkB,CAAC;UAAE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACF1P,OAAA,CAACf,aAAa;YACZ2S,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC0D,gBAAgB,EAAEpD,eAAe,CAAC,CAAE;YACpCsD,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACF1P,OAAA,CAACd,mBAAmB;YAClB0S,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC4D,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCmD,UAAU,EAAEpR,UAAW;YACvBc,eAAe,EAAEA;UAAgB;YAAAgO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF1P,OAAA,CAACb,uBAAuB;YACtByS,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC4D,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCjO,UAAU,EAAEA,UAAW;YACvBc,eAAe,EAAEA;UAAgB;YAAAgO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACF1P,OAAA,CAACZ,8BAA8B;YAC7BwS,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC4D,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCoD,eAAe,EAAErR;UAAW;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACF1P,OAAA,CAACX,yBAAyB;YACxBuS,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC4D,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCqD,aAAa,EAAEtR;UAAW;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACF1P,OAAA,CAACV,0BAA0B;YACzBsS,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC4D,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCjO,UAAU,EAAEA;UAAW;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACF1P,OAAA,CAACT,0BAA0B;YACzBqS,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC4D,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCjO,UAAU,EAAEA;UAAW;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAExB,CAAC,eACF1P,OAAA,CAACR,sBAAsB;YACrBoS,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC4D,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCjO,UAAU,EAAEA;UAAW;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,eACF1P,OAAA,CAACP,qBAAqB;YACpBmS,eAAe,EAAE7D,cAAc,CAAC,CAAE;YAClC4D,mBAAmB,EAAE3C,kBAAkB,CAAC,CAAE;YAC1C0C,gBAAgB,EAAEhD,eAAe,CAAC,CAAE;YACpCjO,UAAU,EAAEA;UAAW;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAExB,CAAC;QAAA,eACF;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtP,EAAA,CAp1CID,4BAA4B;EAAA,QACTpB,eAAe,EAEvBY,SAAS,EA8BPD,WAAW;AAAA;AAAAsS,EAAA,GAjCxB7R,4BAA4B;AAs1ClC,eAAeA,4BAA4B;AAAC,IAAA6R,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}