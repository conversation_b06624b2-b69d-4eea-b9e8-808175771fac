import request from "supertest";
import { expect } from "chai";
import { app } from "../../src/index.js"; 
const baseUrl = `/api/v1/faqs`;

describe('GET ALL Faqs', () => {


    it('should retrieve FAQs successfully', async () => {
        const res = await request(app)
            .get(`${baseUrl}/all`)
            .expect(200);

        expect(res._body.success).to.be.true;
        expect(res._body.faqs).to.be.an('array');
    });

    it('should return an empty array if no FAQs are found', async () => {
        const res = await request(app)
            .get(`${baseUrl}/all`)

        expect(res._body.success).not.equal(false);
        expect(res._body.faqs).to.be.an('array');
    });
});
